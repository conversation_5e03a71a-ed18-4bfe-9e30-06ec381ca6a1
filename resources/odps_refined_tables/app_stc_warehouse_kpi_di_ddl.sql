CREATE TABLE IF NOT EXISTS app_stc_warehouse_kpi_di(
    `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
    `warehouse_no` BIGINT COMMENT '仓库编号，取值范围：2-155，唯一标识每个仓库',
    `warehouse_name` STRING COMMENT '仓库名称，如：华西总仓、长沙总仓、武汉总仓等',
    `check_sku_cnt` BIGINT COMMENT '抽检SKU数量，统计周期内抽检的商品数量，通常为0',
    `in_bound_sku_cnt` BIGINT COMMENT '入库SKU数量，统计周期内入库的商品数量，通常为0',
    `back_order_cnt` BIGINT COMMENT '退货订单总数，统计周期内发生的退货订单数量，取值范围：0-15',
    `finish_order_cnt` BIGINT COMMENT '已完成订单数，统计周期内完成的订单数量，取值范围：0-1',
    `damage_amt` DECIMAL(38,18) COMMENT '货损总金额，统计周期内发生的货损金额，单位：元',
    `damage_amt_wah` DECIMAL(38,18) COMMENT '仓配责任货损金额，统计周期内仓配责任导致的货损金额，单位：元',
    `sale_amt` DECIMAL(38,18) COMMENT '销售总金额，统计周期内的销售总额，单位：元',
    `after_sale_amt` DECIMAL(38,18) COMMENT '售后总金额，统计周期内发生的所有售后金额，单位：元',
    `after_sale_amt_wah` DECIMAL(38,18) COMMENT '仓配责任售后金额，统计周期内仓配责任导致的售后金额，单位：元',
    `after_sale_amt_pur` DECIMAL(38,18) COMMENT '采购责任售后金额，统计周期内采购责任导致的售后金额，单位：元',
    `after_sale_amt_che` DECIMAL(38,18) COMMENT '品控责任售后金额，统计周期内品控责任导致的售后金额，单位：元',
    `after_sale_amt_pur_che` DECIMAL(38,18) COMMENT '采购品控共同责任售后金额，统计周期内采购和品控共同责任导致的售后金额，单位：元',
    `after_sale_amt_oth` DECIMAL(38,18) COMMENT '其他责任售后金额，统计周期内其他责任导致的售后金额，单位：元',
    `delivery_total_amt` DECIMAL(38,18) COMMENT '配送销售金额，统计周期内配送相关的销售金额，单位：元',
    `coupon_amt` DECIMAL(38,18) COMMENT '优惠券金额，统计周期内使用的优惠券总额，单位：元',
    `origin_total_amt` DECIMAL(38,18) COMMENT '应付GMV，统计周期内应付的商品交易总额，单位：元',
    `real_total_amt` DECIMAL(38,18) COMMENT '实付GMV，统计周期内实际支付的商品交易总额，单位：元',
    `storage_amt` DECIMAL(38,18) COMMENT '仓储成本，统计周期内发生的仓储费用，单位：元',
    `arterial_roads_amt` DECIMAL(38,18) COMMENT '干线运输成本，统计周期内干线运输费用，单位：元',
    `deliver_amt` DECIMAL(38,18) COMMENT '配送成本，统计周期内配送费用，单位：元',
    `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提成本，统计周期内采购自提费用，通常为0，单位：元',
    `other_amt` DECIMAL(38,18) COMMENT '其他成本，统计周期内其他杂项成本，单位：元',
    `allocation_amt` DECIMAL(38,18) COMMENT '调拨成本，统计周期内调拨相关费用，单位：元'
)
COMMENT '仓配KPI库存数据表，包含仓库运营的各项关键绩效指标，包括销售、售后、成本等维度数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据加载日期')
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
    'comment'='仓配KPI库存数据表，用于监控和分析仓库运营的各项关键绩效指标')
LIFECYCLE 30;