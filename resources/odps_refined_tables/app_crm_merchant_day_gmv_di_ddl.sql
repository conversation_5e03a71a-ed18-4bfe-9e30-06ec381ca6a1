CREATE TABLE IF NOT EXISTS app_crm_merchant_day_gmv_di(
    cust_id BIGINT COMMENT '商户ID，唯一标识商户',
    cust_name STRING COMMENT '商户名称',
    city_id BIGINT COMMENT '商户所在运营区域ID',
    merchant_total_gmv DECIMAL(38,18) COMMENT '商户总GMV（总交易额）',
    distribution_gmv DECIMAL(38,18) COMMENT '配送GMV（配送业务交易额）',
    delivery_unit_price DECIMAL(38,18) COMMENT '配送客单价（平均每单配送金额）',
    distribution_amout BIGINT COMMENT '配送次数',
    fruit_gmv DECIMAL(38,18) COMMENT '鲜果类GMV',
    dairy_gmv DECIMAL(38,18) COMMENT '乳制品类GMV',
    non_dairy_gmv DECIMAL(38,18) COMMENT '非乳制品类GMV',
    brand_gmv DECIMAL(38,18) COMMENT '自营品牌GMV',
    reward_gmv DECIMAL(38,18) COMMENT '奖励SKU的GMV',
    core_merchant_tag BIGINT COMMENT '核心客户标记：0-否，1-是',
    bd_id BIGINT COMMENT 'BD编号，公海为0',
    bd_name STRING COMMENT 'BD名称',
    l1 DECIMAL(38,18) COMMENT 'L1指标',
    l2 DECIMAL(38,18) COMMENT 'L2指标',
    sku_num BIGINT COMMENT '本月累计下单商品种类数',
    spu_num BIGINT COMMENT '本月累计下单SPU种类数',
    thirty_days_order_spu BIGINT COMMENT '30天内下单SPU数量',
    thirty_sixty_days_order_spu BIGINT COMMENT '30-60天内下单SPU数量',
    seven_days_fruit_gmv DECIMAL(38,18) COMMENT '7天鲜果GMV',
    seven_days_dairy_gmv DECIMAL(38,18) COMMENT '7天乳制品GMV',
    seven_days_brand_gmv DECIMAL(38,18) COMMENT '7天自营品牌GMV',
    thirty_days_fruit_gmv DECIMAL(38,18) COMMENT '30天鲜果GMV',
    thirty_days_dairy_gmv DECIMAL(38,18) COMMENT '30天乳制品GMV',
    thirty_days_brand_gmv DECIMAL(38,18) COMMENT '30天自营品牌GMV',
    browsing_history STRING COMMENT '最近一次点击商品详情页的商品信息，JSON格式',
    agent_gmv DECIMAL(38,18) COMMENT '代售GMV',
    province STRING COMMENT '省份',
    city STRING COMMENT '城市',
    area STRING COMMENT '区域'
)
COMMENT '商户日度GMV统计表，包含商户基本信息、各类商品GMV、业务指标和地域信息'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期')
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
    'comment'='商户日度GMV明细表，用于商户交易分析和业务监控')
LIFECYCLE 30;