CREATE TABLE IF NOT EXISTS app_srm_supplier_consignment_warehouse_stock_detail_df(
	`supplier_id` BIGINT COMMENT '供应商ID',
	`supplier_name` STRING COMMENT '供应商名称',
	`warehouse_no` BIGINT COMMENT '仓库编号',
	`warehouse_name` STRING COMMENT '仓库名称',
	`purchase_no` STRING COMMENT '采购单号',
	`sku` STRING COMMENT 'SKU编码，商品唯一标识',
	`sku_sub_type` BIGINT COMMENT '商品二级性质：1-自营代销不入仓，2-自营代销入仓',
	`spu_title` STRING COMMENT '商品名称',
	`production_date` DATETIME COMMENT '生产日期，格式：年月日时分秒',
	`quality_date` DATETIME COMMENT '保质期到期日期，格式：年月日时分秒',
	`warn_days` BIGINT COMMENT '到期预警天数，取值范围：0-45天',
	`temporary_date` DATETIME COMMENT '临保日期，格式：年月日时分秒',
	`doc` DECIMAL(38,18) COMMENT '库存周转天数',
	`batch_stock_quantity` BIGINT COMMENT '批次库存量，单位：件',
	`batch_sales_14d` BIGINT COMMENT '近14天销量（出库口径），单位：件',
	`batch_risk_quantity` BIGINT COMMENT '预计临保风险件数，单位：件',
	`batch_risk_60d_flag` STRING COMMENT '临保风险时间标识：60天内-60天内风险，60天外-60天外风险',
	`date_flag` STRING COMMENT '同步时间标记，格式：yyyyMMdd'
)
COMMENT '供应商代销入仓库存明细表，记录供应商代销商品的入仓库存详细信息，包括库存量、销量、临保风险等数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：yyyyMMdd')
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='供应商代销入仓库存明细表，用于库存管理和风险预警分析')
LIFECYCLE 30;