CREATE TABLE IF NOT EXISTS app_stc_sku_temporary_damage_delivery_di(
	`date` STRING COMMENT '业务日期，格式为yyyyMMdd，表示数据发生的业务日期',
	`sku_id` STRING COMMENT 'SKU编号，商品库存单位的唯一标识',
	`spu_name` STRING COMMENT '商品名称，标准产品单元的名称',
	`temporary_store_cnt` BIGINT COMMENT '转入批次临保库存数量，取值范围：0-21',
	`temporary_store_amt` DECIMAL(38,18) COMMENT '转入批次临保库存金额，单位为元',
	`temporary_sale_cnt` BIGINT COMMENT 'SKU性质为临保的当日销售数量，取值范围：0-8',
	`temporary_sale_amt` DECIMAL(38,18) COMMENT 'SKU性质为临保的当日销售金额，单位为元',
	`temporary_damage_cnt` BIGINT COMMENT 'SKU性质为临保的当日货损数量，取值范围：0-10',
	`temporary_damage_amt` DECIMAL(38,18) COMMENT 'SKU性质为临保的当日货损金额，单位为元',
	`temporary_deliver_amt` DECIMAL(38,18) COMMENT 'SKU性质为临保的当日配送GMV（除批发），单位为元',
	`deliver_total_amt` DECIMAL(38,18) COMMENT '整体当日配送GMV（除批发），单位为元'
) 
COMMENT '临保品仓库数据信息表，记录临保商品的库存、销售、货损和配送等相关数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据加载的日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='临保品仓库数据信息表，包含临保商品的库存、销售、货损和配送等关键业务指标') 
LIFECYCLE 30;