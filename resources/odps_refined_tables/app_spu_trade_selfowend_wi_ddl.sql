CREATE TABLE IF NOT EXISTS app_spu_trade_selfowend_wi(
	year STRING COMMENT '年份，格式：YYYY',
	week_of_year BIGINT COMMENT '周数，取值范围：1-53',
	monday STRING COMMENT '周一日期，格式：YYYYMMDD（年月日）',
	sunday STRING COMMENT '周日日期，格式：YYYYMMDD（年月日）',
	cause_type STRING COMMENT '业务类型；枚举：鲜沐,SAAS',
	register_province STRING COMMENT '注册省份',
	register_city STRING COMMENT '注册城市',
	register_area STRING COMMENT '注册区域',
	spu_id STRING COMMENT 'SPU ID（商品ID）',
	spu_name STRING COMMENT 'SPU名称',
	cust_type STRING COMMENT '客户类型；枚举：面包蛋糕,西餐,咖啡,甜品冰淇淋,茶饮,其他',
	brand_type STRING COMMENT '大客户类型；枚举：大客户,普通,批发客户（历史枚举：单店,批发大客户,普通大客户,KA大客户 已弃用）',
	brand_name STRING COMMENT '品牌名称',
	category1 STRING COMMENT '一级类目',
	category2_id STRING COMMENT '二级类目ID',
	category2 STRING COMMENT '二级类目',
	category3_id STRING COMMENT '三级类目ID',
	category3 STRING COMMENT '三级类目',
	category4_id STRING COMMENT '四级类目ID',
	category4 STRING COMMENT '四级类目',
	origin_total_amt DECIMAL(38,18) COMMENT '实付总金额（元）',
	real_total_amt DECIMAL(38,18) COMMENT '应付总金额（元）',
	cust_cnt BIGINT COMMENT '客户数，取值范围：0-11',
	new_cust_cnt BIGINT COMMENT '（历史截止当天）当天新客户数，取值范围：0-3',
	order_time_cnt DECIMAL(38,18) COMMENT '客户下单时间间隔之和（天）',
	order_time_avg DECIMAL(38,18) COMMENT '平均下单时间间隔（天）',
	order_time_cnt_m DECIMAL(38,18) COMMENT '客户下单时间间隔之和（分钟）',
	order_time_avg_m DECIMAL(38,18) COMMENT '平均下单时间间隔（分钟）'
) 
COMMENT '自营品城市整体交易数据周表，按周统计自营商品在各城市的交易数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：YYYYMMDD（年月日）') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='自营品城市整体交易数据周表，包含商品交易、客户分布、时间间隔等维度的统计分析') 
LIFECYCLE 30;