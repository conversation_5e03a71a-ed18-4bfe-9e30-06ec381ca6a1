CREATE TABLE IF NOT EXISTS app_warehouse_category_supply_kpi_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd',
	`warehouse_no` BIGINT COMMENT '仓库编号，取值范围1-155',
	`warehouse_name` STRING COMMENT '仓库名称',
	`category` STRING COMMENT '商品类别：鲜果-新鲜水果类商品，标品-标准化包装商品',
	`sale_out_time` DECIMAL(38,18) COMMENT '售罄时长（小时）',
	`on_sale_time` DECIMAL(38,18) COMMENT '上架时长（小时）',
	`store_cost_amt` DECIMAL(38,18) COMMENT '期末库存成本（元）',
	`sale_amt` DECIMAL(38,18) COMMENT '销售出库成本（元）',
	`temporary_store_amt` DECIMAL(38,18) COMMENT '临保成本（元）',
	`damage_amt` DECIMAL(38,18) COMMENT '滞销过期货损出库成本（元）',
	`origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV（元）'
) 
COMMENT '供应链KPI指标表，包含各仓库不同商品类别的库存周转、销售成本等关键绩效指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='供应链KPI分析表，用于监控和优化供应链运营效率') 
LIFECYCLE 30;