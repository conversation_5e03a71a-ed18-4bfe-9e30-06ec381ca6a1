CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_stc_kpi_mi` (
  `month` STRING COMMENT '统计月份，格式为yyyyMM',
  `check_sku_cnt` BIGINT COMMENT '抽检商品数量',
  `in_bound_sku_cnt` BIGINT COMMENT '入库商品数量',
  `check_rate` DECIMAL(38,18) COMMENT '抽检比例，计算公式：抽检数量/入库数量',
  `back_order_cnt` BIGINT COMMENT '退货订单总数',
  `finish_order_cnt` BIGINT COMMENT '已完成订单数量',
  `back_finish_rate` DECIMAL(38,18) COMMENT '退货完结率，计算公式：已完成单数/退货总单数',
  `damage_amt` DECIMAL(38,18) COMMENT '货损总金额（元）',
  `damage_amt_wah` DECIMAL(38,18) COMMENT '仓配责任货损金额（元）',
  `sale_amt` DECIMAL(38,18) COMMENT '销售总金额（元）',
  `error_sku_cnt` BIGINT COMMENT '错误商品件数',
  `error_sku_cnt_wah` BIGINT COMMENT '仓配责任错误商品件数',
  `error_cust_cnt` BIGINT COMMENT '错误客户数量',
  `error_cust_cnt_wah` BIGINT COMMENT '仓配责任错误客户数量',
  `cust_cnt` BIGINT COMMENT '活跃客户数量',
  `sku_cnt` BIGINT COMMENT '配送商品件数',
  `total_point_cnt` BIGINT COMMENT '总配送点位数',
  `point_cnt` BIGINT COMMENT '配送点位数（不含喜茶）',
  `no_in_time_point_cnt` BIGINT COMMENT '配送不及时点位数（不含喜茶）',
  `delay_time_point_cnt_2` BIGINT COMMENT '前置2小时配送不及时点位数（不含喜茶）',
  `out_time` DECIMAL(38,18) COMMENT '配送超时时间（小时，不含喜茶）',
  `delay_time_2` DECIMAL(38,18) COMMENT '前置2小时配送超时时间（小时，不含喜茶）',
  `path_cnt` BIGINT COMMENT '配送线路数量（不含喜茶）',
  `delay_path_cnt` BIGINT COMMENT '出库不及时线路数量（不含喜茶）',
  `out_distance_point_cnt` BIGINT COMMENT '超距离配送点位数（含喜茶）',
  `after_sale_amt` DECIMAL(38,18) COMMENT '售后总金额（元）',
  `after_sale_amt_wah` DECIMAL(38,18) COMMENT '仓配责任售后金额（元）',
  `after_sale_amt_pur` DECIMAL(38,18) COMMENT '采购责任售后金额（元）',
  `after_sale_amt_che` DECIMAL(38,18) COMMENT '品控责任售后金额（元）',
  `after_sale_amt_pur_che` DECIMAL(38,18) COMMENT '采购品控共同责任售后金额（元）',
  `after_sale_amt_oth` DECIMAL(38,18) COMMENT '其他责任售后金额（元）',
  `delivery_total_amt` DECIMAL(38,18) COMMENT '配送销售总金额（元）',
  `coupon_amt` DECIMAL(38,18) COMMENT '优惠券金额（元）',
  `origin_total_amt` DECIMAL(38,18) COMMENT '应付GMV（元）',
  `real_total_amt` DECIMAL(38,18) COMMENT '实付GMV（元）',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储成本（元）',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '干线运输成本（元）',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送成本（元）',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提成本（元）',
  `other_amt` DECIMAL(38,18) COMMENT '其他运营成本（元）',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨成本（元）',
  `cost_amt` DECIMAL(38,18) COMMENT '商品成本（元）'
) 
COMMENT '仓配KPI汇总表，包含仓储配送相关的关键绩效指标数据，用于业务分析和监控'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
               'comment'='仓配KPI汇总表，按月统计仓储配送各项业务指标，包含成本、效率、质量等多维度数据')
LIFECYCLE 30;