CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_kpi_whole_delivery_wi` (
  `year` STRING COMMENT '年份，格式：YYYY',
  `week_of_year` BIGINT COMMENT '周数，取值范围：1-53',
  `monday` STRING COMMENT '周一日期，格式：YYYYMMDD（年月日）',
  `sunday` STRING COMMENT '周日日期，格式：YYYYMMDD（年月日）',
  `cust_group` STRING COMMENT '客户类型，取值范围：大客户、平台客户、批发客户、ALL',
  `sku_type` STRING COMMENT '商品类型，取值范围：自营、代仓、全品类、SAAS客户自营、ALL',
  `category` STRING COMMENT '商品类目，取值范围：鲜果、乳制品、其他、ALL',
  `dlv_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额（元）',
  `dlv_real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额（元）',
  `dlv_cost_amt` DECIMAL(38,18) COMMENT '履约总成本（元）',
  `dlv_origin_gross_profit` DECIMAL(38,18) COMMENT '履约应付毛利润（元）',
  `dlv_real_gross_profit` DECIMAL(38,18) COMMENT '履约实付毛利润（元）',
  `dlv_cust_cnt` BIGINT COMMENT '履约客户数，统计周期内完成履约的客户数量',
  `dlv_point_cnt` BIGINT COMMENT '履约点位数，统计周期内完成履约的配送点位数量',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费（元），含精准送、超时加单费，去除优惠券',
  `after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后金额（元）',
  `damage_amt` DECIMAL(38,18) COMMENT '货损金额（元）',
  `offine_delivey_amt` DECIMAL(38,18) COMMENT '履约费用（元）',
  `offine_no_delivey_amt` DECIMAL(38,18) COMMENT '非履约费用（元）',
  `timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送履约应付总金额（元）',
  `timing_real_total_amt` DECIMAL(38,18) COMMENT '省心送履约实付总金额（元）',
  `timing_cost_amt` DECIMAL(38,18) COMMENT '省心送履约总成本（元）'
)
COMMENT '履约KPI汇总表，包含各维度（客户类型、商品类型、商品类目）的履约关键绩效指标数据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：YYYYMMDD（年月日）'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '履约KPI汇总表，按周维度统计各业务线的履约绩效指标',
  'last_data_modified_time' = '2025-09-18 10:39:01'
)
LIFECYCLE 30;