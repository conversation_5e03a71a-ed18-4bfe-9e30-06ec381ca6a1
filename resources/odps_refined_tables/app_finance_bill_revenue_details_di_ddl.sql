CREATE TABLE IF NOT EXISTS app_finance_bill_revenue_details_di(
	order_no STRING COMMENT '订单编号',
	order_item_id BIGINT COMMENT '订单项编号',
	delivery_path_id BIGINT COMMENT 'delivery_path的ID',
	pay_type STRING COMMENT '支付方式，枚举值：线下支付等',
	service_area STRING COMMENT '配送仓大区，枚举值：西南、华东、福建等',
	province STRING COMMENT '省',
	city STRING COMMENT '市',
	m_id BIGINT COMMENT '客户ID',
	mname STRING COMMENT '客户名',
	realname STRING COMMENT '品牌的工商名称',
	name_remakes STRING COMMENT '品牌的品牌名称',
	sku STRING COMMENT 'SKU',
	pd_name STRING COMMENT '商品名',
	category1 STRING COMMENT '商品一级类目，枚举值：鲜果、乳制品等',
	tax_rate DECIMAL(38,18) COMMENT '税率',
	order_sku_cnt BIGINT COMMENT '订单商品数量',
	real_sku_cnt BIGINT COMMENT '实际送达商品数量（剔除缺货数量）',
	real_total_amt DECIMAL(38,18) COMMENT '订单实付总价',
	origin_total_amt DECIMAL(38,18) COMMENT '订单应付总价',
	total_discount_amt DECIMAL(38,18) COMMENT '营销金额',
	delivery_amt DECIMAL(38,18) COMMENT '运费',
	out_times_amt DECIMAL(38,18) COMMENT '超时加单金额',
	pay_time DATETIME COMMENT '支付日期时间，格式：年月日时分秒',
	finish_time DATETIME COMMENT '确认收入日期时间，格式：年月日时分秒',
	revenue_amt DECIMAL(38,18) COMMENT '确认收入金额（含税）',
	revenue_amt_notax DECIMAL(38,18) COMMENT '确认收入金额（不含税）',
	tax_amt DECIMAL(38,18) COMMENT '税额',
	unit_cost DECIMAL(38,18) COMMENT '成本单价（含税）',
	unit_cost_notax DECIMAL(38,18) COMMENT '成本单价（不含税）',
	cost DECIMAL(38,18) COMMENT '成本（含税）',
	cost_notax DECIMAL(38,18) COMMENT '成本（不含税）',
	delivery_coupon_amt DECIMAL(38,18) COMMENT '运费优惠金额',
	cust_team STRING COMMENT '客户团队类型，枚举值：平台客户、大客户',
	remark STRING COMMENT '订单备注',
	agent_sale_flag BIGINT COMMENT '代售商品标志，枚举值：0-是;1-否',
	sub_type BIGINT COMMENT '商品二级性质，枚举值：1-自营-代销不入仓、2-自营-代销入仓、3-自营-经销、4-代仓-代仓',
	order_status BIGINT COMMENT '订单状态，枚举值：2-待配送,3-待收货,6-已收货,8-已退款',
	precision_delivery_fee DECIMAL(38,18) COMMENT '精准送费用',
	settle_type STRING COMMENT '结算类型'
) 
COMMENT '财务口径账期收入明细表，记录财务账期内的收入明细数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，yyyyMMdd格式的年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='财务口径账期收入明细表，包含订单收入、成本、税额等财务相关明细数据') 
LIFECYCLE 30;