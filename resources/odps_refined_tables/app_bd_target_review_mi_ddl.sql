CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_bd_target_review_mi` (
  `months` STRING COMMENT '月份，格式为yyyyMM',
  `bd_id` BIGINT COMMENT '业绩归属的BD_ID',
  `bd_name` STRING COMMENT '业绩归属的销售姓名',
  `bd_m1` STRING COMMENT 'BD所属M1管理者姓名',
  `bd_m2` STRING COMMENT 'BD所属M2管理者姓名',
  `bd_m3` STRING COMMENT 'BD所属M3管理者姓名',
  `bd_work_zone` STRING COMMENT 'BD所在销售区域，多个区域用逗号分隔',
  `bd_work_city` STRING COMMENT 'BD所在城市',
  `team_tag` STRING COMMENT '销售团队标识',
  `same_m1_gmv_last_m3` DECIMAL(38,18) COMMENT '非ATGMV前3月达成值',
  `same_m1_gmv_last_m2` DECIMAL(38,18) COMMENT '非ATGMV前2月达成值',
  `same_m1_gmv_last_m1` DECIMAL(38,18) COMMENT '非ATGMV上月达成值',
  `same_m1_avg_gmv` DECIMAL(38,18) COMMENT '非ATGMV达成月均值',
  `no_at_gmv_target` DECIMAL(38,18) COMMENT '非ATGMV本月目标',
  `bd_gmv_increase_rate` DECIMAL(38,18) COMMENT 'BD非ATGMV目标增长率，百分比值',
  `m1_gmv_increase_rate` DECIMAL(38,18) COMMENT 'M1团队非ATGMV目标增长率，百分比值',
  `gmv_increase_rate_diff` DECIMAL(38,18) COMMENT 'BD和团队非ATGMV目标增长率偏离值，百分比值',
  `is_gmv_diff_more_10` STRING COMMENT '非ATGMV目标偏离值是否超过10%，枚举值：是/否',
  `same_m1_fruit_gmv_last_m3` DECIMAL(38,18) COMMENT '鲜果GMV前3月达成值_同城',
  `same_m1_fruit_gmv_last_m2` DECIMAL(38,18) COMMENT '鲜果GMV前2月达成值_同城',
  `same_m1_fruit_gmv_last_m1` DECIMAL(38,18) COMMENT '鲜果GMV上月达成值_同城',
  `same_m1_avg_fruit_gmv` DECIMAL(38,18) COMMENT '鲜果GMV前3月达成月均值_同城',
  `fruit_gmv_target` DECIMAL(38,18) COMMENT '鲜果GMV本月目标',
  `fruit_gmv_increase_rate` DECIMAL(38,18) COMMENT 'BD鲜果GMV目标增长率，百分比值',
  `m1_fruit_gmv_increase_rate` DECIMAL(38,18) COMMENT 'M1团队鲜果GMV目标增长率，百分比值',
  `fruit_gmv_increase_rate_diff` DECIMAL(38,18) COMMENT 'BD和团队鲜果GMV目标增长率偏离值，百分比值',
  `is_fruit_gmv_diff_more_10` STRING COMMENT '鲜果GMV目标偏离值是否超过10%，枚举值：是/否'
) 
COMMENT '销售目标偏差校对表，用于分析BD销售目标与实际达成情况的偏差'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，数据日期，格式为yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '销售目标偏差校对表，包含BD销售目标与实际达成情况的偏差分析数据',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;