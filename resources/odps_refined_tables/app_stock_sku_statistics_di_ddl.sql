CREATE TABLE IF NOT EXISTS app_stock_sku_statistics_di(
    `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
    `sku` STRING COMMENT '商品SKU编码，唯一标识一个商品',
    `warehouse_no` BIGINT COMMENT '仓库编号，取值范围2-174，表示商品所在的仓库',
    `quantity` BIGINT COMMENT '销售量，取值范围0-1025，表示商品的销售数量',
    `duration_rate_14d` DECIMAL(38,18) COMMENT '14天平均售罄率，取值范围0-1，表示商品在14天内的平均售罄比例',
    `turnover_rate_7d` DECIMAL(38,18) COMMENT '7天周转率，表示商品在7天内的周转效率，可能存在None值表示数据缺失'
)
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据的分区日期'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = '商品库存SKU统计明细表，记录每日各商品在各仓库的销售情况和周转指标'
)
LIFECYCLE 30;