CREATE TABLE IF NOT EXISTS app_crm_wecom_workers_m1_df(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
	`m1_name` STRING COMMENT '城市负责人名称（M1），销售主管级别的管理者姓名',
	`m2_name` STRING COMMENT '区域负责人名称（M2），销售经理级别的管理者姓名',
	`m3_name` STRING COMMENT '部门负责人名称（M3），销售总监级别的管理者姓名',
	`region` STRING COMMENT '大区名称，取值范围包括：华中大区、川渝大区、上海大区、苏皖大区等',
	`zz_bd_cnt` BIGINT COMMENT '在职销售数量，统计范围内在职的销售人员总数',
	`lz_bd_cnt` BIGINT COMMENT '离职销售数量，统计范围内离职的销售人员总数',
	`zz_jh_bd_cnt` BIGINT COMMENT '在职且激活企微销售数量，在职并且已激活企业微信的销售人员数量',
	`lz_jh_bd_cnt` BIGINT COMMENT '离职且激活企微销售数量，离职但仍然激活了企业微信的销售人员数量'
) 
COMMENT '销售企微激活状态看板，用于统计各层级管理者下属销售人员的在职离职状态和企业微信激活情况'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据加载的分区日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='销售组织架构和企微激活状态统计表，按M1-M3层级展示销售人员的在职离职和企微激活数据') 
LIFECYCLE 30;