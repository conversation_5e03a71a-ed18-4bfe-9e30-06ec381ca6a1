```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_sale_large_area_category_kpi_trade_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的业务日期',
  `large_area_name` STRING COMMENT '运营服务大区名称',
  `category` STRING COMMENT '品类：鲜果、乳制品、其他',
  `order_origin_total_amt` DECIMAL(38,18) COMMENT '交易应付总金额（元）',
  `order_real_total_amt` DECIMAL(38,18) COMMENT '交易实付总金额（元）',
  `order_cust_cnt` BIGINT COMMENT '交易客户数（去重客户数）',
  `order_cust_arpu` DECIMAL(38,18) COMMENT 'ARPU值（应付总金额/客户数），单位：元/客户',
  `order_cnt` BIGINT COMMENT '交易订单数',
  `delivery_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额（元）',
  `delivery_real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额（元）',
  `delivery_cust_cnt` BIGINT COMMENT '履约活跃客户数（去重客户数）',
  `delivery_origin_profit` DECIMAL(38,18) COMMENT '履约应付毛利润（元）',
  `delivery_real_profit` DECIMAL(38,18) COMMENT '履约实付毛利润（元）',
  `delivery_after_profit` DECIMAL(38,18) COMMENT '履约后毛利润（元）',
  `delivery_days_avg` DECIMAL(38,18) COMMENT '履约频次（平均履约天数）',
  `delivery_point_cnt` BIGINT COMMENT '履约点位数（去重点位数量）',
  `delivery_amt` DECIMAL(38,18) COMMENT '自营履约费用（元）',
  `new_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '新客履约应付总金额（元）',
  `new_delivery_real_total_amt` DECIMAL(38,18) COMMENT '新客履约实付总金额（元）',
  `new_delivery_cust_cnt` BIGINT COMMENT '新客履约活跃客户数（去重客户数）',
  `new_delivery_real_profit` DECIMAL(38,18) COMMENT '新客履约实付毛利润（元）',
  `old_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '老客履约应付总金额（元）',
  `old_delivery_real_total_amt` DECIMAL(38,18) COMMENT '老客履约实付总金额（元）',
  `old_delivery_cust_cnt` BIGINT COMMENT '老客履约活跃客户数（去重客户数）',
  `old_delivery_real_profit` DECIMAL(38,18) COMMENT '老客履约实付毛利润（元）',
  `order_sku_cnt` BIGINT COMMENT '交易SKU款数（去重SKU数量）',
  `order_sku_weight` DECIMAL(38,18) COMMENT '交易SKU重量（KG）',
  `delivery_sku_cnt` BIGINT COMMENT '履约SKU款数（去重SKU数量）',
  `delivery_sku_weight` DECIMAL(38,18) COMMENT '履约SKU重量（KG）'
) 
COMMENT '销售KPI指标汇总表，按大区和品类维度统计交易和履约相关指标，包含新老客分析'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据处理的日期'
)
STORED AS ALIORC  
TBLPROPERTIES (
  'comment'='销售KPI指标汇总表，包含交易金额、客户数、订单数、履约金额、毛利润、SKU数量等核心业务指标',
  'columnar.nested.type'='true'
) 
LIFECYCLE 30;
```