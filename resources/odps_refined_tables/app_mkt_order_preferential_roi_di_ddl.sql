CREATE TABLE IF NOT EXISTS app_mkt_order_preferential_roi_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示年月日',
	`city_id` BIGINT COMMENT '运营服务区ID，取值范围：1001-44269',
	`city_name` STRING COMMENT '运营服务区名称',
	`large_area_id` BIGINT COMMENT '运营服务大区ID，取值范围：1-91',
	`large_area_name` STRING COMMENT '运营服务大区名称',
	`cust_team` STRING COMMENT '客户团队类型，枚举值：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户',
	`life_cycle_detail` STRING COMMENT '生命周期标签（细），枚举值：S1、S2、B1、W等',
	`preferential_type` STRING COMMENT '营销活动类型，枚举值：临保活动、特价活动等',
	`preferential_amt` DECIMAL(38,18) COMMENT '营销金额，单位：元',
	`origin_total_amt` DECIMAL(38,18) COMMENT '订单应付金额，单位：元',
	`real_total_amt` DECIMAL(38,18) COMMENT '订单实付金额，单位：元'
) 
COMMENT '交易维度营销活动ROI拆解报表，用于分析营销活动的投入产出比'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='交易维度营销活动ROI拆解报表，包含营销活动类型、金额、客户团队等信息') 
LIFECYCLE 30;