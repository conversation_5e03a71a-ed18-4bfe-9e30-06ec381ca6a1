CREATE TABLE IF NOT EXISTS app_warehouse_category_supply_kpi_wi(
	`year` STRING COMMENT '年份，格式：YYYY',
	`week_of_year` BIGINT COMMENT '周数，取值范围：1-53',
	`monday` STRING COMMENT '周一日期，格式：YYYYMMDD',
	`sunday` STRING COMMENT '周日日期，格式：YYYYMMDD',
	`warehouse_no` BIGINT COMMENT '仓库编号，取值范围：1-155',
	`warehouse_name` STRING COMMENT '仓库名称',
	`category` STRING COMMENT '商品类别，枚举值：鲜果、标品',
	`sale_out_time` DECIMAL(38,18) COMMENT '售罄时长（小时）',
	`on_sale_time` DECIMAL(38,18) COMMENT '上架时长（小时）',
	`store_cost_amt` DECIMAL(38,18) COMMENT '期末库存成本（元）',
	`sale_amt` DECIMAL(38,18) COMMENT '销售出库成本（元）',
	`temporary_store_amt` DECIMAL(38,18) COMMENT '临保成本（元）',
	`damage_amt` DECIMAL(38,18) COMMENT '滞销过期货损出库成本（元）',
	`origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV（元）'
) 
COMMENT '供应链KPI指标表，包含各仓库不同商品类别的库存周转、销售成本等关键绩效指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='供应链KPI分析表，用于监控仓库运营效率和成本控制情况') 
LIFECYCLE 30;