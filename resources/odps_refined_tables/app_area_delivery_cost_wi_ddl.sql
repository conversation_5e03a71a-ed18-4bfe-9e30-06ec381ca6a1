CREATE TABLE IF NOT EXISTS app_area_delivery_cost_wi(
	friday STRING COMMENT '周五日期，格式为yyyyMMdd',
	saturday STRING COMMENT '周六日期，格式为yyyyMMdd',
	area_no BIGINT COMMENT '城配仓编号，取值范围：1-144',
	area_name STRING COMMENT '城配仓名称',
	service_area STRING COMMENT '服务区域：华北、华东等',
	area_type STRING COMMENT '城配仓类型：内区、外区',
	path_cnt BIGINT COMMENT '线路数量，取值范围：2-126',
	point_cnt BIGINT COMMENT '点位数，取值范围：16-2373',
	km_cnt DECIMAL(38,18) COMMENT '总公里数',
	delivery_amt DECIMAL(38,18) COMMENT '配送总费用',
	point_cnt_b1w BIGINT COMMENT '上周点位数，取值范围：13-3325',
	total_amt_b1w DECIMAL(38,18) COMMENT '上周总成本',
	delivery_total_amt DECIMAL(38,18) COMMENT '配送GVM（总交易额）'
) 
COMMENT '城配仓维度配送成本表，按城配仓统计配送相关的成本、线路、点位等指标'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='城配仓维度配送成本表，包含各城配仓的配送成本、线路数、点位数等关键指标') 
LIFECYCLE 30;