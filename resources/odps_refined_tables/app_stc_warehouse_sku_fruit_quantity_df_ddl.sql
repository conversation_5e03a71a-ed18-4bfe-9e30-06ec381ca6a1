CREATE TABLE IF NOT EXISTS app_stc_warehouse_sku_fruit_quantity_df(
	`warehouse_no` BIGINT COMMENT '库存仓号，数值型仓库编号',
	`warehouse_name` STRING COMMENT '库存仓名称',
	`sku_id` STRING COMMENT 'SKU编号，商品唯一标识',
	`on_sale` STRING COMMENT '上架状态：上架-正常销售，下架-暂停销售，异常-数据异常状态',
	`sku_type` STRING COMMENT 'SKU类型：自营-平台自营商品，代仓-第三方仓库代管，代售-第三方销售',
	`sku_disc` STRING COMMENT '商品规格描述',
	`spu_name` STRING COMMENT '商品名称',
	`category1` STRING COMMENT '一级类目',
	`category2` STRING COMMENT '二级类目',
	`category3` STRING COMMENT '三级类目',
	`sku_life` STRING COMMENT 'SKU生命周期状态：使用中-正常使用，已删除-已下架删除，None-无状态',
	`origin_palce` STRING COMMENT '商品产地',
	`is_domestic` STRING COMMENT '是否国产：是-国产商品，否-进口商品',
	`is_big_cust` STRING COMMENT '是否大客户专享：是-大客户专享商品，否-普通商品',
	`quality_time` BIGINT COMMENT '保质期天数',
	`store_batch_cnt` BIGINT COMMENT '在仓批次数',
	`first_batch_date` STRING COMMENT '最早批次日期，格式为yyyy-MM-dd HH:mm:ss，年月日时分秒',
	`first_batch_time` BIGINT COMMENT '最早批次库龄天数（与今日对比）',
	`last_batch_date` STRING COMMENT '最新批次日期，格式为yyyy-MM-dd HH:mm:ss，年月日时分秒',
	`last_batch_time` BIGINT COMMENT '最新批次库龄天数（与今日对比）',
	`store_quantity` BIGINT COMMENT '仓库库存数量',
	`lock_quantity` BIGINT COMMENT '锁定库存数量',
	`safe_quantity` BIGINT COMMENT '安全库存数量',
	`road_quantity` BIGINT COMMENT '在途库存数量',
	`uesed_quantity` BIGINT COMMENT '可用库存数量',
	`uesed_road_quantity` BIGINT COMMENT '可用及在途库存数量',
	`road_amt` DECIMAL(38,18) COMMENT '在途库存成本金额',
	`uesed_road_amt` DECIMAL(38,18) COMMENT '可用及在途成本金额',
	`unit_coat` DECIMAL(38,18) COMMENT '成本单价',
	`nearly_7_sale_cnt` BIGINT COMMENT '近7天出库量',
	`nearly_7_sale_avg` DECIMAL(38,18) COMMENT '近7天日均出库量',
	`nearly_7_sale_amt` DECIMAL(38,18) COMMENT '近7天出库成本',
	`nearly_7_store_amt` DECIMAL(38,18) COMMENT '近7天在库成本',
	`sku_turnover` DECIMAL(38,18) COMMENT '近7天周转天数',
	`nearly_7_sale_out_rask` DECIMAL(38,18) COMMENT '近7天售罄率',
	`nearly_3_sale_cnt` BIGINT COMMENT '近3天出库量',
	`nearly_3_sale_avg` DECIMAL(38,18) COMMENT '近3天日均出库量',
	`doc` DECIMAL(38,18) COMMENT '库存覆盖天数【(可用+在途)/近三日均出库】',
	`flag_tag` STRING COMMENT '库存打标：高售罄-销售良好，七天无动销-7天无销售，高周转-周转快，严重滞销-销售困难',
	`turnover_quantity` DECIMAL(38,18) COMMENT '周转库存数量',
	`emergency_quantity` DECIMAL(38,18) COMMENT '应急库存数量',
	`excess_quantity` DECIMAL(38,18) COMMENT '过剩库存数量',
	`risk_quantity` DECIMAL(38,18) COMMENT '风险库存数量'
) 
COMMENT '仓+SKU维度鲜果库存数据表，包含库存数量、成本、周转等核心指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='鲜果库存明细数据表，用于库存管理和分析') 
LIFECYCLE 30;