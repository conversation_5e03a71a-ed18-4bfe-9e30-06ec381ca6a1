CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_delivery_supplier_detail_di` (
  `date` STRING COMMENT '业务日期，格式为yyyyMMdd',
  `order_no` STRING COMMENT '订单编号',
  `deliver_time` DATETIME COMMENT '配送时间，格式为年月日时分秒',
  `sku_cnt` BIGINT COMMENT '商品数量',
  `real_total_amt` DECIMAL(38,18) COMMENT '实付金额',
  `sku_id` STRING COMMENT '商品SKU编码',
  `spu_id` BIGINT COMMENT '商品SPU ID，即pd_id',
  `spu_no` STRING COMMENT 'SPU编号',
  `spu_name` STRING COMMENT '商品名称',
  `sku_disc` STRING COMMENT 'SKU描述，即重量规格信息',
  `sku_type` STRING COMMENT '商品类型：自营、代仓、代售',
  `cust_id` BIGINT COMMENT '售后用户ID，即客户ID',
  `cust_name` STRING COMMENT '客户名称',
  `cust_type` STRING COMMENT '客户类型：大客户、普通',
  `cust_team` STRING COMMENT '客户团队类型：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户、批发',
  `brand_id` BIGINT COMMENT '品牌ID（原大客户ID），-1表示无品牌',
  `brand_name` STRING COMMENT '品牌的企业名称',
  `brand_alias` STRING COMMENT '品牌的品牌名称',
  `city_id` BIGINT COMMENT '运营服务ID',
  `city_name` STRING COMMENT '运营服务名称',
  `large_area_id` BIGINT COMMENT '运营服务大区ID',
  `large_area_name` STRING COMMENT '运营服务大区名称',
  `province` STRING COMMENT '注册省份',
  `purchase_no` STRING COMMENT '采购批次编号',
  `supplier` STRING COMMENT '批次供应商名称',
  `store_no` BIGINT COMMENT '配送仓编号',
  `store_name` STRING COMMENT '配送仓名称',
  `warehouse_no` BIGINT COMMENT '库存仓编号',
  `warehouse_name` STRING COMMENT '库存仓名称',
  `receipt_method` STRING COMMENT '收货方式'
)
COMMENT '配送有码数据表，记录配送订单的详细信息，包括商品信息、客户信息、配送信息等'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '配送有码数据表，用于存储配送订单的完整信息',
  'lifecycle' = '30'
);