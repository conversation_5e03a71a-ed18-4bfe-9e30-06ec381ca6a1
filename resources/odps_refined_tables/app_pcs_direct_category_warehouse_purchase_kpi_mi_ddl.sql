CREATE TABLE IF NOT EXISTS app_pcs_direct_category_warehouse_purchase_kpi_mi(
	`month` STRING COMMENT '日期，格式为yyyyMM，表示年月',
	`warehouse_no` BIGINT COMMENT '库存仓号，取值范围：1-155',
	`warehouse_name` STRING COMMENT '库存仓名称',
	`category4` STRING COMMENT '四级类目名称，如：柠檬、金桔、其他葡萄、枣、橘子等',
	`direct_purchase_amt` DECIMAL(38,18) COMMENT '直采金额',
	`purchases_amt` DECIMAL(38,18) COMMENT '采购金额（非直采）',
	`cost_flow_amt` DECIMAL(38,18) COMMENT '成本浮动金额',
	`direct_delivery_origin_amt` DECIMAL(38,18) COMMENT '直采履约应付金额',
	`direct_delivery_real_amt` DECIMAL(38,18) COMMENT '直采履约实付金额',
	`direct_delivery_market_amt` DECIMAL(38,18) COMMENT '直采营销费用',
	`direct_delivery_cost_amt` DECIMAL(38,18) COMMENT '直采成本费用',
	`direct_init_amt` DECIMAL(38,18) COMMENT '直采期末库存金额',
	`direct_after_sale_pcs_amt` DECIMAL(38,18) COMMENT '直采采购责售后金额',
	`direct_damage_pcs_amt` DECIMAL(38,18) COMMENT '直采采购责货损金额'
) 
COMMENT '直采KPI指标表，按仓库和类目维度统计直采相关的采购、履约、库存等财务指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，yyyyMMdd格式，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='直采业务KPI监控表，用于分析直采业务的财务表现和运营效率') 
LIFECYCLE 30;