```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_bd_mtd_comm_mi` (
  `last_bd_name` STRING COMMENT '最新归属BD姓名',
  `last_bd_id` STRING COMMENT 'BD ID',
  `dep_level3` STRING COMMENT '大区名称，枚举值包括：华中大区、闽桂大区、苏皖大区、华南大区等',
  `dep_name` STRING COMMENT '区域名称，枚举值包括：武汉、广西、苏州、深圳、福泉等',
  `total_score_num` DOUBLE COMMENT '利润积分，数值型，范围：0-9027.5',
  `bd_performance_rate` DOUBLE COMMENT '利润积分系数，数值型，范围：1.0-1.25',
  `total_comm_amt` DOUBLE COMMENT '佣金总额，数值型，范围：0-5389.89',
  `a_commisstion_amt` DOUBLE COMMENT '高价值客户总佣金，数值型，范围：0-3130.5',
  `a_cust_cnt` STRING COMMENT '高价值客户数，字符串型，范围：0-39',
  `a_cust_comm_amt` DOUBLE COMMENT '高价值客户数佣金，数值型，范围：0-1908',
  `more_than_spu_cnt` BIGINT COMMENT '高价值客户超额SPU数，整型，范围：0-660',
  `a_spu_comm_amt` DOUBLE COMMENT '高价值超额spu佣金，数值型，范围：0-990',
  `category_comm_amt` DOUBLE COMMENT '品类推广总佣金，数值型，范围：0-2406.15',
  `old_cust_comm` DOUBLE COMMENT '存量客户品类佣金，数值型，范围：0-2181.78',
  `new_cust_comm` DOUBLE COMMENT '新增客户品类佣金，数值型，范围：0-627.52',
  `big_sku_cnt` DOUBLE COMMENT '品类推广件数_大规格，数值型，范围：0-1877.57',
  `old_big_sku_cnt` DOUBLE COMMENT '存量客户推广件数_大规格，数值型，范围：0-1747.32',
  `new_big_sku_cnt` DOUBLE COMMENT '新增客户推广件数_大规格，数值型，范围：0-285.451',
  `dlv_real_amt` DOUBLE COMMENT 'MTD履约实付GMV，数值型，范围：0-585460',
  `item_profit_amt` DOUBLE COMMENT 'MTD履约商品毛利润，数值型，范围：0-71108.7',
  `dlv_spu_cnt` BIGINT COMMENT '履约SPU数，整型，范围：0-1188',
  `more_than_spu_cust` BIGINT COMMENT '高价值超额spu客户数，整型，范围：0-96',
  `score_target` BIGINT COMMENT '利润积分目标，整型，范围：0-17100',
  `arpu_comm_amt` DOUBLE COMMENT '高价值客户超ARPU佣金，数值型，范围：0-705'
) 
COMMENT 'MTD单销售绩效汇总表，包含BD月度累计的销售绩效数据，包括利润积分、佣金、GMV等关键指标'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，数据日期，格式：yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES ('comment'='MTD单销售绩效汇总表，用于BD月度绩效分析和佣金计算')
LIFECYCLE 720
```