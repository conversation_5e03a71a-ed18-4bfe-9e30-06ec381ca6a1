CREATE TABLE IF NOT EXISTS app_operate_kpi_trade_mi(
	month STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
	origin_total_amt DECIMAL(38,18) COMMENT '交易应付总金额（单位：元），即订单原始金额',
	real_total_amt DECIMAL(38,18) COMMENT '交易实付总金额（单位：元），即扣除优惠后的实际支付金额',
	cust_cnt BIGINT COMMENT '交易客户数，即完成交易的去重客户数量',
	cust_arpu DECIMAL(38,18) COMMENT 'ARPU值（单位：元），计算公式：应付总金额/客户数，表示每个客户的平均收入',
	order_cnt BIGINT COMMENT '交易订单数，即完成的订单总数',
	order_avg DECIMAL(38,18) COMMENT '订单均价（单位：元），计算公式：应付总金额/订单数，表示每个订单的平均金额',
	consign_origin_total_amt DECIMAL(38,18) COMMENT '代售应付总金额（单位：元），代售业务的订单原始金额',
	consign_real_total_amt DECIMAL(38,18) COMMENT '代售实付总金额（单位：元），代售业务的实际支付金额',
	consign_preferential_amt DECIMAL(38,18) COMMENT '代售营销金额（单位：元），代售业务的优惠金额',
	consign_cust_cnt BIGINT COMMENT '代售客户数，代售业务的去重客户数量',
	consign_order_cnt BIGINT COMMENT '代售订单数，代售业务的订单总数',
	register_cust_cnt BIGINT COMMENT '注册客户数，即新注册的用户数量',
	new_active_cust_cnt BIGINT COMMENT '有效拉新客户数，注册且首日下单金额>=15元的客户数量'
) 
PARTITIONED BY (ds STRING COMMENT '分区字段，yyyyMMdd格式日期，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='交易（订单）运营KPI指标汇总表，包含交易金额、客户数、订单数等核心运营指标，以及代售业务和注册拉新相关指标') 
LIFECYCLE 30;