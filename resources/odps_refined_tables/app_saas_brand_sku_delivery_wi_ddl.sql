CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_saas_brand_sku_delivery_wi` (
  `year` STRING COMMENT '年份，格式：YYYY',
  `week_of_year` BIGINT COMMENT '周数，取值范围：1-53',
  `monday` STRING COMMENT '周一日期，格式：YYYYMMDD，表示年月日',
  `sunday` STRING COMMENT '周日日期，格式：YYYYMMDD，表示年月日',
  `brand_alias` STRING COMMENT '品牌名称',
  `sku_id` STRING COMMENT '商品SKU ID',
  `title` STRING COMMENT '商品标题',
  `specification` STRING COMMENT '商品规格描述',
  `category1` STRING COMMENT '后台一级类目',
  `delivery_gmv` DECIMAL(38,18) COMMENT '履约GMV，商品交易总额',
  `cost_amt` DECIMAL(38,18) COMMENT '商品成本金额'
)
COMMENT 'SaaS利润数据表现表（仅为鲜沐自营数据），记录品牌商品维度的履约GMV和成本数据'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式：YYYYMMDD，表示年月日'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = 'SaaS利润数据表现表（仅为鲜沐自营数据），按天分区存储品牌商品维度的履约和成本数据',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;