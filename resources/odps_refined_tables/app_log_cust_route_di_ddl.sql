```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_log_cust_route_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据发生的年月日',
  `page_one` STRING COMMENT '用户访问路径中的第一个页面URL',
  `page_two` STRING COMMENT '用户访问路径中的第二个页面URL',
  `page_three` STRING COMMENT '用户访问路径中的第三个页面URL',
  `page_four` STRING COMMENT '用户访问路径中的第四个页面URL',
  `page_five` STRING COMMENT '用户访问路径中的第五个页面URL',
  `page_six` STRING COMMENT '用户访问路径中的第六个页面URL',
  `pv` BIGINT COMMENT '页面浏览量，取值范围：2-9482，表示该路径的总访问次数',
  `uv` BIGINT COMMENT '独立访客数，取值范围：1-376，表示该路径的独立用户访问数量'
)
COMMENT '用户路径行为数据表，记录用户在网站/app中的页面跳转路径和访问量指标'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示数据采集的年月日'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '用户路径分析数据表，用于分析用户行为路径和转化漏斗',
  'lifecycle' = '30'
);
```