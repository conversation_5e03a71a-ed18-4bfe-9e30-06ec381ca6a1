CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_log_search_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据发生的年月日',
  `search_name` STRING COMMENT '搜索词，用户输入的具体搜索内容',
  `search_cnt` BIGINT COMMENT '搜索次数，该搜索词被搜索的总次数',
  `search_cust_cnt` BIGINT COMMENT '搜索客户数，使用该搜索词进行搜索的独立客户数量'
)
COMMENT '搜索数据表，记录用户搜索行为的统计信息，包括搜索词、搜索次数和搜索用户数等指标'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示数据的分区日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '搜索行为数据表，用于分析用户搜索偏好和搜索热度趋势'
)
LIFECYCLE 30;