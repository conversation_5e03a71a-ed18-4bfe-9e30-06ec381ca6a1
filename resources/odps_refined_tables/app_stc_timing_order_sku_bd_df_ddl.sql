CREATE TABLE IF NOT EXISTS app_stc_timing_order_sku_bd_df(
	`order_date` STRING COMMENT '下单时间，格式为yyyyMMdd，表示年月日',
	`order_time_tag` STRING COMMENT '订单时间打标，取值范围：一个月内订单、二个月内订单、三个月内订单、超过三个月订单',
	`large_area_id` BIGINT COMMENT '运营服务大区ID，取值范围：1-91',
	`large_area_name` STRING COMMENT '运营服务大区名称',
	`city_id` BIGINT COMMENT '城市ID，取值范围：1001-44225',
	`city_name` STRING COMMENT '城市名称',
	`cust_id` BIGINT COMMENT '客户ID，取值范围：2-565494',
	`cust_name` STRING COMMENT '客户名称',
	`cust_phone` STRING COMMENT '客户电话',
	`bd_id` BIGINT COMMENT '销售ID，取值范围：-1-1180780，-1表示无销售',
	`bd_name` STRING COMMENT '销售名称，无销售时显示"无"',
	`m1_id` BIGINT COMMENT '销售主管ID（一级），取值范围：-1-1182080，-1表示无主管',
	`m1_name` STRING COMMENT '销售主管名称（一级），无主管时显示"无"',
	`warehouse_no` BIGINT COMMENT '库存仓号，取值范围：1-10，nan表示无仓号',
	`warehouse_name` STRING COMMENT '库存仓名称，无仓时显示"-"',
	`order_no` STRING COMMENT '订单编号',
	`sku_id` STRING COMMENT 'sku编号',
	`sku_type` STRING COMMENT 'sku类型，取值范围：自营、代仓、代售',
	`spu_name` STRING COMMENT '商品名称',
	`sku_disc` STRING COMMENT '商品描述',
	`total_sku_cnt` BIGINT COMMENT '下单数量，取值范围：1-2500',
	`origin_unit_amt` DECIMAL(38,18) COMMENT '应付单价',
	`sale_now_price` DECIMAL(38,18) COMMENT '最新单价',
	`diff_unit_amt` DECIMAL(38,18) COMMENT '单价差异（最新单价-实付均价）',
	`price_float` STRING COMMENT '价格浮动，取值范围：涨价、持平、降价',
	`timing_dlv_sku_cnt` BIGINT COMMENT '省心送已履约数量，取值范围：0-2500',
	`timing_no_dlv_sku_cnt` BIGINT COMMENT '省心送未履约数量，取值范围：-6-510',
	`timing_no_dlv_amt` DECIMAL(38,18) COMMENT '未配送部分差异金额',
	`timing_dlv_14_day_sku_cnt` BIGINT COMMENT '省心送未来14天预约配送数量，取值范围：0-9',
	`total_timing_dlv_14_day_sku_cnt` BIGINT COMMENT '省心送未来14天预约配送数量(仓+sku汇总)，取值范围：0-10',
	`store_quantity` BIGINT COMMENT '在仓数量，取值范围：0-2853',
	`road_quantity` BIGINT COMMENT '在途数量，取值范围：0-1',
	`estimated_sales` BIGINT COMMENT '预测销量，取值范围：0-612',
	`remind_replenishment` STRING COMMENT '补货提醒，取值范围：None、- 等'
) 
COMMENT '省心送补货预警表，用于监控省心送服务的补货需求和预警'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true') 
LIFECYCLE 30;