CREATE TABLE IF NOT EXISTS app_saas_brand_sku_trade_mi(
	month STRING COMMENT '月份，格式为yyyyMM',
	brand_alias STRING COMMENT '品牌名称',
	title STRING COMMENT '商品标题',
	specification STRING COMMENT '商品规格',
	category1 STRING COMMENT '后台一级类目',
	sku_type STRING COMMENT '商品类型：商城下单、鲜沐自营、代仓、代售、客户自营',
	total_gmv DECIMAL(38,18) COMMENT '总交易GMV',
	sku_cnt BIGINT COMMENT '商品销量'
) 
PARTITIONED BY (ds STRING COMMENT '分区字段，yyyyMMdd格式的日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='saas品牌品类结构数据表，包含品牌商品的交易和销售数据') 
LIFECYCLE 30;