CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_sku_log_view_mi` (
  `month` STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
  `sku_id` STRING COMMENT '商品SKU ID，唯一标识一个具体商品规格',
  `spu_name` STRING COMMENT '商品名称，即SPU名称，标识同一类商品',
  `view_key` STRING COMMENT '浏览名称，表示用户浏览的具体内容或品类',
  `pv` BIGINT COMMENT '月页面浏览量，统计周期内该浏览名称的访问次数，取值范围：≥100'
)
COMMENT '人群浏览行为偏好分析表，记录用户对不同商品和品类的浏览行为数据'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '人群浏览行为偏好分析表，用于分析用户对不同商品和品类的浏览偏好',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;