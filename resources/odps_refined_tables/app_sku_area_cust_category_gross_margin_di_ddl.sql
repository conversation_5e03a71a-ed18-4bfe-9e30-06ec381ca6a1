CREATE TABLE IF NOT EXISTS app_sku_area_cust_category_gross_margin_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据所属的业务日期',
	`large_area_name` STRING COMMENT '服务大区名称，如：广州大区、重庆大区等',
	`warehouse_name` STRING COMMENT '库存仓名称，如：东莞总仓、重庆总仓等',
	`cust_class` STRING COMMENT '客户大类，枚举值：大客户（茶百道）、大客户（非茶百道）、批发、普通（非品牌）、普通（品牌）',
	`sku_id` STRING COMMENT 'SKU编号，商品的唯一标识',
	`sku_type` STRING COMMENT 'SKU类型，枚举值：自营、代仓',
	`spu_name` STRING COMMENT '商品名称',
	`category_1` STRING COMMENT '一级类目，如：乳制品',
	`category_2` STRING COMMENT '二级类目，如：乳制品',
	`category_3` STRING COMMENT '三级类目，如：奶酪丨芝士、液体乳、炼乳、稀奶油等',
	`category_4` STRING COMMENT '四级类目，如：奶油奶酪、马斯卡彭、常温牛奶、鲜牛奶、罐装炼乳、搅打型稀奶油等',
	`origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额，原始订单金额',
	`real_total_amt` DECIMAL(38,18) COMMENT '实付总金额，实际支付金额',
	`cost_amt` DECIMAL(38,18) COMMENT '总成本，商品成本金额',
	`gross_margin` DECIMAL(38,18) COMMENT '毛利润，计算公式：实付总金额 - 总成本',
	`sku_cnt` BIGINT COMMENT '配送数量，整型数值，范围：0-1303',
	`origin_amt_befored` DECIMAL(38,18) COMMENT '昨日应付金额，前一天的应付总金额',
	`real_amt_befored` DECIMAL(38,18) COMMENT '昨日实付金额，前一天的实际支付金额',
	`cost_amt_befored` DECIMAL(38,18) COMMENT '昨日总成本，前一天的商品成本金额',
	`gross_margin_befored` DECIMAL(38,18) COMMENT '昨日毛利润，前一天的毛利润',
	`sku_cnt_befored` DECIMAL(38,18) COMMENT '昨日配送数量，前一天的配送数量',
	`origin_amt_beforew` DECIMAL(38,18) COMMENT '上周应付金额，上周同期的应付总金额',
	`real_amt_beforew` DECIMAL(38,18) COMMENT '上周实付金额，上周同期的实际支付金额',
	`cost_amt_beforew` DECIMAL(38,18) COMMENT '上周总成本，上周同期的商品成本金额',
	`gross_margin_beforew` DECIMAL(38,18) COMMENT '上周毛利润，上周同期的毛利润',
	`sku_cnt_beforew` DECIMAL(38,18) COMMENT '上周配送数量，上周同期的配送数量',
	`sku_disc` STRING COMMENT '商品描述，包含规格信息，如：1KG*1块、500g*6盒等'
) 
COMMENT '毛利数据SKU维度日表，按SKU、区域、客户类别统计的每日毛利数据，包含当日、昨日和上周同期的对比数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据加载日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='SKU维度毛利分析表，用于SKU级别的毛利分析和趋势对比') 
LIFECYCLE 30;