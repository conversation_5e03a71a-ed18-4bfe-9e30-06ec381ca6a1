CREATE TABLE IF NOT EXISTS app_crm_saas_order_cust_df_extra_h(
	order_time DATETIME COMMENT '下单时间，格式为年月日时分秒',
	pay_time DATETIME COMMENT '支付时间，格式为年月日时分秒',
	delivery_time DATETIME COMMENT '配送时间，格式为年月日时分秒',
	order_no STRING COMMENT '订单号，唯一标识一个订单',
	payable_price DECIMAL(38,18) COMMENT '应付价格，含18位小数精度',
	delivery_fee DECIMAL(38,18) COMMENT '配送费，含18位小数精度',
	order_address_contact_name STRING COMMENT '订单收货人姓名',
	order_address_contact_phone STRING COMMENT '收货人联系电话',
	order_address_province STRING COMMENT '订单收货地址-省份',
	order_address_city STRING COMMENT '订单收货地址-城市',
	order_address_area STRING COMMENT '订单收货地址-区县',
	order_address STRING COMMENT '详细收货地址',
	status BIGINT COMMENT '订单状态枚举：2-待配送，3-待收货，6-已收货，5-已收货（数据中实际出现）',
	status_text STRING COMMENT '状态文本描述',
	m_size STRING COMMENT '客户类型枚举：大客户等',
	province STRING COMMENT '商户所在省份',
	city STRING COMMENT '商户所在城市',
	district STRING COMMENT '商户所在区县',
	mname STRING COMMENT '商户名称',
	mcontact STRING COMMENT '商户联系人姓名',
	mphone STRING COMMENT '商户手机号',
	sub_account_contact STRING COMMENT '子账号联系人姓名',
	sub_account_phone STRING COMMENT '子账号手机号',
	bd_id BIGINT COMMENT 'BD ID，业务发展人员标识',
	bd_name STRING COMMENT 'BD名称，业务发展人员姓名'
) 
COMMENT 'SAAS鲜沐订单小时级数据表，包含订单基本信息、配送信息、商户信息和BD关联信息'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='鲜沐SAAS系统订单小时级明细表，用于订单分析和业务监控') 
LIFECYCLE 30;