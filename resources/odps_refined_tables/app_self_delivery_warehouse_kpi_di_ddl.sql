CREATE TABLE IF NOT EXISTS app_self_delivery_warehouse_kpi_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务日期',
	`origin_total_amt` DECIMAL(38,18) COMMENT '原始总金额：订单原始金额总和，单位元',
	`real_total_amt` DECIMAL(38,18) COMMENT '实际总金额：扣除优惠后的实际成交金额，单位元',
	`cost_amt` DECIMAL(38,18) COMMENT '成本：总成本金额，单位元',
	`timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送原始总金额：省心送业务的原始金额，单位元',
	`timing_real_total_amt` DECIMAL(38,18) COMMENT '省心送实际总金额：省心送业务的实际成交金额，单位元',
	`cust_cnt` BIGINT COMMENT '客户数：当日服务的客户数量',
	`order_cnt` BIGINT COMMENT '订单数：当日完成的订单数量',
	`point_cnt` BIGINT COMMENT '点位数：当日服务的配送点位数量',
	`day_point_cnt` BIGINT COMMENT '日均点位：当日平均点位数量',
	`sku_cnt` BIGINT COMMENT 'SKU数量：当日涉及的商品SKU种类数',
	`delivery_amt` DECIMAL(38,18) COMMENT '运费：配送运费总额，单位元',
	`after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后总金额：已完成收货的售后订单金额，单位元',
	`inventory_loss_amt` DECIMAL(38,18) COMMENT '盘亏总金额：库存盘点亏损金额，单位元',
	`inventory_profit_amt` DECIMAL(38,18) COMMENT '盘盈总金额：库存盘点盈余金额，单位元',
	`damage_amt` DECIMAL(38,18) COMMENT '货损总金额：货物损坏损失金额，单位元',
	`damage_rate` DECIMAL(38,18) COMMENT '货损占比：货损金额占总金额的比例，取值范围0-1',
	`storage_amt` DECIMAL(38,18) COMMENT '仓储成本：仓储相关费用，单位元',
	`arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本：干线运输费用，单位元',
	`deliver_amt` DECIMAL(38,18) COMMENT '配送成本：末端配送费用，单位元',
	`self_picked_amt` DECIMAL(38,18) COMMENT '采购自提成本：自提点相关成本，单位元',
	`other_amt` DECIMAL(38,18) COMMENT '其他成本：其他未分类成本，单位元',
	`allocation_amt` DECIMAL(38,18) COMMENT '调拨成本：库存调拨相关费用，单位元',
	`heytea_storage_amt` DECIMAL(38,18) COMMENT '喜茶仓储成本：喜茶业务仓储费用，单位元',
	`heytea_arterial_roads_amt` DECIMAL(38,18) COMMENT '喜茶干线成本：喜茶业务干线运输费用，单位元',
	`heytea_deliver_amt` DECIMAL(38,18) COMMENT '喜茶配送成本：喜茶业务末端配送费用，单位元',
	`heytea_way_deliver_amt` DECIMAL(38,18) COMMENT '喜茶专配成本：喜茶专线配送费用，单位元',
	`saas_point_cnt` BIGINT COMMENT 'SaaS点位数：使用SaaS系统的点位数量',
	`sample_point_cnt` BIGINT COMMENT '出样点位数：提供样品展示的点位数量',
	`after_sale_point_cnt` BIGINT COMMENT '补货点位数：需要进行补货的点位数量',
	`wholesale_point_cnt` BIGINT COMMENT '批发客户点位数：批发业务客户点位数量',
	`heytea_point_cnt` BIGINT COMMENT '喜茶共配点位数：喜茶共同配送点位数量',
	`heytea_way_point_cnt` BIGINT COMMENT '喜茶专配点位数：喜茶专线配送点位数量',
	`delivery_out_times_amt` DECIMAL(38,18) COMMENT '运费+超时加单费：运费和超时费用的总和，单位元',
	`deliver_coupon_amt` DECIMAL(38,18) COMMENT '优惠券费：配送优惠券补贴金额，单位元',
	`total_point_cnt` BIGINT COMMENT '总配送点位：所有配送点位的总和',
	`out_times_amt` DECIMAL(38,18) COMMENT '超时加单费：超时配送的额外费用，单位元',
	`precision_delivery_fee` DECIMAL(38,18) COMMENT '精准送费用：精准配送服务费用，单位元'
) 
COMMENT '履约口径KPI指标日汇总表(自营)，包含自营业务的各项履约相关关键绩效指标数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据分区日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='自营业务履约KPI指标日度汇总表，用于监控和分析自营配送业务的各项关键绩效指标') 
LIFECYCLE 30;