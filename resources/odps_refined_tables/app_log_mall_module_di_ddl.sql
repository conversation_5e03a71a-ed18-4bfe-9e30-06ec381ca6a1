CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_log_mall_module_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示年月日',
  `cust_team` STRING COMMENT '客户团队类型，取值范围：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户、批发',
  `module_name` STRING COMMENT '模块名称，如：meetings、个人中心等',
  `exposure_pv` BIGINT COMMENT '模块曝光PV（页面浏览量）',
  `exposure_uv` BIGINT COMMENT '模块曝光UV（独立访客数）',
  `click_pv` BIGINT COMMENT '模块点击PV（点击页面浏览量）',
  `click_uv` BIGINT COMMENT '模块点击UV（点击独立访客数）',
  `detail_click_pv` BIGINT COMMENT '模块详情页点击PV（详情页点击页面浏览量）',
  `detail_click_uv` BIGINT COMMENT '模块详情页点击UV（详情页点击独立访客数）',
  `handle_click_pv` BIGINT COMMENT '模块handle点击PV（处理按钮点击页面浏览量）',
  `handle_click_uv` BIGINT COMMENT '模块handle点击UV（处理按钮点击独立访客数）',
  `purchase_click_pv` BIGINT COMMENT '模块立即采购点击PV（立即采购按钮点击页面浏览量）',
  `purchase_click_uv` BIGINT COMMENT '模块立即采购点击UV（立即采购按钮点击独立访客数）',
  `cart_buy_pv` BIGINT COMMENT '模块加购PV（加入购物车页面浏览量）',
  `cart_buy_uv` BIGINT COMMENT '模块加购UV（加入购物车独立访客数）',
  `detail_cart_buy_pv` BIGINT COMMENT '模块详情页加购PV（详情页加入购物车页面浏览量）',
  `detail_cart_buy_uv` BIGINT COMMENT '模块详情页加购UV（详情页加入购物车独立访客数）',
  `handle_cart_buy_pv` BIGINT COMMENT '模块handle加购PV（处理按钮加入购物车页面浏览量）',
  `handle_cart_buy_uv` BIGINT COMMENT '模块handle加购UV（处理按钮加入购物车独立访客数）',
  `instant_buy_pv` BIGINT COMMENT '模块立即购买PV（立即购买按钮点击页面浏览量）',
  `instant_buy_uv` BIGINT COMMENT '模块立即购买UV（立即购买按钮点击独立访客数）',
  `detail_instant_buy_pv` BIGINT COMMENT '模块详情页立即购买PV（详情页立即购买按钮点击页面浏览量）',
  `detail_instant_buy_uv` BIGINT COMMENT '模块详情页立即购买UV（详情页立即购买按钮点击独立访客数）',
  `handle_instant_buy_pv` BIGINT COMMENT '模块handle立即购买PV（处理按钮立即购买页面浏览量）',
  `handle_instant_buy_uv` BIGINT COMMENT '模块handle立即购买UV（处理按钮立即购买独立访客数）',
  `expand_buy_pv` BIGINT COMMENT '模块拓展购买确认支付PV（拓展购买确认支付页面浏览量）',
  `expand_buy_uv` BIGINT COMMENT '模块拓展购买确认支付UV（拓展购买确认支付独立访客数）',
  `timing_buy_pv` BIGINT COMMENT '模块省心送确认支付PV（省心送确认支付页面浏览量）',
  `timing_buy_uv` BIGINT COMMENT '模块省心送确认支付UV（省心送确认支付独立访客数）',
  `order_order_cnt` BIGINT COMMENT '模块购买次数（订单数量）',
  `order_cust_cnt` BIGINT COMMENT '模块购买人数（购买用户数）'
)
COMMENT '商城SKU流量分析表，记录各模块的流量指标和转化数据'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示年月日'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '商城SKU流量分析表，用于分析各模块的流量表现和转化效果',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;