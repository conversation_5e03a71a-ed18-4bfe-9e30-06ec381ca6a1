```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_kpi_whole_trade_wi` (
  `year` STRING COMMENT '年份，格式：YYYY',
  `week_of_year` BIGINT COMMENT '周数，取值范围：1-53',
  `monday` STRING COMMENT '周一日期，格式：YYYYMMDD',
  `sunday` STRING COMMENT '周日日期，格式：YYYYMMDD',
  `cust_group` STRING COMMENT '客户类型，取值范围：大客户、平台客户、批发客户、ALL',
  `sku_type` STRING COMMENT '商品类型，取值范围：自营、代仓、全品类、SAAS客户自营、ALL',
  `category` STRING COMMENT '商品类目，取值范围：鲜果、乳制品、其他、ALL',
  `origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额，单位：元',
  `real_total_amt` DECIMAL(38,18) COMMENT '实付总金额，单位：元',
  `cust_cnt` BIGINT COMMENT '客户数，取值范围：1-19309',
  `order_cnt` BIGINT COMMENT '订单数，取值范围：1-29194',
  `timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送应付总金额，单位：元',
  `after_sale_noreceived_amt` DECIMAL(38,18) COMMENT '未到货售后总金额，单位：元'
)
COMMENT '交易KPI金额汇总表，包含各维度交易金额、客户数、订单数等关键指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：YYYYMMDD，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '交易KPI金额汇总表，按年周、客户类型、商品类型、商品类目等多维度聚合统计交易数据',
  'lifecycle' = '30'
);
```