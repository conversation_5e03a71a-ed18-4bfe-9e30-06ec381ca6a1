CREATE TABLE IF NOT EXISTS app_area_path_cost_di(
	delivery_date STRING COMMENT '配送日期，格式：yyyyMMdd',
	area_no BIGINT COMMENT '配送仓编号，取值范围：1-86',
	area_name STRING COMMENT '配送仓名称',
	path STRING COMMENT '线路编码，示例值：F、D、L、C',
	service_area STRING COMMENT '服务区域',
	carrier_name STRING COMMENT '承运商名称',
	car_id BIGINT COMMENT '车辆编号',
	driver STRING COMMENT '司机姓名',
	traffic_amt DECIMAL(38,18) COMMENT '打车费',
	purchase_amt DECIMAL(38,18) COMMENT '帮采费',
	extras_amt DECIMAL(38,18) COMMENT '杂费',
	extras_remark STRING COMMENT '杂费备注'
) 
COMMENT '路线维度配送成本表，记录各配送线路的成本明细数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，yyyyMMdd格式') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='路线维度配送成本表，包含配送日期、配送仓、线路、费用等详细信息') 
LIFECYCLE 30;