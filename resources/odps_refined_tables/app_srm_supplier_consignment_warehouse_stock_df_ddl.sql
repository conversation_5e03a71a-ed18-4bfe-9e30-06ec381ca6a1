```sql
CREATE TABLE IF NOT EXISTS app_srm_supplier_consignment_warehouse_stock_df(
    supplier_id BIGINT COMMENT '供应商ID，唯一标识供应商',
    supplier_name STRING COMMENT '供应商名称',
    warehouse_no BIGINT COMMENT '仓库编号，唯一标识仓库',
    warehouse_name STRING COMMENT '仓库名称',
    sku STRING COMMENT 'SKU编码，商品最小库存单位的唯一标识',
    spu_title STRING COMMENT '商品名称，标准产品单元的名称',
    sku_sub_type BIGINT COMMENT '商品二级性质：1-自营代销不入仓，2-自营代销入仓',
    stock_quantity BIGINT COMMENT '供应商库存量，当前库存数量',
    risk_quantity BIGINT COMMENT '临保风险量，临近保质期的库存数量',
    sales_14d DECIMAL(38,18) COMMENT '近14天销量均值（包含小规格销量），小数精度较高',
    date_flag STRING COMMENT '同步时间标记，格式为yyyyMMdd（年月日）'
)
COMMENT '供应商代销入仓库存表，记录供应商代销商品的入仓库存信息，包括库存量、临保风险量和销售数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd（年月日）')
STORED AS ALIORC
TBLPROPERTIES (
    'comment'='供应商代销入仓库存表，用于跟踪和管理供应商代销商品的库存状态',
    'lifecycle'='30'
);
```