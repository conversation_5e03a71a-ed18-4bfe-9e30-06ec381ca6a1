```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_log_mall_banner_di` (
  `date` STRING COMMENT '日期，格式：yyyyMMdd，表示年月日',
  `cust_team` STRING COMMENT '客户团队类型，枚举值：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户、批发',
  `cust_type` STRING COMMENT '客户行业类型，枚举值：面包蛋糕、西餐、咖啡、甜品冰淇淋、茶饮、其他',
  `life_cycle` STRING COMMENT '生命周期标签（粗），枚举值：稳定期、成长期等',
  `life_cycle_detail` STRING COMMENT '生命周期标签（细），枚举值：S1、A1等',
  `register_province` STRING COMMENT '注册时省份名称',
  `register_city` STRING COMMENT '注册时城市名称',
  `register_area` STRING COMMENT '注册时区县名称',
  `city_id` BIGINT COMMENT '运营服务区ID，数值型标识',
  `city_name` STRING COMMENT '运营服务区名称',
  `large_area_id` BIGINT COMMENT '运营服务大区ID，数值型标识',
  `large_area_name` STRING COMMENT '运营服务大区名称',
  `banner_id` STRING COMMENT 'banner页ID，唯一标识',
  `banner_name` STRING COMMENT 'banner页名称',
  `enter_pv` BIGINT COMMENT '进入页面浏览量（Page View）',
  `enter_uv` BIGINT COMMENT '进入页面独立访客数（Unique Visitor）',
  `sku_click_pv` BIGINT COMMENT '商品点击浏览量',
  `sku_click_uv` BIGINT COMMENT '商品点击独立访客数',
  `receive_click_pv` BIGINT COMMENT '立即领取按钮点击浏览量',
  `receive_click_uv` BIGINT COMMENT '立即领取按钮点击独立访客数',
  `sku_purchase_click_pv` BIGINT COMMENT '商品立即采购按钮点击浏览量',
  `sku_purchase_click_uv` BIGINT COMMENT '商品立即采购按钮点击独立访客数',
  `sku_cart_buy_pv` BIGINT COMMENT '商品加入购物车浏览量',
  `sku_cart_buy_uv` BIGINT COMMENT '商品加入购物车独立访客数',
  `sku_instant_buy_pv` BIGINT COMMENT '商品立即购买按钮点击浏览量',
  `sku_instant_buy_uv` BIGINT COMMENT '商品立即购买按钮点击独立访客数',
  `sku_order_order_cnt` BIGINT COMMENT '商品购买订单次数',
  `sku_order_cust_cnt` BIGINT COMMENT '商品购买独立客户数'
) 
COMMENT '商城banner页流量分析表，用于分析不同banner页面的用户行为数据，包括页面访问、商品点击、购买转化等核心指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：yyyyMMdd，表示年月日'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '商城banner页流量分析表',
  'lifecycle' = '30'
)
```