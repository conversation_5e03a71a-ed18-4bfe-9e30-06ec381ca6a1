```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_crm_sku_performance_wi` (
  `year` STRING COMMENT '年份，格式：YYYY',
  `week_of_year` BIGINT COMMENT '周数，取值范围：1-53',
  `monday` STRING COMMENT '周一日期，格式：YYYYMMDD（年月日）',
  `sunday` STRING COMMENT '周日日期，格式：YYYYMMDD（年月日）',
  `sku_id` STRING COMMENT '商品SKU ID',
  `spu_name` STRING COMMENT '商品名称',
  `sku_spec` STRING COMMENT '商品规格描述',
  `sku_category` STRING COMMENT '商品类目，枚举：自营品牌、鲜果、乳制品、非乳制品',
  `category4` STRING COMMENT '四级分类名称',
  `administrative_city` STRING COMMENT '商家对应的行政城市名称',
  `is_simple` STRING COMMENT '是否单店，枚举：是、否',
  `sku_cnt` BIGINT COMMENT '商品数量，统计周期内销售的商品件数',
  `real_gmv_amt` DECIMAL(38,18) COMMENT '订单实付金额，统计周期内用户实际支付的金额',
  `origin_gmv_amt` DECIMAL(38,18) COMMENT '订单应付金额，统计周期内订单原始金额',
  `deliver_gmv_amt` DECIMAL(38,18) COMMENT '履约实付金额，统计周期内履约相关的实际支付金额',
  `deliver_cost` DECIMAL(38,18) COMMENT '履约成本，统计周期内履约相关的成本支出',
  `order_cnt` BIGINT COMMENT '订单数，统计周期内产生的订单数量',
  `cust_cnt` BIGINT COMMENT '订单客户数，统计周期内下单的客户数量',
  `m1` STRING COMMENT '城市负责人（M1）姓名',
  `m2` STRING COMMENT '区域负责人（M2）姓名',
  `m3` STRING COMMENT '部门负责人（M3）姓名',
  `cust_type` STRING COMMENT '客户类型，枚举：面包蛋糕、西餐、咖啡、甜品冰淇淋、茶饮、其他'
)
COMMENT 'SKU粒度平台销售业绩报表周汇总表，按周统计商品销售业绩数据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：YYYYMMDD（年月日），表示数据统计日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = 'SKU粒度平台销售业绩报表周汇总表，包含商品销售相关的业绩指标和维度信息',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```