```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_stc_warehouse_sku_board_position_cost_di` (
  `date` STRING COMMENT '日期，格式：yyyyMMdd',
  `warehouse_no` BIGINT COMMENT '库存仓ID，取值范围：2-69',
  `warehouse_name` STRING COMMENT '库存仓名称',
  `batch_no` STRING COMMENT '批次编号',
  `sku_id` STRING COMMENT 'SKU编号',
  `sku_type` STRING COMMENT 'SKU类型：自营、代仓、代售',
  `spu_id` BIGINT COMMENT 'SPU ID，取值范围：7-18910',
  `spu_no` STRING COMMENT 'SPU编号',
  `spu_name` STRING COMMENT 'SPU名称',
  `sku_disc` STRING COMMENT 'SKU描述信息',
  `category` STRING COMMENT '一级类目',
  `storage_way` STRING COMMENT '存储方式：冷冻、冷藏、常温等',
  `pack_unit` STRING COMMENT '包装单位：箱、包、盒、袋、瓶等',
  `production_date` DATETIME COMMENT '生产日期，格式：年月日时分秒',
  `quality_date` DATETIME COMMENT '保质期，格式：年月日时分秒',
  `init_quantity` BIGINT COMMENT '期初库存数量，取值范围：1-58000',
  `warehouse_sku_quantity` BIGINT COMMENT '仓库库存数量，取值范围：1-58000',
  `storage_type` STRING COMMENT '储存类别：整件、拆包',
  `storage_num` BIGINT COMMENT '箱入数，取值范围：0-8000',
  `layer_height` BIGINT COMMENT '层高，取值范围：0-600',
  `layer_total` BIGINT COMMENT '层码放数量，取值范围：0-24000',
  `layer_cnt` BIGINT COMMENT '单板码放数量，取值范围：0-720000',
  `layer_cnt_up` DECIMAL(38,18) COMMENT '板位数（原始）',
  `board_position_fee_cnt` DECIMAL(38,18) COMMENT '板位数（计费）',
  `board_position_warehouse_cnt` DECIMAL(38,18) COMMENT '板位数（仓维汇总）',
  `board_position_temperature_cnt` DECIMAL(38,18) COMMENT '板位数（仓温维汇总）',
  `warehouse_layer_tempature_amt` DECIMAL(38,18) COMMENT '板位费（板位费-板位数（仓温维汇总））',
  `board_position_unit` DECIMAL(38,18) COMMENT '板位价格'
)
COMMENT '板位费汇总数据表，记录仓库SKU板位费用相关信息，包括库存信息、存储方式、板位数量及费用计算等'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '板位费汇总数据表，用于仓库SKU板位费用的统计和分析',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```