CREATE TABLE IF NOT EXISTS app_saas_brand_sku_delivery_mi(
	month STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
	brand_alias STRING COMMENT '品牌名称，枚举值包括：一只酸奶牛、赵记鲜果、柠季、榴莲嘟嘟、蔡小甜等31个品牌',
	sku_id STRING COMMENT '商品SKU ID，唯一标识商品',
	title STRING COMMENT '商品标题',
	specification STRING COMMENT '商品规格描述',
	category1 STRING COMMENT '后台一级类目，枚举值包括：新鲜水果、新鲜蔬菜等18个类目',
	delivery_gmv DECIMAL(38,18) COMMENT '履约GMV，商品交付总金额',
	cost_amt DECIMAL(38,18) COMMENT '商品成本金额'
) 
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='SaaS利润数据表现表，包含鲜沐自营商品的销售和成本数据，用于利润分析') 
LIFECYCLE 30;