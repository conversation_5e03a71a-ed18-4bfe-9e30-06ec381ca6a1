CREATE TABLE IF NOT EXISTS app_commonly_recommended_df(
	`cust_id` BIGINT COMMENT '客户ID，数值型标识，取值范围：6-18762',
	`sku_id` STRING COMMENT '商品SKU，字符串型商品唯一标识',
	`score` DECIMAL(38,18) COMMENT '商品排序分值，小数型评分，范围：0.9-2.7',
	`purchases` BIGINT COMMENT '一年内购买次数，数值型购买频次，取值范围：0-299，均值：4.4，中位数：1'
) 
COMMENT '常用推荐离线商品表，包含客户对商品的购买行为和推荐评分数据'
PARTITIONED BY (
	`ds` STRING NOT NULL COMMENT '分区字段，yyyyMMdd格式日期，表示数据日期'
)
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='常用推荐离线商品表，用于存储客户商品推荐相关数据') 
LIFECYCLE 100;