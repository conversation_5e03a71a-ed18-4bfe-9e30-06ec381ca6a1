CREATE TABLE IF NOT EXISTS app_saas_market_item_no_sale_detail_di(
	`tenant_id` BIGINT COMMENT '租户ID，取值范围：2-123',
	`time_tag` STRING COMMENT '时间标签，格式：yyyyMMdd（年月日）',
	`type` BIGINT COMMENT '滞销类型：1-7日滞销，2-30日滞销',
	`item_id` BIGINT COMMENT '商品ID，取值范围：2-44901',
	`sale_price` DECIMAL(38,18) COMMENT '商品售价，支持18位小数精度'
)
COMMENT 'SaaS商品滞销明细表，记录各租户商品滞销情况，包含7日和30日滞销两种类型'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：yyyyMMdd（年月日）')
STORED AS ALIORC
TBLPROPERTIES ('comment'='SaaS商品滞销明细分析表，用于商品滞销监控和运营分析')
LIFECYCLE 30;