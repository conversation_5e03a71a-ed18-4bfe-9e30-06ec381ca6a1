```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_log_area_cust_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
  `large_area_no` STRING COMMENT '运营大区编号，如：2、5、24、29、50等',
  `large_area_name` STRING COMMENT '运营大区名称，如：上海大区、昆明快递大区、成都大区、重庆大区、长沙大区等',
  `cust_type` STRING COMMENT '客户业态类型，枚举值：咖啡、烘焙、茶饮、其他',
  `uv` BIGINT COMMENT '登录用户数（去重用户数）',
  `pv` BIGINT COMMENT '登录页面浏览量',
  `exposure_sku_cnt` BIGINT COMMENT '曝光商品SKU数量',
  `view_time` DECIMAL(38,18) COMMENT '浏览时长，单位为分钟',
  `order_cust_cnt` BIGINT COMMENT '下单用户数（去重用户数）',
  `translate_rate` DECIMAL(38,18) COMMENT '转化率，计算公式：下单用户数/登录用户数',
  `unlogin_uv` BIGINT COMMENT '未登录用户数（去重用户数）',
  `unlogin_pv` BIGINT COMMENT '未登录页面浏览量',
  `cust_flag` STRING COMMENT '是否老客标识，枚举值：是、否',
  `home_page_pv` BIGINT COMMENT '首页登录页面浏览量',
  `order_cnt` BIGINT COMMENT '下单次数',
  `total_exposure_pv` BIGINT COMMENT '总曝光页面浏览量',
  `total_exposure_uv` BIGINT COMMENT '总曝光用户数（去重用户数）',
  `total_click_pv` BIGINT COMMENT '总点击页面浏览量',
  `total_click_uv` BIGINT COMMENT '总点击用户数（去重用户数）',
  `sku_exposure_pv` BIGINT COMMENT '商品曝光页面浏览量',
  `sku_exposure_uv` BIGINT COMMENT '商品曝光用户数（去重用户数）',
  `sku_click_pv` BIGINT COMMENT '商品点击页面浏览量',
  `sku_click_uv` BIGINT COMMENT '商品点击用户数（去重用户数）',
  `sku_cart_buy_pv` BIGINT COMMENT '商品加购页面浏览量',
  `sku_cart_buy_uv` BIGINT COMMENT '商品加购用户数（去重用户数）'
)
COMMENT '日志整体监控表，按运营大区和客户业态统计用户行为指标，包括登录、浏览、曝光、点击、加购、下单等核心指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据所属日期分区'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '日志整体监控表，用于监控各运营大区不同客户业态的用户行为数据',
  'lifecycle' = '30'
);
```