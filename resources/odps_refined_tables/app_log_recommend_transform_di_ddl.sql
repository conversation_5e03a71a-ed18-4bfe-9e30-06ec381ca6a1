CREATE TABLE IF NOT EXISTS app_log_recommend_transform_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd',
	`scene` STRING COMMENT '场景：首页、商品详情页、购物车页、分类页、活动页、特价专区',
	`cust_uv` BIGINT COMMENT '用户UV（去重用户数）',
	`cust_pv` BIGINT COMMENT '用户PV（页面浏览量）',
	`new_cust_uv` BIGINT COMMENT '新用户UV（去重新用户数）',
	`sku_impression_uv` BIGINT COMMENT '商品曝光UV（去重曝光用户数）',
	`sku_impression_pv` BIGINT COMMENT '商品曝光PV（商品曝光次数）',
	`sku_click_uv` BIGINT COMMENT '商品点击UV（去重点击用户数）',
	`sku_click_pv` BIGINT COMMENT '商品点击PV（商品点击次数）',
	`sku_cart_uv` BIGINT COMMENT '商品加购UV（去重加购用户数）',
	`sku_cart_pv` BIGINT COMMENT '商品加购PV（商品加购次数）',
	`order_cnt` BIGINT COMMENT '下单数（订单数量）',
	`order_amt` DECIMAL(38,18) COMMENT '下单金额（元）',
	`order_uv` BIGINT COMMENT '下单用户UV（去重下单用户数）',
	`order_paid_cnt` BIGINT COMMENT '支付订单数',
	`order_paid_amt` DECIMAL(38,18) COMMENT '支付金额（元）',
	`order_paid_uv` BIGINT COMMENT '支付订单用户UV（去重支付用户数）',
	`order_paid_amt_avg` DECIMAL(38,18) COMMENT '平均支付金额（元/订单）',
	`sku_click_uv_rate` DECIMAL(38,18) COMMENT '商品UV点击率（点击UV/曝光UV）',
	`sku_click_pv_rate` DECIMAL(38,18) COMMENT '商品PV点击率（点击PV/曝光PV）',
	`sku_cart_uv_rate` DECIMAL(38,18) COMMENT '商品UV加购率（加购UV/曝光UV）',
	`sku_cart_pv_rate` DECIMAL(38,18) COMMENT '商品PV加购率（加购PV/曝光PV）',
	`order_paid_uv_rate` DECIMAL(38,18) COMMENT '订单支付UV转化率（支付用户UV/总用户UV）',
	`abexperiments_experiment_id` STRING COMMENT 'AB实验解析字段-experiment_id，取值范围：实验ID或"无"',
	`abexperiments_experiment_place` STRING COMMENT 'AB实验解析字段-experiment_place，取值范围：实验位置或"无"',
	`abexperiments_variant_id` STRING COMMENT 'AB实验解析字段-variant_id，取值范围：变体ID或"无"'
) 
COMMENT '商城推荐转化汇总表，包含各场景下的用户行为数据和转化指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='商城推荐转化汇总表，统计各推荐场景下的用户行为、转化和AB实验数据') 
LIFECYCLE 30;