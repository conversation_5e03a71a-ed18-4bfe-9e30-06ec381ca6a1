CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_mkt_deliver_sku_preferential_mi`(
	`month` STRING COMMENT '月份，格式为yyyyMM',
	`sku_id` STRING COMMENT '商品SKU ID',
	`spu_id` BIGINT COMMENT '商品SPU ID（商品pd_id）',
	`spu_name` STRING COMMENT '商品名称',
	`sku_disc` STRING COMMENT '商品规格描述',
	`sku_type` STRING COMMENT '商品类型；枚举：自营,代仓,代售',
	`category1` STRING COMMENT '一级分类',
	`category2` STRING COMMENT '二级分类',
	`category3` STRING COMMENT '三级分类',
	`category4` STRING COMMENT '四级分类',
	`city_id` BIGINT COMMENT '运营服务区ID',
	`city_name` STRING COMMENT '运营服务区名称',
	`large_area_id` BIGINT COMMENT '运营服务大区ID',
	`large_area_name` STRING COMMENT '运营服务大区名称',
	`cust_team` STRING COMMENT '客户团队类型；枚举：集团大客户（茶百道）,集团大客户（喜茶）,Mars大客户,平台客户',
	`cust_type` STRING COMMENT '客户类型；枚举：面包蛋糕,西餐,咖啡,甜品冰淇淋,茶饮,其他',
	`life_cycle_detail` STRING COMMENT '生命周期标签（细）',
	`warehouse_no` BIGINT COMMENT '库存仓编号',
	`warehouse_name` STRING COMMENT '库存仓名称',
	`store_no` BIGINT COMMENT '城配仓编号',
	`store_name` STRING COMMENT '城配仓名称',
	`sku_cnt` BIGINT COMMENT '履约商品数量',
	`origin_total_amt` DECIMAL(38,18) COMMENT '应付金额',
	`real_total_amt` DECIMAL(38,18) COMMENT '实付金额',
	`cost_amt` DECIMAL(38,18) COMMENT '商品成本',
	`activity_coupon_amt` DECIMAL(38,18) COMMENT '平台活动券优惠金额',
	`sh_coupon_amt` DECIMAL(38,18) COMMENT '售后补偿券优惠金额',
	`new_cust_coupon_amt` DECIMAL(38,18) COMMENT '区域拉新券优惠金额',
	`qy_coupon_amt` DECIMAL(38,18) COMMENT '会员权益券优惠金额',
	`bd_coupon_amt` DECIMAL(38,18) COMMENT '销售客情券优惠金额',
	`recall_coupon_amt` DECIMAL(38,18) COMMENT '销售月活券优惠金额',
	`xp_coupon_amt` DECIMAL(38,18) COMMENT '行业活动券优惠金额',
	`return_coupon_amt` DECIMAL(38,18) COMMENT '满返优惠金额(作废)',
	`ygfl_coupon_amt` DECIMAL(38,18) COMMENT '员工福利券优惠金额',
	`thbt_coupon_amt` DECIMAL(38,18) COMMENT '销售囤货券优惠金额',
	`jjbc_coupon_amt` DECIMAL(38,18) COMMENT '区域活动券优惠金额',
	`pllx_coupon_amt` DECIMAL(38,18) COMMENT '销售品类券优惠金额',
	`plzh_coupon_amt` DECIMAL(38,18) COMMENT '品类召回优惠金额(作废)',
	`lsfx_coupon_amt` DECIMAL(38,18) COMMENT '销售现货券优惠金额',
	`zxbt_coupon_amt` DECIMAL(38,18) COMMENT '滞销补贴优惠金额(作废)',
	`lbbt_coupon_amt` DECIMAL(38,18) COMMENT '临保补贴优惠金额(作废)',
	`tsqk_coupon_amt` DECIMAL(38,18) COMMENT '功能测试券优惠金额',
	`jzs_coupon_amt` DECIMAL(38,18) COMMENT '精准送优惠金额',
	`bed_pack_amt` DECIMAL(38,18) COMMENT '红包优惠金额',
	`deliver_coupon_amt` DECIMAL(38,18) COMMENT '运费优惠券金额',
	`activity_nolb_amt` DECIMAL(38,18) COMMENT '活动优惠金额（不含临保）',
	`activity_lb_amt` DECIMAL(38,18) COMMENT '临保活动优惠金额',
	`ladder_price_amt` DECIMAL(38,18) COMMENT '阶梯价优惠金额',
	`collocation_amt` DECIMAL(38,18) COMMENT '搭配购优惠金额',
	`suit_amt` DECIMAL(38,18) COMMENT '组合包优惠金额',
	`expand_amt` DECIMAL(38,18) COMMENT '拓展购买优惠金额',
	`replace_amt` DECIMAL(38,18) COMMENT '换购优惠金额',
	`presale_amt` DECIMAL(38,18) COMMENT '预售优惠金额',
	`presale_balance_amt` DECIMAL(38,18) COMMENT '预售尾款立减优惠金额',
	`group_purchase_amt` DECIMAL(38,18) COMMENT '多人拼团优惠金额',
	`reduce_amt` DECIMAL(38,18) COMMENT '满减优惠金额',
	`seckill_amt` DECIMAL(38,18) COMMENT '秒杀优惠金额',
	`cream_card_amt` DECIMAL(38,18) COMMENT '奶油卡优惠金额',
	`milk_card_amt` DECIMAL(38,18) COMMENT '鲜奶卡优惠金额',
	`other_card_amt` DECIMAL(38,18) COMMENT '其他黄金卡优惠金额',
	`gift_amt` DECIMAL(38,18) COMMENT '赠品优惠金额',
	`delivery_amt` DECIMAL(38,18) COMMENT '运费应付金额',
	`delivery_real_amt` DECIMAL(38,18) COMMENT '运费实付金额',
	`live_exclusive_amt` DECIMAL(38,18) COMMENT '直播专享优惠金额',
	`live_sharing_amt` DECIMAL(38,18) COMMENT '直播同享优惠金额',
	`ppbc_coupon_amt` DECIMAL(38,18) COMMENT '平台补偿券优惠金额',
	`psbc_coupon_amt` DECIMAL(38,18) COMMENT '配送补偿券优惠金额',
	`qyzh_coupon_amt` DECIMAL(38,18) COMMENT '区域召回券优惠金额',
	`schd_coupon_amt` DECIMAL(38,18) COMMENT '市场活动券优惠金额',
	`qt_coupon_amt` DECIMAL(38,18) COMMENT '其他优惠券优惠金额'
) 
COMMENT '商品粒度履约优惠明细月表，记录商品维度的履约优惠明细数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='商品粒度履约优惠明细月表，包含商品基本信息、分类信息、区域信息、客户信息以及各类优惠金额明细') 
LIFECYCLE 30;