CREATE TABLE IF NOT EXISTS app_pcs_supplier_rebate_sku_cumulative_df(
	`year` BIGINT COMMENT '年度，格式：YYYY',
	`period` STRING COMMENT '周期，取值范围：年、月、季度等',
	`sku_id` STRING COMMENT 'SKU编码，商品唯一标识',
	`commodity_name_and_specification` STRING COMMENT '商品名称&规格，包含商品名称和规格信息的组合',
	`supplier_id` BIGINT COMMENT '供货商ID，供应商的唯一标识',
	`supplier_name` STRING COMMENT '供货商名称，供应商的完整名称',
	`warehouse_name` STRING COMMENT '仓库名称，存储商品的仓库名称，多个仓库用逗号分隔',
	`commodity_temperature_zone` STRING COMMENT '商品温区，取值范围：冷藏、冷冻、常温等',
	`purchase_order_amount_in_period` DECIMAL(38,18) COMMENT '周期内采购下单金额，含税金额',
	`purchase_order_amount_excluding_tax` DECIMAL(38,18) COMMENT '采购下单金额（不含税），不含税金额',
	`purchase_order_quantity_in_period` BIGINT COMMENT '周期内采购下单件数，采购商品的数量',
	`purchase_order_weight_in_period` DECIMAL(38,18) COMMENT '周期内采购下单重量，采购商品的总重量',
	`purchase_order_volume_in_period` DECIMAL(38,18) COMMENT '周期内采购下单体积，采购商品的总体积',
	`purchase_inbound_amount_in_period` DECIMAL(38,18) COMMENT '周期内采购入库金额，含税金额',
	`purchase_inbound_amount_excluding_tax` DECIMAL(38,18) COMMENT '采购入库金额（不含税），不含税金额',
	`purchase_inbound_quantity_in_period` BIGINT COMMENT '周期内采购入库件数，入库商品的数量',
	`purchase_inbound_weight_in_period` DECIMAL(38,18) COMMENT '周期内采购入库重量，入库商品的总重量',
	`purchase_inbound_volume_in_period` DECIMAL(38,18) COMMENT '周期内采购入库体积，入库商品的总体积',
	`purchase_reservation_amount_in_period` DECIMAL(38,18) COMMENT '周期内采购预约金额，含税金额',
	`purchase_reservation_amount_excluding_tax` DECIMAL(38,18) COMMENT '采购预约金额（不含税），不含税金额',
	`purchase_reservation_quantity_in_period` BIGINT COMMENT '周期内采购预约件数，预约商品的数量',
	`purchase_reservation_weight_in_period` DECIMAL(38,18) COMMENT '周期内采购预约重量，预约商品的总重量',
	`purchase_reservation_volume_in_period` DECIMAL(38,18) COMMENT '周期内采购预约体积，预约商品的总体积'
) 
COMMENT '供应商返利目标累计月维度表，记录供应商返利相关的采购下单、入库、预约等累计数据，按月维度统计'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='供应商返利目标累计月维度表，用于供应商返利计算和分析') 
LIFECYCLE 30;