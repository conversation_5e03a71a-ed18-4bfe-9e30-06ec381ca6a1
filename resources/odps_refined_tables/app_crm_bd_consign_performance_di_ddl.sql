```sql
CREATE TABLE IF NOT EXISTS app_crm_bd_consign_performance_di(
    `date` STRING COMMENT '业务日期，格式为yyyyMMdd，表示业绩统计的具体日期',
    `bd_id` BIGINT COMMENT '销售ID，唯一标识一个销售人员，取值范围：-1表示无销售，正数表示有效销售ID',
    `bd_name` STRING COMMENT '销售姓名，销售人员的真实姓名',
    `administrative_city` STRING COMMENT '销售所属行政城市，销售人员所在的城市名称',
    `zone_name` STRING COMMENT '区域名称，销售人员所属的业务区域',
    `m1` STRING COMMENT '城市负责人（M1），销售的直接上级管理者',
    `m2` STRING COMMENT '区域负责人（M2），M1的直接上级管理者',
    `m3` STRING COMMENT '部门负责人（M3），M2的直接上级管理者',
    `cust_type` STRING COMMENT '客户类型；枚举值：面包蛋糕,西餐,咖啡,甜品冰淇淋,茶饮,其他',
    `order_cust_cnt` BIGINT COMMENT '下单客户数，当日下单的独立客户数量',
    `order_sku_cnt` BIGINT COMMENT '销量，当日销售的商品总数量',
    `order_cnt` BIGINT COMMENT '订单数，当日产生的订单总数',
    `real_total_amt` DECIMAL(38,18) COMMENT '订单实付金额，当日订单的实际支付总金额',
    `drop_in_visit_cust_cnt` BIGINT COMMENT '上门拜访客户数（上门/有效），当日有效上门拜访的客户数量',
    `visit_cust_cnt` BIGINT COMMENT '总拜访客户数，当日所有拜访的客户总数量'
)
COMMENT 'BD粒度代售业绩报表日汇总表，按销售人员和日期维度统计代售业务绩效指标'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，用于数据管理和查询优化'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment'='销售代售业绩日统计表，包含销售层级关系、客户类型和各项业务指标',
    'columnar.nested.type'='true'
)
LIFECYCLE 30;
```