```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_crm_city_today_hour_gmv_di_extra_h` (
  `city` STRING COMMENT '行政城市名称，如：上海市、北京市等',
  `area` STRING COMMENT '区域名称，如：嘉定区、奉贤区等',
  `origin_total_gmv` DECIMAL(38,18) COMMENT '交易应付GMV（不含全品类），单位：元',
  `real_total_gmv` DECIMAL(38,18) COMMENT '交易实付GMV（不含全品类），单位：元',
  `cust_cnt` BIGINT COMMENT '交易客户数（不含全品类）',
  `categories_origin_total_gmv` DECIMAL(38,18) COMMENT '全品类交易应付GMV，单位：元',
  `categories_real_total_gmv` DECIMAL(38,18) COMMENT '全品类交易实付GMV，单位：元',
  `categories_cust_cnt` BIGINT COMMENT '全品类交易客户数',
  `fruit_origin_total_gmv` DECIMAL(38,18) COMMENT '鲜果交易应付GMV（不含全品类），单位：元',
  `fruit_real_total_gmv` DECIMAL(38,18) COMMENT '鲜果交易实付GMV（不含全品类），单位：元',
  `fruit_cust_cnt` BIGINT COMMENT '鲜果交易客户数（不含全品类）',
  `anchor_origin_total_gmv` DECIMAL(38,18) COMMENT '安佳铁塔交易应付GMV，单位：元',
  `anchor_real_total_gmv` DECIMAL(38,18) COMMENT '安佳铁塔交易实付GMV，单位：元',
  `anchor_cust_cnt` BIGINT COMMENT '安佳铁塔交易客户数',
  `dairy_origin_total_gmv` DECIMAL(38,18) COMMENT '乳制品交易应付GMV（不含全品类），单位：元',
  `dairy_real_total_gmv` DECIMAL(38,18) COMMENT '乳制品交易实付GMV（不含全品类），单位：元',
  `dairy_cust_cnt` BIGINT COMMENT '乳制品交易客户数（不含全品类）',
  `other_origin_total_gmv` DECIMAL(38,18) COMMENT '其他品类交易应付GMV（不含全品类），单位：元',
  `other_real_total_gmv` DECIMAL(38,18) COMMENT '其他品类交易实付GMV（不含全品类），单位：元',
  `other_cust_cnt` BIGINT COMMENT '其他品类交易客户数（不含全品类）',
  `no_anchor_origin_total_gmv` DECIMAL(38,18) COMMENT '非安佳铁塔交易应付GMV（不含全品类），单位：元',
  `no_anchor_real_total_gmv` DECIMAL(38,18) COMMENT '非安佳铁塔交易实付GMV（不含全品类），单位：元',
  `no_anchor_cust_cnt` BIGINT COMMENT '非安佳铁塔交易客户数（不含全品类）',
  `dlv_real_total_gmv` DECIMAL(38,18) COMMENT '履约自营实付GMV（不含全品类），单位：元',
  `dlv_cust_cnt` BIGINT COMMENT '履约自营客户数（不含全品类）',
  `dlv_categories_real_total_gmv` DECIMAL(38,18) COMMENT '全品类履约实付GMV，单位：元',
  `dlv_categories_cust_cnt` BIGINT COMMENT '全品类履约客户数',
  `dlv_fruit_real_total_gmv` DECIMAL(38,18) COMMENT '鲜果自营履约实付GMV（不含全品类），单位：元',
  `dlv_fruit_cust_cnt` BIGINT COMMENT '鲜果自营履约客户数（不含全品类）',
  `dlv_anchor_real_total_gmv` DECIMAL(38,18) COMMENT '安佳铁塔履约实付GMV，单位：元',
  `dlv_anchor_cust_cnt` BIGINT COMMENT '安佳铁塔履约客户数',
  `dlv_dairy_real_total_gmv` DECIMAL(38,18) COMMENT '乳制品自营履约实付GMV（不含全品类），单位：元',
  `dlv_dairy_cust_cnt` BIGINT COMMENT '乳制品自营履约客户数（不含全品类）',
  `dlv_other_real_total_gmv` DECIMAL(38,18) COMMENT '其他品类履约实付GMV（不含全品类），单位：元',
  `dlv_other_cust_cnt` BIGINT COMMENT '其他品类履约客户数（不含全品类）',
  `dlv_no_anchor_real_total_gmv` DECIMAL(38,18) COMMENT '非安佳铁塔履约实付GMV（不含全品类），单位：元',
  `dlv_no_anchor_cust_cnt` BIGINT COMMENT '非安佳铁塔履约客户数（不含全品类）',
  `dlv_timing_real_total_gmv` DECIMAL(38,18) COMMENT '省心送明日实付GMV，单位：元',
  `dlv_timing_anchor_real_total_gmv` DECIMAL(38,18) COMMENT '安佳铁塔省心送明日实付GMV，单位：元',
  `dlv_timing_dairy_real_total_gmv` DECIMAL(38,18) COMMENT '乳制品自营省心送明日实付GMV（不含全品类+AT），单位：元',
  `dlv_timing_other_real_total_gmv` DECIMAL(38,18) COMMENT '其他自营省心送明日实付GMV，单位：元',
  `dlv_tomorrow_real_total_gmv` DECIMAL(38,18) COMMENT '履约明日自营品实付GMV（不含全品类），单位：元',
  `dlv_tomorrow_cust_cnt` BIGINT COMMENT '履约明日自营品客户数（不含全品类）',
  `no_anchor_categories_kpi_gmv` DECIMAL(38,18) COMMENT '非AT履约实付GMV（自营品）+全品类交易实付GMV，单位：元',
  `accompanying_visits_m1` BIGINT COMMENT 'M1陪访数，取值范围：0-∞',
  `accompanying_visits_m2` BIGINT COMMENT 'M2陪访数，取值范围：0-∞',
  `day_tag` STRING COMMENT '时间标识，格式：yyyyMMdd，表示数据所属日期'
)
COMMENT '城市今日数据（小时级）- 按城市和区域统计的小时级GMV和客户数数据，包含交易和履约相关指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '城市小时级GMV统计表，包含交易和履约相关的各类GMV和客户数指标',
  'lifecycle' = '30'
);
```