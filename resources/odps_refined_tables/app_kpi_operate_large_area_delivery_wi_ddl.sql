CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_kpi_operate_large_area_delivery_wi` (
  `year` STRING COMMENT '年份，格式：YYYY',
  `week_of_year` STRING COMMENT '周数，格式：1-53',
  `monday` STRING COMMENT '周一日期，格式：YYYYMMDD',
  `sunday` STRING COMMENT '周日日期，格式：YYYYMMDD',
  `large_area_name` STRING COMMENT '运营服务大区名称，枚举值：南宁大区、昆明快递大区、杭州大区、武汉大区、贵阳大区等',
  `origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV（总金额）',
  `real_total_amt` DECIMAL(38,18) COMMENT '履约实付GMV（总金额）',
  `marketing_amt` DECIMAL(38,18) COMMENT '营销费用',
  `cost_amt` DECIMAL(38,18) COMMENT '成本金额',
  `origin_gross` DECIMAL(38,18) COMMENT '应付毛利润',
  `real_gross` DECIMAL(38,18) COMMENT '实付毛利润',
  `origin_gross_margin` DECIMAL(38,18) COMMENT '应付毛利率（百分比小数形式）',
  `real_gross_margin` DECIMAL(38,18) COMMENT '实付毛利率（百分比小数形式）',
  `cust_cnt` BIGINT COMMENT '履约客户数',
  `point_cnt` BIGINT COMMENT '点位数',
  `origin_pre_cust_price` DECIMAL(38,18) COMMENT '应付客单价（平均每个客户的应付金额）',
  `real_pre_cust_price` DECIMAL(38,18) COMMENT '实付客单价（平均每个客户的实付金额）',
  `timing_origin_amt` DECIMAL(38,18) COMMENT '省心送应付GMV',
  `timing_real_amt` DECIMAL(38,18) COMMENT '省心送实付GMV',
  `consign_origin_amt` DECIMAL(38,18) COMMENT '代售应付GMV',
  `consign_real_amt` DECIMAL(38,18) COMMENT '代售实付GMV',
  `consign_marketing_amt` DECIMAL(38,18) COMMENT '代售营销费用',
  `consign_origin_gross` DECIMAL(38,18) COMMENT '代售应付毛利润',
  `consign_real_gross` DECIMAL(38,18) COMMENT '代售实付毛利润',
  `consign_cust_cnt` BIGINT COMMENT '代售履约客户数，枚举值：0-无代售客户',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储费',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '单点干线费',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提费',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨费',
  `other_amt` DECIMAL(38,18) COMMENT '其他费用',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送费'
) 
COMMENT '运营履约KPI表（平台客户），包含各大区的运营履约关键绩效指标数据，如GMV、毛利率、客户数、费用等'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据日期'
)
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
               'comment'='运营履约KPI数据表，按大区和周维度统计的履约绩效指标') 
LIFECYCLE 30;