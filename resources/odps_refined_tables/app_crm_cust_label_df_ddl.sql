```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_crm_cust_label_df` (
  `cust_id` BIGINT COMMENT '客户编号，唯一标识客户的ID',
  `cust_name` STRING COMMENT '客户名称',
  `operate_status` BIGINT COMMENT '运营状态：0-正常，1-倒闭',
  `register_time` DATETIME COMMENT '注册时间，格式为年月日时分秒',
  `audit_time` DATETIME COMMENT '审核通过时间，格式为年月日时分秒',
  `abandon_date` BIGINT COMMENT '废弃日期，格式为yyyyMMdd',
  `islock` BIGINT COMMENT '审核状态：0-审核通过，1-审核中，2-审核未通过，3-账号被拉黑',
  `is_audit_30d` BIGINT COMMENT '是否在近30日审核通过：0-否，1-是',
  `is_first_order_30d` BIGINT COMMENT '是否在近30日是首单客户：0-否，1-是',
  `is_first_register_30d` BIGINT COMMENT '是否在近30日是首次注册客户：0-否，1-是',
  `cust_type` STRING COMMENT '客户类型；枚举值：面包蛋糕,西餐,咖啡,甜品冰淇淋,茶饮,其他',
  `cust_team` STRING COMMENT '客户团队类型；枚举值：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户',
  `brand_id` BIGINT COMMENT '品牌ID',
  `brand_name` STRING COMMENT '品牌公司名称',
  `brand_alias` STRING COMMENT '品牌别名',
  `city_id` BIGINT COMMENT '城市ID(运营服务区)',
  `city_name` STRING COMMENT '城市名称',
  `register_province` STRING COMMENT '注册时省份',
  `register_city` STRING COMMENT '注册时城市',
  `register_area` STRING COMMENT '注册时区域',
  `bd_id` BIGINT COMMENT '销售ID',
  `bd_name` STRING COMMENT '销售名称',
  `large_area_id` BIGINT COMMENT '服务大区ID',
  `large_area_name` STRING COMMENT '服务大区名称',
  `bd_zone` STRING COMMENT '销售大区',
  `m1` STRING COMMENT '城市负责人',
  `m2` STRING COMMENT '区域负责人',
  `m3` STRING COMMENT '部门负责人',
  `invite_bd_id` BIGINT COMMENT '销售邀请人ID',
  `invite_bd_name` STRING COMMENT '销售邀请人名称',
  `invite_driver_id` BIGINT COMMENT '司机邀请人ID',
  `invite_driver_name` STRING COMMENT '司机邀请人名称',
  `invite_cust_id` BIGINT COMMENT '客户邀请人ID',
  `invite_cust_name` STRING COMMENT '客户邀请人名称',
  `is_disabled_bd` BIGINT COMMENT 'BD是否离职：0-在职，1-离职',
  `frequency` DECIMAL(38,18) COMMENT '近1年平均下单周期（超过60天取60天）',
  `top_line` DECIMAL(38,18) COMMENT '近1年平均下单周期上限',
  `low_line` DECIMAL(38,18) COMMENT '近1年平均下单周期下限',
  `standard_line` STRING COMMENT '近1年标准线标记；枚举值：over,normal,no_deal,first_deal',
  `first_order_days` BIGINT COMMENT '最早一次下单距今天数',
  `last_order_days` BIGINT COMMENT '最近一次下单距今天数',
  `first_delivery_days` BIGINT COMMENT '最早一次配送距今天数',
  `last_delivery_days` BIGINT COMMENT '最近一次配送距今天数',
  `life_cycle` STRING COMMENT '生命周期标签（粗）',
  `life_cycle_detail` STRING COMMENT '生命周期标签（细）',
  `cust_value` STRING COMMENT '用户价值标签（111，132呈现）',
  `cust_value_score` DECIMAL(38,18) COMMENT '用户价值标签价值分',
  `r_value` STRING COMMENT 'R价值标签',
  `f_value` STRING COMMENT 'F价值标签',
  `m_value` STRING COMMENT 'M价值标签',
  `spu_warn` STRING COMMENT 'SPU预警标签',
  `first_order_time` DATETIME COMMENT '最早一次下单时间，格式为年月日时分秒',
  `last_order_time` DATETIME COMMENT '最近一次下单时间，格式为年月日时分秒',
  `order_real_amt_7d` DECIMAL(38,18) COMMENT '最近7天下单实付金额',
  `order_real_amt_14d` DECIMAL(38,18) COMMENT '最近14天下单实付金额',
  `order_real_amt_30d` DECIMAL(38,18) COMMENT '最近30天下单实付金额',
  `order_real_amt_60d` DECIMAL(38,18) COMMENT '最近60天下单实付金额',
  `order_real_amt_180d` DECIMAL(38,18) COMMENT '最近180天下单实付金额',
  `order_real_amt_365d` DECIMAL(38,18) COMMENT '最近365天下单实付金额',
  `order_real_amt_his` DECIMAL(38,18) COMMENT '历史所有下单实付金额',
  `order_origin_amt_7d` DECIMAL(38,18) COMMENT '最近7天下单应付金额',
  `order_origin_amt_14d` DECIMAL(38,18) COMMENT '最近14天下单应付金额',
  `order_origin_amt_30d` DECIMAL(38,18) COMMENT '最近30天下单应付金额',
  `order_origin_amt_60d` DECIMAL(38,18) COMMENT '最近60天下单应付金额',
  `order_origin_amt_180d` DECIMAL(38,18) COMMENT '最近180天下单应付金额',
  `order_origin_amt_365d` DECIMAL(38,18) COMMENT '最近365天下单应付金额',
  `order_origin_amt_his` DECIMAL(38,18) COMMENT '历史所有下单应付金额',
  `order_days_7d` BIGINT COMMENT '最近7天下单频次（下单天数）',
  `order_days_14d` BIGINT COMMENT '最近14天下单频次（下单天数）',
  `order_days_30d` BIGINT COMMENT '最近30天下单频次（下单天数）',
  `order_days_60d` BIGINT COMMENT '最近60天下单频次（下单天数）',
  `order_days_180d` BIGINT COMMENT '最近180天下单频次（下单天数）',
  `order_days_365d` BIGINT COMMENT '最近365天下单频次（下单天数）',
  `order_days_his` BIGINT COMMENT '历史所有下单频次（下单天数）',
  `order_cnt_7d` BIGINT COMMENT '最近7天订单数',
  `order_cnt_14d` BIGINT COMMENT '最近14天订单数',
  `order_cnt_30d` BIGINT COMMENT '最近30天订单数',
  `order_cnt_60d` BIGINT COMMENT '最近60天订单数',
  `order_cnt_180d` BIGINT COMMENT '最近180天订单数',
  `order_cnt_365d` BIGINT COMMENT '最近365天订单数',
  `order_cnt_his` BIGINT COMMENT '历史所有订单数',
  `order_sku_cnt_7d` BIGINT COMMENT '最近7天订单商品件数',
  `order_sku_cnt_14d` BIGINT COMMENT '最近14天订单商品件数',
  `order_sku_cnt_30d` BIGINT COMMENT '最近30天订单商品件数',
  `order_sku_cnt_60d` BIGINT COMMENT '最近60天订单商品件数',
  `order_sku_cnt_180d` BIGINT COMMENT '最近180天订单商品件数',
  `order_sku_cnt_365d` BIGINT COMMENT '最近365天订单商品件数',
  `order_sku_cnt_his` BIGINT COMMENT '历史所有订单商品件数',
  `order_spu_cnt_7d` BIGINT COMMENT '最近7天订单去重SPU数',
  `order_spu_cnt_14d` BIGINT COMMENT '最近14天订单去重SPU数',
  `order_spu_cnt_30d` BIGINT COMMENT '最近30天订单去重SPU数',
  `order_spu_cnt_60d` BIGINT COMMENT '最近60天订单去重SPU数',
  `order_spu_cnt_180d` BIGINT COMMENT '最近180天订单去重SPU数',
  `order_spu_cnt_365d` BIGINT COMMENT '最近365天订单去重SPU数',
  `order_spu_cnt_his` BIGINT COMMENT '历史所有订单去重SPU数',
  `order_fruit_real_amt_7d` DECIMAL(38,18) COMMENT '最近7天鲜果下单实付金额',
  `order_fruit_real_amt_14d` DECIMAL(38,18) COMMENT '最近14天鲜果下单实付金额',
  `order_fruit_real_amt_30d` DECIMAL(38,18) COMMENT '最近30天鲜果下单实付金额',
  `order_fruit_real_amt_60d` DECIMAL(38,18) COMMENT '最近60天鲜果下单实付金额',
  `order_fruit_real_amt_180d` DECIMAL(38,18) COMMENT '最近180天鲜果下单实付金额',
  `order_fruit_real_amt_365d` DECIMAL(38,18) COMMENT '最近365天鲜果下单实付金额',
  `order_fruit_real_amt_his` DECIMAL(38,18) COMMENT '历史所有鲜果下单实付金额',
  `order_fruit_origin_amt_7d` DECIMAL(38,18) COMMENT '最近7天鲜果下单应付金额',
  `order_fruit_origin_amt_14d` DECIMAL(38,18) COMMENT '最近14天鲜果下单应付金额',
  `order_fruit_origin_amt_30d` DECIMAL(38,18) COMMENT '最近30天鲜果下单应付金额',
  `order_fruit_origin_amt_60d` DECIMAL(38,18) COMMENT '最近60天鲜果下单应付金额',
  `order_fruit_origin_amt_180d` DECIMAL(38,18) COMMENT '最近180天鲜果下单应付金额',
  `order_fruit_origin_amt_365d` DECIMAL(38,18) COMMENT '最近365天鲜果下单应付金额',
  `order_fruit_origin_amt_his` DECIMAL(38,18) COMMENT '历史所有鲜果下单应付金额',
  `order_fruit_sku_cnt_7d` BIGINT COMMENT '最近7天订单鲜果商品件数',
  `order_fruit_sku_cnt_14d` BIGINT COMMENT '最近14天订单鲜果商品件数',
  `order_fruit_sku_cnt_30d` BIGINT COMMENT '最近30天订单鲜果商品件数',
  `order_fruit_sku_cnt_60d` BIGINT COMMENT '最近60天订单鲜果商品件数',
  `order_fruit_sku_cnt_180d` BIGINT COMMENT '最近180天订单鲜果商品件数',
  `order_fruit_sku_cnt_365d` BIGINT COMMENT '最近365天订单鲜果商品件数',
  `order_fruit_sku_cnt_his` BIGINT COMMENT '历史所有订单鲜果商品件数',
  `order_dairy_real_amt_7d` DECIMAL(38,18) COMMENT '最近7天乳制品下单实付金额',
  `order_dairy_real_amt_14d` DECIMAL(38,18) COMMENT '最近14天乳制品下单实付金额',
  `order_dairy_real_amt_30d` DECIMAL(38,18) COMMENT '最近30天乳制品下单实付金额',
  `order_dairy_real_amt_60d` DECIMAL(38,18) COMMENT '最近60天乳制品下单实付金额',
  `order_dairy_real_amt_180d` DECIMAL(38,18) COMMENT '最近180天乳制品下单实付金额',
  `order_dairy_real_amt_365d` DECIMAL(38,18) COMMENT '最近365天乳制品下单实付金额',
  `order_dairy_real_amt_his` DECIMAL(38,18) COMMENT '历史所有乳制品下单实付金额',
  `order_dairy_origin_amt_7d` DECIMAL(38,18) COMMENT '最近7天乳制品下单应付金额',
  `order_dairy_origin_amt_14d` DECIMAL(38,18) COMMENT '最近14天乳制品下单应付金额',
  `order_dairy_origin_amt_30d` DECIMAL(38,18) COMMENT '最近30天乳制品下单应付金额',
  `order_dairy_origin_amt_60d` DECIMAL(38,18) COMMENT '最近60天乳制品下单应付金额',
  `order_dairy_origin_amt_180d` DECIMAL(38,18) COMMENT '最近180天乳制品下单应付金额',
  `order_dairy_origin_amt_365d` DECIMAL(38,18) COMMENT '最近365天乳制品下单应付金额',
  `order_dairy_origin_amt_his` DECIMAL(38,18) COMMENT '历史所有乳制品下单应付金额',
  `order_dairy_sku_cnt_7d` BIGINT COMMENT '最近7天订单乳制品商品件数',
  `order_dairy_sku_cnt_14d` BIGINT COMMENT '最近14天订单乳制品商品件数',
  `order_dairy_sku_cnt_30d` BIGINT COMMENT '最近30天订单乳制品商品件数',
  `order_dairy_sku_cnt_60d` BIGINT COMMENT '最近60天订单乳制品商品件数',
  `order_dairy_sku_cnt_180d` BIGINT COMMENT '最近180天订单乳制品商品件数',
  `order_dairy_sku_cnt_365d` BIGINT COMMENT '最近365天订单乳制品商品件数',
  `order_dairy_sku_cnt_his` BIGINT COMMENT '历史所有订单乳制品商品件数',
  `order_other_real_amt_7d` DECIMAL(38,18) COMMENT '最近7天其他下单实付金额',
  `order_other_real_amt_14d` DECIMAL(38,18) COMMENT '最近14天其他下单实付金额',
  `order_other_real_amt_30d` DECIMAL(38,18) COMMENT '最近30天其他下单实付金额',
  `order_other_real_amt_60d` DECIMAL(38,18) COMMENT '最近60天其他下单实付金额',
  `order_other_real_amt_180d` DECIMAL(38,18) COMMENT '最近180天其他下单实付金额',
  `order_other_real_amt_365d` DECIMAL(38,18) COMMENT '最近365天其他下单实付金额',
  `order_other_real_amt_his` DECIMAL(38,18) COMMENT '历史所有其他下单实付金额',
  `order_other_origin_amt_7d` DECIMAL(38,18) COMMENT '最近7天其他下单应付金额',
  `order_other_origin_amt_14d` DECIMAL(38,18) COMMENT '最近14天其他下单应付金额',
  `order_other_origin_amt_30d` DECIMAL(38,18) COMMENT '最近30天其他下单应付金额',
  `order_other_origin_amt_60d` DECIMAL(38,18) COMMENT '最近60天其他下单应付金额',
  `order_other_origin_amt_180d` DECIMAL(38,18) COMMENT '最近180天其他下单应付金额',
  `order_other_origin_amt_365d` DECIMAL(38,18) COMMENT '最近365天其他下单应付金额',
  `order_other_origin_amt_his` DECIMAL(38,18) COMMENT '历史所有其他下单应付金额',
  `order_other_sku_cnt_7d` BIGINT COMMENT '最近7天订单其他商品件数',
  `order_other_sku_cnt_14d` BIGINT COMMENT '最近14天订单其他商品件数',
  `order_other_sku_cnt_30d` BIGINT COMMENT '最近30天订单其他商品件数',
  `order_other_sku_cnt_60d` BIGINT COMMENT '最近60天订单其他商品件数',
  `order_other_sku_cnt_180d` BIGINT COMMENT '最近180天订单其他商品件数',
  `order_other_sku_cnt_365d` BIGINT COMMENT '最近365天订单其他商品件数',
  `order_other_sku_cnt_his` BIGINT COMMENT '历史所有订单其他商品件数',
  `order_self_real_amt_7d` DECIMAL(38,18) COMMENT '最近7天自营品牌下单实付金额',
  `order_self_real_amt_14d` DECIMAL(38,18) COMMENT '最近14天自营品牌下单实付金额',
  `order_self_real_amt_30d` DECIMAL(38,18) COMMENT '最近30天自营品牌下单实付金额',
  `order_self_real_amt_60d` DECIMAL(38,18) COMMENT '最近60天自营品牌下单实付金额',
  `order_self_real_amt_180d` DECIMAL(38,18) COMMENT '最近180天自营品牌下单实付金额',
  `order_self_real_amt_365d` DECIMAL(38,18) COMMENT '最近365天自营品牌下单实付金额',
  `order_self_real_amt_his` DECIMAL(38,18) COMMENT '历史所有自营品牌下单实付金额',
  `order_self_origin_amt_7d` DECIMAL(38,18) COMMENT '最近7天自营品牌下单应付金额',
  `order_self_origin_amt_14d` DECIMAL(38,18) COMMENT '最近14天自营品牌下单应付金额',
  `order_self_origin_amt_30d` DECIMAL(38,18) COMMENT '最近30天自营品牌下单应付金额',
  `order_self_origin_amt_60d` DECIMAL(38,18) COMMENT '最近60天自营品牌下单应付金额',
  `order_self_origin_amt_180d` DECIMAL(38,18) COMMENT '最近180天自营品牌下单应付金额',
  `order_self_origin_amt_365d` DECIMAL(38,18) COMMENT '最近365天自营品牌下单应付金额',
  `order_self_origin_amt_his` DECIMAL(38,18) COMMENT '历史所有自营品牌下单应付金额',
  `order_self_sku_cnt_7d` BIGINT COMMENT '最近7天订单自营品牌商品件数',
  `order_self_sku_cnt_14d` BIGINT COMMENT '最近14天订单自营品牌商品件数',
  `order_self_sku_cnt_30d` BIGINT COMMENT '最近30天订单自营品牌商品件数',
  `order_self_sku_cnt_60d` BIGINT COMMENT '最近60天订单自营品牌商品件数',
  `order_self_sku_cnt_180d` BIGINT COMMENT '最近180天订单自营品牌商品件数',
  `order_self_sku_cnt_365d` BIGINT COMMENT '最近365天订单自营品牌商品件数',
  `order_self_sku_cnt_his` BIGINT COMMENT '历史所有订单自营品牌商品件数',
  `order_timing_real_amt_7d` DECIMAL(38,18) COMMENT '最近7天省心送下单实付金额',
  `order_timing_real_amt_14d` DECIMAL(38,18) COMMENT '最近14天省心送下单实付金额',
  `order_timing_real_amt_30d` DECIMAL(38,18) COMMENT '最近30天省心送下单实付金额',
  `order_timing_real_amt_60d` DECIMAL(38,18) COMMENT '最近60天省心送下单实付金额',
  `order_timing_real_amt_180d` DECIMAL(38,18) COMMENT '最近180天省心送下单实付金额',
  `order_timing_real_amt_365d` DECIMAL(38,18) COMMENT '最近365天省心送下单实付金额',
  `order_timing_real_amt_his` DECIMAL(38,18) COMMENT '历史所有省心送下单实付金额',
  `order_timing_origin_amt_7d` DECIMAL(38,18) COMMENT '最近7天省心送下单应付金额',
  `order_timing_origin_amt_14d` DECIMAL(38,18) COMMENT '最近14天省心送下单应付金额',
  `order_timing_origin_amt_30d` DECIMAL(38,18) COMMENT '最近30天省心送下单应付金额',
  `order_timing_origin_amt_60d` DECIMAL(38,18) COMMENT '最近60天省心送下单应付金额',
  `order_timing_origin_amt_180d` DECIMAL(38,18) COMMENT '最近180天省心送下单应付金额',
  `order_timing_origin_amt_365d` DECIMAL(38,18) COMMENT '最近365天省心送下单应付金额',
  `order_timing_origin_amt_his` DECIMAL(38,18) COMMENT '历史所有省心送下单应付金额',
  `order_timing_sku_cnt_7d` BIGINT COMMENT '最近7天订单省心送商品件数',
  `order_timing_sku_cnt_14d` BIGINT COMMENT '最近14天订单省心送商品件数',
  `order_timing_sku_cnt_30d` BIGINT COMMENT '最近30天订单省心送商品件数',
  `order_timing_sku_cnt_60d` BIGINT COMMENT '最近60天订单省心送商品件数',
  `order_timing_sku_cnt_180d` BIGINT COMMENT '最近180天订单省心送商品件数',
  `order_timing_sku_cnt_365d` BIGINT COMMENT '最近365天订单省心送商品件数',
  `order_timing_sku_cnt_his` BIGINT COMMENT '历史所有订单省心送商品件数',
  `order_real_amt_30d_last` DECIMAL(38,18) COMMENT '上个周期（距30-60天）下单实付金额',
  `order_origin_amt_30d_last` DECIMAL(38,18) COMMENT '上个周期（距30-60天）下单应付金额',
  `order_spu_cnt_30d_last` BIGINT COMMENT '上个周期（距30-60天）订单去重SPU数',
  `order_days_30d_last` BIGINT COMMENT '上个周期（距30-60天）下单频次（下单天数）',
  `coupon_amt_7d` DECIMAL(38,18) COMMENT '最近7天优惠券金额（不包括运费券和售后券）',
  `coupon_amt_14d` DECIMAL(38,18) COMMENT '最近14天优惠券金额（不包括运费券和售后券）',
  `coupon_amt_30d` DECIMAL(38,18) COMMENT '最近30天优惠券金额（不包括运费券和售后券）',
  `coupon_amt_60d` DECIMAL(38,18) COMMENT '最近60天优惠券金额（不包括运费券和售后券）',
  `coupon_amt_180d` DECIMAL(38,18) COMMENT '最近180天优惠券金额（不包括运费券和售后券）',
  `coupon_amt_365d` DECIMAL(38,18) COMMENT '最近365天优惠券金额（不包括运费券和售后券）',
  `coupon_amt_his` DECIMAL(38,18) COMMENT '历史所有优惠券金额（不包括运费券和售后券）',
  `deliver_coupon_amt_7d` DECIMAL(38,18) COMMENT '最近7天运费券金额',
  `deliver_coupon_amt_14d` DECIMAL(38,18) COMMENT '最近14天运费券金额',
  `deliver_coupon_amt_30d` DECIMAL(38,18) COMMENT '最近30天运费券金额',
  `deliver_coupon_amt_60d` DECIMAL(38,18) COMMENT '最近60天运费券金额',
  `deliver_coupon_amt_180d` DECIMAL(38,18) COMMENT '最近180天运费券金额',
  `deliver_coupon_amt_365d` DECIMAL(38,18) COMMENT '最近365天运费券金额',
  `deliver_coupon_amt_his` DECIMAL(38,18) COMMENT '历史所有运费券金额',
  `after_sale_coupon_amt_7d` DECIMAL(38,18) COMMENT '最近7天售后券金额',
  `after_sale_coupon_amt_14d` DECIMAL(38,18) COMMENT '最近14天售后券金额',
  `after_sale_coupon_amt_30d` DECIMAL(38,18) COMMENT '最近30天售后券金额',
  `after_sale_coupon_amt_60d` DECIMAL(38,18) COMMENT '最近60天售后券金额',
  `after_sale_coupon_amt_180d` DECIMAL(38,18) COMMENT '最近180天售后券金额',
  `after_sale_coupon_amt_365d` DECIMAL(38,18) COMMENT '最近365天售后券金额',
  `after_sale_coupon_amt_his` DECIMAL(38,18) COMMENT '历史所有售后券金额',
  `after_sale_amt_7d` DECIMAL(38,18) COMMENT '最近7天售后金额',
  `after_sale_amt_14d` DECIMAL(38,18) COMMENT '最近14天售后金额',
  `after_sale_amt_30d` DECIMAL(38,18) COMMENT '最近30天售后金额',
  `after_sale_amt_60d` DECIMAL(38,18) COMMENT '最近60天售后金额',
  `after_sale_amt_180d` DECIMAL(38,18) COMMENT '最近180天售后金额',
  `after_sale_amt_365d` DECIMAL(38,18) COMMENT '最近365天售后金额',
  `after_sale_amt_his` DECIMAL(38,18) COMMENT '历史所有售后金额',
  `fruit_after_sale_amt_7d` DECIMAL(38,18) COMMENT '最近7天鲜果售后金额',
  `fruit_after_sale_amt_14d` DECIMAL(38,18) COMMENT '最近14天鲜果售后金额',
  `fruit_after_sale_amt_30d` DECIMAL(38,18) COMMENT '最近30天鲜果售后金额',
  `fruit_after_sale_amt_60d` DECIMAL(38,18) COMMENT '最近60天鲜果售后金额',
  `fruit_after_sale_amt_180d` DECIMAL(38,18) COMMENT '最近180天鲜果售后金额',
  `fruit_after_sale_amt_365d` DECIMAL(38,18) COMMENT '最近365天鲜果售后金额',
  `fruit_after_sale_amt_his` DECIMAL(38,18) COMMENT '历史所有鲜果售后金额',
  `dairy_after_sale_amt_7d` DECIMAL(38,18) COMMENT '最近7天乳制品售后金额',
  `dairy_after_sale_amt_14d` DECIMAL(38,18) COMMENT '最近14天乳制品售后金额',
  `dairy_after_sale_amt_30d` DECIMAL(38,18) COMMENT '最近30天乳制品售后金额',
  `dairy_after_sale_amt_60d` DECIMAL(38,18) COMMENT '最近60天乳制品售后金额',
  `dairy_after_sale_amt_180d` DECIMAL(38,18) COMMENT '最近180天乳制品售后金额',
  `dairy_after_sale_amt_365d` DECIMAL(38,18) COMMENT '最近365天乳制品售后金额',
  `dairy_after_sale_amt_his` DECIMAL(38,18) COMMENT '历史所有乳制品售后金额',
  `other_after_sale_amt_7d` DECIMAL(38,18) COMMENT '最近7天其他售后金额',
  `other_after_sale_amt_14d` DECIMAL(38,18) COMMENT '最近14天其他售后金额',
  `other_after_sale_amt_30d` DECIMAL(38,18) COMMENT '最近30天其他售后金额',
  `other_after_sale_amt_60d` DECIMAL(38,18) COMMENT '最近60天其他售后金额',
  `other_after_sale_amt_180d` DECIMAL(38,18) COMMENT '最近180天其他售后金额',
  `other_after_sale_amt_365d` DECIMAL(38,18) COMMENT '最近365天其他售后金额',
  `other_after_sale_amt_his` DECIMAL(38,18) COMMENT '历史所有其他售后金额',
  `self_after_sale_amt_7d` DECIMAL(38,18) COMMENT '最近7天自营品牌售后金额',
  `self_after_sale_amt_14d` DECIMAL(38,18) COMMENT '最近14天自营品牌售后金额',
  `self_after_sale_amt_30d` DECIMAL(38,18) COMMENT '最近30天自营品牌售后金额',
  `self_after_sale_amt_60d` DECIMAL(38,18) COMMENT '最近60天自营品牌售后金额',
  `self_after_sale_amt_180d` DECIMAL(38,18) COMMENT '最近180天自营品牌售后金额',
  `self_after_sale_amt_365d` DECIMAL(38,18) COMMENT '最近365天自营品牌售后金额',
  `self_after_sale_amt_his` DECIMAL(38,18) COMMENT '历史所有自营品牌售后金额',
  `timing_after_sale_amt_7d` DECIMAL(38,18) COMMENT '最近7天省心送售后金额',
  `timing_after_sale_amt_14d` DECIMAL(38,18) COMMENT '最近14天省心送售后金额',
  `timing_after_sale_amt_30d` DECIMAL(38,18) COMMENT '最近30天省心送售后金额',
  `timing_after_sale_amt_60d` DECIMAL(38,18) COMMENT '最近60天省心送售后金额',
  `timing_after_sale_amt_180d` DECIMAL(38,18) COMMENT '最近180天省心送售后金额',
  `timing_after_sale_amt_365d` DECIMAL(38,18) COMMENT '最近365天省心送售后金额',
  `timing_after_sale_amt_his` DECIMAL(38,18) COMMENT '历史所有省心送售后金额',
  `first_delivery_time` DATETIME COMMENT '最早一次配送时间，格式为年月日时分秒',
  `last_delivery_time` DATETIME COMMENT '最近一次配送时间，格式为年月日时分秒',
  `delivery_real_amt_7d` DECIMAL(38,18) COMMENT '最近7天履约实付金额',
  `delivery_real_amt_14d` DECIMAL(38,18) COMMENT '最近14天履约实付金额',
  `delivery_real_amt_30d` DECIMAL(38,18) COMMENT '最近30天履约实付金额',
  `delivery_real_amt_60d` DECIMAL(38,18) COMMENT '最近60天履约实付金额',
  `delivery_real_amt_180d` DECIMAL(38,18) COMMENT '最近180天履约实付金额',
  `delivery_real_amt_365d` DECIMAL(38,18) COMMENT '最近365天履约实付金额',
  `delivery_real_amt_his` DECIMAL(38,18) COMMENT '历史所有履约实付金额',
  `delivery_cost_amt_7d` DECIMAL(38,18) COMMENT '最近7天履约成本金额',
  `delivery_cost_amt_14d` DECIMAL(38,18) COMMENT '最近14天履约成本金额',
  `delivery_cost_amt_30d` DECIMAL(38,18) COMMENT '最近30天履约成本金额',
  `delivery_cost_amt_60d` DECIMAL(38,18) COMMENT '最近60天履约成本金额',
  `delivery_cost_amt_180d` DECIMAL(38,18) COMMENT '最近180天履约成本金额',
  `delivery_cost_amt_365d` DECIMAL(38,18) COMMENT '最近365天履约成本金额',
  `delivery_cost_amt_his` DECIMAL(38,18) COMMENT '历史所有履约成本金额',
  `delivery_point_cnt_7d` BIGINT COMMENT '最近7天配送点位数',
  `delivery_point_cnt_14d` BIGINT COMMENT '最近14天配送点位数',
  `delivery_point_cnt_30d` BIGINT COMMENT '最近30天配送点位数',
  `delivery_point_cnt_60d` BIGINT COMMENT '最近60天配送点位数',
  `delivery_point_cnt_180d` BIGINT COMMENT '最近180天配送点位数',
  `delivery_point_cnt_365d` BIGINT COMMENT '最近365天配送点位数',
  `delivery_point_cnt_his` BIGINT COMMENT '历史所有配送点位数',
  `delivery_days_7d` BIGINT COMMENT '最近7天配送频次（配送天数）',
  `delivery_days_14d` BIGINT COMMENT '最近14天配送频次（配送天数）',
  `delivery_days_30d` BIGINT COMMENT '最近30天配送频次（配送天数）',
  `delivery_days_60d` BIGINT COMMENT '最近60天配送频次（配送天数）',
  `delivery_days_180d` BIGINT COMMENT '最近180天配送频次（配送天数）',
  `delivery_days_365d` BIGINT COMMENT '最近365天配送频次（配送天数）',
  `delivery_days_his` BIGINT COMMENT '历史所有配送频次（配送天数）',
  `delivery_point_amt_7d` DECIMAL(38,18) COMMENT '最近7天单点配送金额',
  `delivery_point_amt_14d` DECIMAL(38,18) COMMENT '最近14天单点配送金额',
  `delivery_point_amt_30d` DECIMAL(38,18) COMMENT '最近30天单点配送金额',
  `delivery_point_amt_60d` DECIMAL(38,18) COMMENT '最近60天单点配送金额',
  `delivery_point_amt_180d` DECIMAL(38,18) COMMENT '最近180天单点配送金额',
  `delivery_point_amt_365d` DECIMAL(38,18) COMMENT '最近365天