CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_area_warehouse_cost_mi` (
  `month` STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
  `service_area` STRING COMMENT '服务区域，枚举值包括：华东、昆明、福建、华北等',
  `warehouse_no` BIGINT COMMENT '仓库编号，唯一标识一个仓库，取值范围：2-155',
  `warehouse_name` STRING COMMENT '仓库名称，如：上海总仓、昆明总仓、福州总仓等',
  `in_quality` BIGINT COMMENT '入库件数，统计周期内的入库商品数量',
  `out_quality` BIGINT COMMENT '出库件数，统计周期内的出库商品数量',
  `on_quality` BIGINT COMMENT '在库件数，统计周期末的在库商品库存数量',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储成本，统计周期内的仓储费用',
  `delivery_amt` DECIMAL(38,18) COMMENT '配送GMV，统计周期内的配送商品总价值'
)
COMMENT '库存仓维度仓储成本月表：按月份、区域、仓库维度统计的仓储成本和配送GMV数据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMM，如202509表示2025年9月'
)
STORED AS ALIORC
TBLPROPERTIES ('comment'='库存仓维度仓储成本月表，包含各仓库的出入库数量、库存量、仓储成本和配送GMV等关键指标')
LIFECYCLE 30;