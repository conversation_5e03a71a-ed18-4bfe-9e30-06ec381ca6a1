CREATE TABLE IF NOT EXISTS app_crm_bd_big_cust_month_gmv_mi(
	bd_id BIGINT COMMENT 'BD编号，唯一标识一个BD，取值范围：-1表示无BD，正数表示有效BD编号',
	bd_name STRING COMMENT 'BD姓名',
	big_cust STRING COMMENT '大客户公司全称',
	big_cust_alias STRING COMMENT '大客户品牌名称/简称',
	merchant_total_gmv DECIMAL(38,18) COMMENT '总GMV业绩金额（单位：元），包含所有支付方式',
	credit_paid_gmv DECIMAL(38,18) COMMENT '账期支付GMV业绩金额（单位：元），指采用账期结算方式的业绩',
	cash_settlement_gmv DECIMAL(38,18) COMMENT '现结支付GMV业绩金额（单位：元），指采用现结结算方式的业绩',
	merchant_total_gmv_ex DECIMAL(38,18) COMMENT '除茶百道外的总GMV业绩金额（单位：元）',
	credit_paid_gmv_ex DECIMAL(38,18) COMMENT '除茶百道外的账期支付GMV业绩金额（单位：元）',
	cash_settlement_gmv_ex DECIMAL(38,18) COMMENT '除茶百道外的现结支付GMV业绩金额（单位：元）'
) 
COMMENT '大客户本月GMV业绩区分账期现结统计表，按BD和大客户维度统计当月GMV业绩，区分账期和现结两种支付方式，并提供除茶百道外的专项统计'
PARTITIONED BY (ym STRING COMMENT '分区字段，yyyyMM格式，表示年月') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='大客户GMV业绩月度统计表，用于BD业绩分析和客户贡献度评估') 
LIFECYCLE 30;