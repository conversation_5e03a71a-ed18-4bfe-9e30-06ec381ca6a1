CREATE TABLE IF NOT EXISTS app_crm_merchant_day_gmv_di_extra_h(
	cust_id BIGINT COMMENT '商户ID，唯一标识商户',
	cust_name STRING COMMENT '商户名称',
	city_id BIGINT COMMENT '商户所在运营区域ID',
	merchant_total_gmv DECIMAL(38,18) COMMENT '商户总GMV（Gross Merchandise Volume，商品交易总额）',
	distribution_gmv DECIMAL(38,18) COMMENT '配送GMV，配送业务的商品交易总额',
	delivery_unit_price DECIMAL(38,18) COMMENT '配送客单价，平均每单配送金额',
	distribution_amout BIGINT COMMENT '配送次数，配送订单数量',
	fruit_gmv DECIMAL(38,18) COMMENT '鲜果品类GMV',
	dairy_gmv DECIMAL(38,18) COMMENT '乳制品品类GMV',
	non_dairy_gmv DECIMAL(38,18) COMMENT '非乳制品品类GMV',
	brand_gmv DECIMAL(38,18) COMMENT '自营品牌GMV',
	reward_gmv DECIMAL(38,18) COMMENT '奖励SKU的GMV',
	core_merchant_tag BIGINT COMMENT '核心客户标记：0-否，1-是',
	bd_id BIGINT COMMENT 'BD编号，负责该商户的业务发展人员ID，公海为0',
	bd_name STRING COMMENT 'BD名称，负责该商户的业务发展人员姓名',
	l1 DECIMAL(38,18) COMMENT 'L1指标，具体业务含义需结合业务场景',
	l2 DECIMAL(38,18) COMMENT 'L2指标，具体业务含义需结合业务场景',
	sku_num BIGINT COMMENT '本月累计下单商品种类数（SKU数量）',
	spu_num BIGINT COMMENT '本月累计下单SPU种类数（标准产品单元数量）',
	thirty_days_order_spu BIGINT COMMENT '30天内下单SPU数量',
	thirty_sixty_days_order_spu BIGINT COMMENT '30-60天内下单SPU数量',
	seven_days_fruit_gmv DECIMAL(38,18) COMMENT '7天鲜果GMV',
	seven_days_dairy_gmv DECIMAL(38,18) COMMENT '7天乳制品GMV',
	seven_days_brand_gmv DECIMAL(38,18) COMMENT '7天自营品牌GMV',
	thirty_days_fruit_gmv DECIMAL(38,18) COMMENT '30天鲜果GMV',
	thirty_days_dairy_gmv DECIMAL(38,18) COMMENT '30天乳制品GMV',
	thirty_days_brand_gmv DECIMAL(38,18) COMMENT '30天自营品牌GMV',
	browsing_history STRING COMMENT '最近一次点击商品详情页的商品信息，JSON格式',
	agent_gmv DECIMAL(38,18) COMMENT '代售GMV，代理销售业务的商品交易总额',
	province STRING COMMENT '商户所在省份',
	city STRING COMMENT '商户所在城市',
	area STRING COMMENT '商户所在区域'
) 
COMMENT '商户本月GMV统计表（当日小时级更新），包含商户交易数据、品类分布、地域信息和业务人员关联关系'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='商户GMV小时级更新表，用于实时监控商户交易情况和业务表现') 
LIFECYCLE 32;