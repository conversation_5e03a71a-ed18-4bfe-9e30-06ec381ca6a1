CREATE TABLE IF NOT EXISTS app_cust_order_sku_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd',
	`register_province` STRING COMMENT '客户注册省份',
	`register_city` STRING COMMENT '客户注册城市',
	`register_area` STRING COMMENT '客户注册区域',
	`cust_class` STRING COMMENT '客户类型：大客户（非茶百道）、普通（非品牌）等',
	`brand_alias` STRING COMMENT '品牌别名：益禾堂浙江江苏、无等',
	`order_type` STRING COMMENT '订单类型：省心送、其他',
	`sku_id` STRING COMMENT 'SKU编号，商品最小库存单位标识',
	`spu_name` STRING COMMENT '商品名称',
	`spu_disc` STRING COMMENT '商品描述，包含规格、等级等信息',
	`category_1` STRING COMMENT '一级类目：鲜果、乳制品等',
	`category_4` STRING COMMENT '四级类目：橙、蜜瓜、无盐黄油、凤梨丨菠萝、柠檬等',
	`after_sale_sku_cnt` BIGINT COMMENT '售后SKU数量，取值范围：0-1',
	`after_sale_cnt` BIGINT COMMENT '售后次数，取值范围：0-3',
	`after_sale_amt` DECIMAL(38,18) COMMENT '售后金额，单位：元',
	`real_total_amt` DECIMAL(38,18) COMMENT '配送实付金额，单位：元',
	`conpon_amt` DECIMAL(38,18) COMMENT '优惠券金额，单位：元'
) 
COMMENT '客户+SKU售后明细表（含未到货），记录客户订单的售后相关信息'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='客户+SKU售后明细表，包含客户注册信息、商品信息、售后数据等详细信息') 
LIFECYCLE 30;