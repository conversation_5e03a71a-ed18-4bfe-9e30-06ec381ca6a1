CREATE TABLE IF NOT EXISTS app_log_mall_sku_di(
    `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计日期',
    `module_name` STRING COMMENT '模块名称，如：总量、首页、分类页、搜索页等',
    `page_name_chs` STRING COMMENT '页面路径名称，如：总量、商品详情页、购物车页等',
    `sku_id` STRING COMMENT '商品SKU ID，唯一标识一个具体规格的商品',
    `spu_id` BIGINT COMMENT '商品SPU ID，标识同一类商品的不同规格',
    `spu_name` STRING COMMENT '商品SPU名称，即商品通用名称',
    `sku_spec` STRING COMMENT '商品规格描述，如：5KG*1箱、1L*1盒等',
    `sku_type` STRING COMMENT '商品类型，取值范围：自营、代仓、代售',
    `category1` STRING COMMENT '商品一级分类，如：乳制品、其他等',
    `category2` STRING COMMENT '商品二级分类，如：乳制品、饮料、包材等',
    `category3` STRING COMMENT '商品三级分类，如：黄油、植物蛋白饮料、饮品杯丨瓶等',
    `category4` STRING COMMENT '商品四级分类，如：乳酸黄油、其他植物蛋白饮料、混合黄油、杯套等',
    `sku_exposure_pv` BIGINT COMMENT '商品曝光PV，商品在页面中被展示的总次数',
    `sku_exposure_uv` BIGINT COMMENT '商品曝光UV，看到该商品的独立用户数',
    `sku_click_pv` BIGINT COMMENT '商品点击PV，用户点击商品的总次数',
    `sku_click_uv` BIGINT COMMENT '商品点击UV，点击该商品的独立用户数',
    `sku_detail_exposure_pv` BIGINT COMMENT '商品详情页曝光PV，商品详情页被展示的总次数',
    `sku_detail_exposure_uv` BIGINT COMMENT '商品详情页曝光UV，看到商品详情页的独立用户数',
    `sku_detail_cart_buy_pv` BIGINT COMMENT '商品详情页加购点击PV，在详情页点击加购按钮的总次数',
    `sku_detail_cart_buy_uv` BIGINT COMMENT '商品详情页加购点击UV，在详情页点击加购按钮的独立用户数',
    `sku_detail_instant_buy_pv` BIGINT COMMENT '商品详情页立即购买点击PV，在详情页点击立即购买按钮的总次数',
    `sku_detail_instant_buy_uv` BIGINT COMMENT '商品详情页立即购买点击UV，在详情页点击立即购买按钮的独立用户数',
    `sku_arouse_click_pv` BIGINT COMMENT '商品唤起点击PV，通过其他入口唤起商品页面的点击总次数',
    `sku_arouse_click_uv` BIGINT COMMENT '商品唤起点击UV，通过其他入口唤起商品页面的独立用户数',
    `sku_arouse_cart_buy_pv` BIGINT COMMENT '商品唤起加购点击PV，通过唤起入口点击加购按钮的总次数',
    `sku_arouse_cart_buy_uv` BIGINT COMMENT '商品唤起加购点击UV，通过唤起入口点击加购按钮的独立用户数',
    `sku_arouse_instant_buy_pv` BIGINT COMMENT '商品唤起立即购买点击PV，通过唤起入口点击立即购买按钮的总次数',
    `sku_arouse_instant_buy_uv` BIGINT COMMENT '商品唤起立即购买点击UV，通过唤起入口点击立即购买按钮的独立用户数',
    `sku_cart_buy_pv` BIGINT COMMENT '商品加购PV，所有加购行为的总次数（包含详情页和唤起入口）',
    `sku_cart_buy_uv` BIGINT COMMENT '商品加购UV，所有加购行为的独立用户数',
    `sku_instant_buy_pv` BIGINT COMMENT '商品立即购买PV，所有立即购买行为的总次数',
    `sku_instant_buy_uv` BIGINT COMMENT '商品立即购买UV，所有立即购买行为的独立用户数',
    `sku_cart_instant_buy_pv` BIGINT COMMENT '商品加购+立即购买PV，加购和立即购买行为的总次数',
    `sku_cart_instant_buy_uv` BIGINT COMMENT '商品加购+立即购买UV，加购和立即购买行为的独立用户数',
    `sku_expand_pay_pv` BIGINT COMMENT '商品拓展购买支付PV，拓展购买场景下的支付次数',
    `sku_expand_pay_uv` BIGINT COMMENT '商品拓展购买支付UV，拓展购买场景下的支付用户数',
    `sku_timing_pay_pv` BIGINT COMMENT '商品省心送支付PV，省心送服务的支付次数',
    `sku_timing_pay_uv` BIGINT COMMENT '商品省心送支付UV，省心送服务的支付用户数',
    `sku_pay_pv` BIGINT COMMENT '商品确认支付PV，确认支付按钮的点击次数',
    `sku_pay_uv` BIGINT COMMENT '商品确认支付UV，确认支付按钮的点击用户数',
    `sku_remind_pv` BIGINT COMMENT '商品提醒PV，所有提醒功能的使用总次数',
    `sku_remind_uv` BIGINT COMMENT '商品提醒UV，使用提醒功能的独立用户数',
    `sku_arrival_remind_pv` BIGINT COMMENT '商品到货提醒PV，到货提醒功能的使用次数',
    `sku_arrival_remind_uv` BIGINT COMMENT '商品到货提醒UV，使用到货提醒功能的独立用户数',
    `sku_arouse_remind_pv` BIGINT COMMENT '商品唤起提醒PV，通过唤起入口使用提醒功能的次数',
    `sku_arouse_remind_uv` BIGINT COMMENT '商品唤起提醒UV，通过唤起入口使用提醒功能的独立用户数',
    `sku_order_order_cnt` BIGINT COMMENT '购买次数，该商品被购买的总次数',
    `sku_order_cust_cnt` BIGINT COMMENT '购买人数，购买该商品的独立用户数',
    `login_uv` BIGINT COMMENT '登陆UV，登录商城的独立用户数',
    `login_pv` BIGINT COMMENT '登陆PV，登录商城的总次数'
)
COMMENT '商城SKU流量分析表，记录商品维度的流量和转化数据，采用新统计逻辑（20230523开始生效）'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期')
STORED AS ALIORC
TBLPROPERTIES (
    'comment'='商城商品SKU维度流量行为分析表，包含曝光、点击、加购、购买等全链路转化指标',
    'columnar.nested.type'='true'
)
LIFECYCLE 30;