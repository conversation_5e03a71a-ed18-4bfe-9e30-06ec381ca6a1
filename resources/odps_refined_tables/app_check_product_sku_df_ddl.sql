CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_check_product_sku_df` (
  `sku` STRING COMMENT '货品SKU编码，唯一标识一个具体货品，示例值：1387607250433、1387607250500、1387882653583'
)
COMMENT '业务数据校验——货品SKU校验表，用于校验货品SKU数据的完整性和唯一性'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示数据所属的业务日期，示例：20250917'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '业务数据校验——货品SKU校验表，存储所有需要校验的货品SKU数据',
  'lifecycle' = '30'
);