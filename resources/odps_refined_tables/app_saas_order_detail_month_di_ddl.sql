CREATE TABLE IF NOT EXISTS app_saas_order_detail_month_di(
	`tenant_id` BIGINT COMMENT '租户ID',
	`tenant_name` STRING COMMENT '租户名称',
	`store_id` BIGINT COMMENT '店铺ID',
	`store_name` STRING COMMENT '店铺名称',
	`account_id` BIGINT COMMENT '下单账号ID',
	`account_name` STRING COMMENT '下单账号名称',
	`address` STRING COMMENT '下单地址',
	`order_no` STRING COMMENT '订单编号',
	`order_id` BIGINT COMMENT '订单流水号',
	`order_item_id` BIGINT COMMENT '订单项流水号',
	`create_time` DATETIME COMMENT '下单时间，格式：年月日时分秒',
	`delivery_time` DATETIME COMMENT '配送时间，格式：年月日时分秒',
	`supplier_sku_id` BIGINT COMMENT '供应商SKU ID',
	`title` STRING COMMENT '商品名称',
	`sku_type` STRING COMMENT '商品类型，枚举值：鲜沐等',
	`amount` BIGINT COMMENT '商品数量',
	`payable_price` DECIMAL(38,18) COMMENT '商品单价',
	`total_price` DECIMAL(38,18) COMMENT '商品实付总价',
	`delivery_fee` DECIMAL(38,18) COMMENT '配送费',
	`after_sale_price` DECIMAL(38,18) COMMENT '售后金额（除配送费）',
	`after_sale_delivery_fee` DECIMAL(38,18) COMMENT '售后配送费',
	`after_sale_order_no` STRING COMMENT '售后订单编号，枚举值：None表示无售后',
	`after_sale_time` DATETIME COMMENT '售后完成时间，格式：年月日时分秒，NaT表示无售后',
	`after_sale_amount` STRING COMMENT '售后商品数量，枚举值：None表示无售后',
	`after_sale_type` STRING COMMENT '售后类型，枚举值：None表示无售后',
	`after_sale_service_type` STRING COMMENT '售后服务类型，枚举值：None表示无售后',
	`reason` STRING COMMENT '售后原因，枚举值：None表示无售后',
	`is_credit_paid` BIGINT COMMENT '是否账期订单，枚举值：1-是，0-否'
) 
COMMENT 'SaaS订单财务用表，包含订单基本信息、商品信息、售后信息等财务相关数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：yyyyMMdd') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='SaaS订单财务明细表，用于财务对账和分析') 
LIFECYCLE 30;