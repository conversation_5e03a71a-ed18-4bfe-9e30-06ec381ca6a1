```sql
CREATE TABLE IF NOT EXISTS app_crm_bd_consign_performance_mi(
    month STRING COMMENT '月份，格式：yyyyMM',
    bd_id BIGINT COMMENT '销售ID，唯一标识一个销售人员',
    bd_name STRING COMMENT '销售姓名',
    administrative_city STRING COMMENT '销售所属行政城市',
    zone_name STRING COMMENT '区域名称',
    m1 STRING COMMENT '城市负责人（M1管理者）姓名',
    m2 STRING COMMENT '区域负责人（M2管理者）姓名', 
    m3 STRING COMMENT '部门负责人（M3管理者）姓名',
    cust_type STRING COMMENT '客户类型；枚举值：面包蛋糕、西餐、咖啡、甜品冰淇淋、茶饮、其他',
    order_cust_cnt BIGINT COMMENT '下单客户数',
    order_sku_cnt BIGINT COMMENT '销量（商品数量）',
    order_cnt BIGINT COMMENT '订单数',
    real_total_amt DECIMAL(38,18) COMMENT '订单实付金额',
    drop_in_visit_cust_cnt BIGINT COMMENT '上门拜访客户数（上门/有效拜访）',
    visit_cust_cnt BIGINT COMMENT '总拜访客户数'
)
COMMENT 'BD粒度代售业绩报表月汇总表，记录销售人员每月的业绩表现和拜访情况'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据日期')
STORED AS ALIORC
TBLPROPERTIES ('comment'='BD代售业绩月汇总表，包含销售业绩指标和客户拜访统计')
LIFECYCLE 30;
```