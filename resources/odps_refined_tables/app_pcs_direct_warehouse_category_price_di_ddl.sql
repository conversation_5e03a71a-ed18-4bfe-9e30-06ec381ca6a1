CREATE TABLE IF NOT EXISTS app_pcs_direct_warehouse_category_price_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务发生日期',
	`warehouse_no` BIGINT COMMENT '库存仓号，取值范围：10-155，常见值：10(嘉兴总仓)、24(华西总仓)、69(东莞总仓)、125(南京总仓)',
	`warehouse_name` STRING COMMENT '库存仓名称，如：嘉兴总仓、东莞总仓、华西总仓、南京总仓等',
	`category4` STRING COMMENT '四级类目，如：金桔、柠檬、蜜瓜等水果品类',
	`purchase_no` STRING COMMENT '采购单号，格式为年月日+数字序列',
	`sku_id` STRING COMMENT 'SKU编号，商品唯一标识',
	`spu_name` STRING COMMENT '商品名称，如：海南小金桔、四川尤力克黄柠檬、西州蜜哈密瓜等',
	`sku_disc` STRING COMMENT '商品描述，包含规格、等级、重量等信息',
	`purchase_sku_cnt` BIGINT COMMENT '采购数量，取值范围：1-300，均值约50，常见值：5、10、20、30、50、249、300',
	`purchase_amt` DECIMAL(38,18) COMMENT '采购金额，单位为元，保留18位小数精度',
	`direct_avg_price` DECIMAL(38,18) COMMENT '直采成本单价，单位为元/单位，保留18位小数精度',
	`purchase_avg_price` DECIMAL(38,18) COMMENT '直采成本单价，单位为元/单位，保留18位小数精度（当前数据中该字段值均为0）'
) 
COMMENT '直采采购数据明细表，记录直采业务的采购订单明细数据，包括采购商品信息、数量、金额和成本单价等'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据加载日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='直采采购数据明细表，用于分析直采业务的采购情况和成本结构') 
LIFECYCLE 30;