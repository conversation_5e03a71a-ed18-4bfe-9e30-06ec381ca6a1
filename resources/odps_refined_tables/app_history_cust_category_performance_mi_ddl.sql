```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_history_cust_category_performance_mi` (
  `bd_id` BIGINT COMMENT 'BD ID，业务发展人员的唯一标识',
  `bd_name` STRING COMMENT '最新归属BD姓名',
  `is_test_bd` BIGINT COMMENT '是否测试BD：1-是，0-否',
  `bd_region` STRING COMMENT '大区名称',
  `bd_work_zone` STRING COMMENT '区域名称',
  `cust_id` BIGINT COMMENT '客户ID，客户的唯一标识',
  `cust_name` STRING COMMENT '客户名称',
  `cust_type` STRING COMMENT '客户类型：存量-现有客户，新增-新客户',
  `cust_type_detail` STRING COMMENT '客户详细类型：拉新-新拉客户，老客-老客户',
  `order_source` STRING COMMENT '订单来源：鲜沐-鲜沐平台，SAAS-SAAS平台',
  `category1` STRING COMMENT '一级类目名称',
  `spu_group` STRING COMMENT '推广品类名称',
  `is_dlv_payment` BIGINT COMMENT '是否履约结算：1-履约结算，0-交易结算',
  `max_months` STRING COMMENT '最近履约(交易)月份，格式：yyyyMM',
  `last_months` STRING COMMENT '非本月的最近履约(交易)月份，格式：yyyyMM',
  `mtd_dlv_ori_amt` DECIMAL(38,18) COMMENT '本月履约(交易)应付GMV金额',
  `mtd_dlv_real_amt` DECIMAL(38,18) COMMENT '本月履约(交易)实付GMV金额',
  `mtd_dlv_sku_cnt` BIGINT COMMENT '本月履约(交易)商品件数',
  `mtd_big_sku_cnt` DECIMAL(38,18) COMMENT '本月大规格履约(交易)商品件数',
  `is_completed` BIGINT COMMENT '是否完成该品类任务：1-是，0-否',
  `last_dlv_ori_amt` DECIMAL(38,18) COMMENT '非本月履约应付GMV金额_最近月份',
  `last_dlv_real_amt` DECIMAL(38,18) COMMENT '非本月履约实付GMV金额_最近月份',
  `last_dlv_sku_cnt` BIGINT COMMENT '非本月履约商品件数_最近月份',
  `last_big_sku_cnt` DECIMAL(38,18) COMMENT '非本月大规格履约商品件数_最近月份'
) 
COMMENT '拉新客户推广品类本月表现表，记录BD拉新客户在各推广品类的月度业绩表现数据'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，数据日期，格式：yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '拉新客户推广品类本月表现表',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 365
```