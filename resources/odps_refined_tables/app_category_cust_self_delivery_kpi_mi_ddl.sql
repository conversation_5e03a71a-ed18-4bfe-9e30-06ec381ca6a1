```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_category_cust_self_delivery_kpi_mi`(
  `month` STRING COMMENT '日期，格式为yyyyMM，表示年月',
  `category` STRING COMMENT '商品类型：鲜果、乳制品、其他',
  `cust_team` STRING COMMENT '客户团队类型：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户',
  `origin_total_amt` DECIMAL(38,18) COMMENT '原始总金额，订单原始金额合计',
  `real_total_amt` DECIMAL(38,18) COMMENT '应付总金额，实际应付金额合计',
  `cost_amt` DECIMAL(38,18) COMMENT '成本，总成本金额',
  `timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送原始总金额，省心送服务的原始金额',
  `timing_real_total_amt` DECIMAL(38,18) COMMENT '省心送实际总金额，省心送服务的实际金额',
  `cust_cnt` BIGINT COMMENT '客户数，去重客户数量',
  `cust_unit_amt` DECIMAL(38,18) COMMENT '客户单价，平均每个客户的金额',
  `order_cnt` BIGINT COMMENT '订单数，总订单数量',
  `point_cnt` BIGINT COMMENT '点位数，服务点位总数',
  `day_point_cnt` BIGINT COMMENT '日点位数，每日活跃点位数量',
  `sku_cnt` BIGINT COMMENT 'SKU数量，商品品类数量',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费，配送费用总额',
  `cust_runoff` DECIMAL(38,18) COMMENT '客户流失率，客户流失比例',
  `after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后总金额，售后已收货金额',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储成本，仓储费用',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本，干线运输费用',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送成本，末端配送费用',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提成本，自提相关费用',
  `other_amt` DECIMAL(38,18) COMMENT '其他成本，其他杂项费用',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨成本，调拨运输费用'
) 
COMMENT '履约口径客户、商品类型维度KPI指标日汇总表，包含各类成本和业务指标数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='履约业务KPI指标汇总表，按客户类型和商品类型维度统计') 
LIFECYCLE 30;
```