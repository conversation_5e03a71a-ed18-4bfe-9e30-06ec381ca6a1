CREATE TABLE IF NOT EXISTS app_scp_lt_change_record_df(
	sku STRING COMMENT '商品SKU，商品唯一标识编码',
	spu STRING COMMENT '商品SPU，标准产品单元编码',
	pd_name STRING COMMENT '商品名称，商品的完整描述名称',
	weight STRING COMMENT '商品规格，包括重量、尺寸等规格信息',
	sub_type BIGINT COMMENT '商品性质：1-普通商品，2-特殊商品，3-危险品，4-易碎品，5-冷链商品',
	warehouse_no BIGINT COMMENT '仓库编号，存储商品的仓库唯一标识',
	warehouse_name STRING COMMENT '仓库名称，存储商品的具体仓库名称',
	supplier_id BIGINT COMMENT '供应商ID，供应商的唯一标识编码',
	supplier STRING COMMENT '供应商名称，提供商品的供应商公司名称',
	purchaser_id BIGINT COMMENT '采购员ID，采购人员的唯一标识编码',
	purchaser STRING COMMENT '采购员姓名，负责采购商品的人员姓名',
	planer STRING COMMENT '计划员姓名，负责商品计划和调度的人员姓名',
	channel_type BIGINT COMMENT '渠道类型：1-线上渠道，2-线下渠道，3-批发渠道，4-零售渠道，5-跨境渠道',
	change_type STRING COMMENT '变更类型：Added-新增记录，Deleted-删除记录，Modified-修改记录',
	prev_advance_day BIGINT COMMENT '前一日提前天数，变更前的供应商提前天数',
	curr_advance_day BIGINT COMMENT '当前提前天数，变更后的供应商提前天数',
	fresh_date DATETIME COMMENT '数据日期，数据记录的具体时间，格式为年月日时分秒'
) 
PARTITIONED BY (ds STRING COMMENT '分区字段，数据加载日期，格式为yyyyMMdd') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='供应商提前期变更记录表，记录供应商提前天数的变更历史，包括新增、删除和修改操作') 
LIFECYCLE 30;