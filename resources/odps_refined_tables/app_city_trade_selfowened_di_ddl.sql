```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_city_trade_selfowened_di` (
  `date` STRING COMMENT '交易日期，格式：yyyyMMdd',
  `register_province` STRING COMMENT '客户注册省份',
  `register_city` STRING COMMENT '客户注册城市',
  `register_area` STRING COMMENT '客户注册区域',
  `cust_type` STRING COMMENT '客户类型；枚举值：面包蛋糕,西餐,咖啡,甜品冰淇淋,茶饮,其他',
  `brand_type` STRING COMMENT '大客户类型；枚举值：大客户,普通,批发客户（注：历史枚举值单店,批发大客户,普通大客户,KA大客户已弃用）',
  `brand_name` STRING COMMENT '品牌名称',
  `category1` STRING COMMENT '商品一级类目',
  `category2_id` STRING COMMENT '商品二级类目ID',
  `category2` STRING COMMENT '商品二级类目名称',
  `category3_id` STRING COMMENT '商品三级类目ID',
  `category3` STRING COMMENT '商品三级类目名称',
  `category4_id` STRING COMMENT '商品四级类目ID',
  `category4` STRING COMMENT '商品四级类目名称',
  `origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额（原始订单金额）',
  `real_total_amt` DECIMAL(38,18) COMMENT '实付总金额（实际支付金额）',
  `cust_cnt` BIGINT COMMENT '客户数量（统计周期内去重客户数）',
  `new_cust_cnt` BIGINT COMMENT '新客户数量（历史截止当天首次下单的客户数）',
  `order_time_cnt` DECIMAL(38,18) COMMENT '下单时间间隔总和，单位：分钟',
  `order_time_avg` DECIMAL(38,18) COMMENT '平均下单时间间隔，单位：分钟'
)
COMMENT '自营品牌城市整体交易数据日表：统计自营品牌在各城市的交易数据，包括客户类型、品牌类型、商品类目、交易金额和客户数量等维度'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '自营品牌城市交易数据日表，用于分析城市维度的交易趋势和客户行为',
  'lifecycle' = '30'
);
```