```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_platform_kpi_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务日期',
  `origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额，单位为元，保留18位小数精度',
  `real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额，单位为元，保留18位小数精度',
  `cust_cnt` BIGINT COMMENT '履约客户数，统计当日完成履约的客户数量',
  `preferential_amt` DECIMAL(38,18) COMMENT '营销金额，单位为元，表示营销活动产生的优惠金额',
  `timing_origin_amt` DECIMAL(38,18) COMMENT '省心送履约应付GMV，单位为元，省心送业务的应付总金额',
  `timing_cust_cnt` BIGINT COMMENT '省心送履约客户数，统计当日省心送业务完成履约的客户数量',
  `timing_ratio` DECIMAL(38,18) COMMENT '省心送合单率，取值范围0-1，表示省心送业务的合单比例',
  `timing_cust_30_not_cnt` BIGINT COMMENT '省心送超过30天未履约客户数，统计省心送业务中超过30天未履约的客户数量',
  `timing_cust_30_90_cnt` BIGINT COMMENT '省心送在30天-90天履约客户数，统计省心送业务中在30-90天内履约的客户数量',
  `login_uv` BIGINT COMMENT '登陆UV，统计当日独立登录用户数',
  `order_uv` BIGINT COMMENT '交易客户数，统计当日完成交易的独立用户数',
  `activity_uv` BIGINT COMMENT '特价点击UV，统计当日点击特价活动的独立用户数',
  `activity_order_uv` BIGINT COMMENT '特价下单人数，统计当日在特价活动中下单的独立用户数',
  `exchange_uv` BIGINT COMMENT '换购点击UV，统计当日点击换购活动的独立用户数',
  `exchange_order_uv` BIGINT COMMENT '换购下单人数，统计当日在换购活动中下单的独立用户数',
  `expand_uv` BIGINT COMMENT '拓展购买点击UV，统计当日点击拓展购买活动的独立用户数',
  `expand_order_uv` BIGINT COMMENT '拓展购买下单人数，统计当日在拓展购买活动中下单的独立用户数',
  `meeting_uv` BIGINT COMMENT '会场活动页点击UV，统计当日点击会场活动页的独立用户数',
  `meeting_order_uv` BIGINT COMMENT '会场活动页下单人数，统计当日在会场活动页下单的独立用户数',
  `other_uv` BIGINT COMMENT '其他点击UV，统计当日点击其他活动的独立用户数',
  `other_order_uv` BIGINT COMMENT '其他下单人数，统计当日在其他活动中下单的独立用户数',
  `timing_all_cust_cnt` BIGINT COMMENT '省心送总客户数，统计省心送业务的总客户数量'
)
COMMENT '平台KPI指标日粒度统计表，包含履约金额、客户数、各业务线UV和下单人数等核心指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '平台核心KPI指标日粒度统计表，用于监控平台业务表现和运营效果',
  'lifecycle' = '365'
);
```