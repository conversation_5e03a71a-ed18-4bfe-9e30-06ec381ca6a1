CREATE TABLE IF NOT EXISTS app_chatbi_after_sale_summary_df(
	order_no STRING COMMENT '原订单号，关联app_chatbi_cust_orders_df.order_no',
	sku_id STRING COMMENT 'SKU编码，关联app_chatbi_cust_orders_df.sku_id',
	cust_id STRING COMMENT '客户ID，关联app_chatbi_cust_orders_df.cust_id',
	first_after_sale_handle_time STRING COMMENT '首次售后处理时间，格式：yyyy-MM-dd HH:mm:ss，年月日时分秒格式',
	last_after_sale_handle_time STRING COMMENT '最近一次售后处理时间，格式：yyyy-MM-dd HH:mm:ss，年月日时分秒格式',
	after_sale_amount DECIMAL(38,18) COMMENT '售后总金额',
	deliveryed_after_sale_amount DECIMAL(38,18) COMMENT '已配送售后金额（deliveryed=1的售后金额）',
	after_sale_quantity BIGINT COMMENT '售后总数量，取值范围：0-49500',
	after_sale_unit STRING COMMENT '售后单位，枚举值包括：g/kg/盒/箱等',
	after_sales_times BIGINT COMMENT '此SKU售后次数，取值范围：1（当前数据中均为1次）',
	first_after_sale_handle_date STRING COMMENT '首次售后处理日期，格式：yyyyMMdd，年月日格式',
	last_after_sale_handle_date STRING COMMENT '最近一次售后处理日期，格式：yyyyMMdd，年月日格式'
) 
COMMENT '售后订单汇总分析表，用于统计和分析售后订单的相关指标'
PARTITIONED BY (ds STRING COMMENT '分区字段，数据处理日期，yyyyMMdd格式') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='售后订单汇总分析表，包含售后订单的金额、数量、处理时间等关键指标') 
LIFECYCLE 7;