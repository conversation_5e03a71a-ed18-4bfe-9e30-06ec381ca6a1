```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_log_search_detail_di` (
  `time` DATETIME COMMENT '事件发生时间，格式为年月日时分秒',
  `query` STRING COMMENT '用户搜索的关键词',
  `url` STRING COMMENT '页面URL地址',
  `envent_type` STRING COMMENT '事件类型：click-点击，impression-曝光，add_to_cart-加购，buy_now-立即购买，purchase-提交订单，checkout-发起支付，paid-支付完成',
  `idx` STRING COMMENT '商品在搜索结果列表中的位置下标',
  `sku_id` STRING COMMENT '商品SKU ID，商品最小库存单位标识',
  `spu_id` BIGINT COMMENT '商品SPU ID，商品标准产品单位标识',
  `sku_price` STRING COMMENT '商品SKU价格，单位为元',
  `master_order_no` STRING COMMENT '主订单编号，None表示无订单',
  `cid` STRING COMMENT '客户端唯一标识，设备指纹ID',
  `sid` STRING COMMENT '会话ID，单次打开商城的唯一标识',
  `count` BIGINT COMMENT '单次打开页面的埋点计数，记录页面内事件发生次数',
  `user_agent` STRING COMMENT '用户设备信息，包括浏览器、操作系统等，None表示无信息',
  `cust_id` BIGINT COMMENT '客户ID，唯一标识一个客户',
  `cust_name` STRING COMMENT '客户名称',
  `cust_type` STRING COMMENT '客户类型：烘焙、茶饮、咖啡、其他等',
  `default_address` STRING COMMENT '店铺默认地址信息，格式为省市区',
  `is_new` STRING COMMENT '是否当日注册：是-当日注册，否-非当日注册'
)
COMMENT '商城搜索词流量分析明细表，记录用户搜索行为、商品曝光点击、加购购买等全链路行为数据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment'='商城搜索词流量分析明细表，用于分析用户搜索行为转化漏斗和商品流量分布',
  'lifecycle'='30'
);
```