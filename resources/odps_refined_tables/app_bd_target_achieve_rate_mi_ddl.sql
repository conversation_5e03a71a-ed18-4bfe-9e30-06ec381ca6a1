CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_bd_target_achieve_rate_mi` (
  `months` STRING COMMENT '月份，格式为yyyyMM',
  `bd_id` BIGINT COMMENT '业绩归属的BD_ID，销售人员唯一标识',
  `bd_name` STRING COMMENT '业绩归属的销售人员姓名',
  `bd_m1` STRING COMMENT 'BD所属M1管理者姓名',
  `bd_m2` STRING COMMENT 'BD所属M2管理者姓名',
  `bd_m3` STRING COMMENT 'BD所属M3管理者姓名',
  `bd_work_zone` STRING COMMENT 'BD所在销售区域，如：杭州湾、徽京、广州、东莞等',
  `bd_work_city` STRING COMMENT 'BD所在城市，如：湖州市、南京市、广州市、东莞市等',
  `team_tag` STRING COMMENT 'BD所属团队，如：平台销售',
  `is_disabled` STRING COMMENT '账号是否禁用，枚举值：未禁用、已禁用',
  `create_time` STRING COMMENT '账号创建时间，格式为yyyy-MM-dd HH:mm:ss',
  `bd_creat_months` STRING COMMENT '账号创建月份，格式为yyyyMM',
  `no_at_gmv_target` DECIMAL(38,18) COMMENT '非ATGMV目标金额',
  `no_at_gmv_amt` DECIMAL(38,18) COMMENT '非ATGMV实际达成金额',
  `no_at_gmv_achieve_rate` DECIMAL(38,18) COMMENT '非ATGMV达成率',
  `fruit_gmv_target` DECIMAL(38,18) COMMENT '鲜果GMV目标金额',
  `fruit_gmv_amt` DECIMAL(38,18) COMMENT '鲜果GMV实际达成金额',
  `fruit_gmv_achieve_rate` DECIMAL(38,18) COMMENT '鲜果GMV达成率',
  `total_gmv_achieve_rate` DECIMAL(38,18) COMMENT '综合GMV达成率',
  `p_rate` DECIMAL(38,18) COMMENT 'P系数，绩效评估系数',
  `fruit_rate` DECIMAL(38,18) COMMENT '鲜果达成系数，鲜果业务绩效评估系数'
) 
COMMENT '销售目标达成系数表，记录销售人员各项目标达成情况和绩效系数'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，数据日期，格式为yyyyMMdd'
)
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
               'comment'='销售目标达成系数表，包含销售人员的目标达成情况和绩效评估数据') 
LIFECYCLE 30;