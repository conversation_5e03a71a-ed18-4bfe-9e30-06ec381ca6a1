```sql
CREATE TABLE IF NOT EXISTS app_warehouse_sku_health_df(
    `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
    `warehouse_no` BIGINT COMMENT '库存仓号，数值型仓库标识',
    `warehouse_name` STRING COMMENT '库存仓名称，文本型仓库名称',
    `sku_id` STRING COMMENT 'sku编号，商品最小库存单位的唯一标识',
    `category1` STRING COMMENT '一级类目，商品最高层级分类',
    `category2` STRING COMMENT '二级类目，商品次高层级分类',
    `category3` STRING COMMENT '三级类目，商品中层级分类',
    `category4` STRING COMMENT '四级类目，商品最细粒度分类',
    `spu_name` STRING COMMENT '商品名称，标准产品单元的名称',
    `sku_disc` STRING COMMENT '商品规格，描述商品的具体规格参数',
    `sku_propriety` STRING COMMENT 'sku性质：常规，活动，临保，拆包，不卖，破袋。取值范围：常规|活动|临保|拆包|不卖|破袋',
    `sku_type` STRING COMMENT '自营,代仓，代售。取值范围：自营|代仓|代售',
    `sku_life` STRING COMMENT '生命周期：使用中，已删除。取值范围：使用中|已删除',
    `on_sale` STRING COMMENT '上架状态：上架，下架，异常。取值范围：上架|下架|异常',
    `is_big_cust` STRING COMMENT '是否大客户专享：是，否。取值范围：是|否',
    `origin_place` STRING COMMENT '产地，商品生产地信息',
    `is_origin` STRING COMMENT '是否国产：是，否。取值范围：是|否',
    `is_new` STRING COMMENT '是否新品：是，否（sku创建时间60天内算新品）。取值范围：是|否',
    `is_180d_order` STRING COMMENT '近半年是否有动销：是，否（近180天是否有销售）。取值范围：是|否',
    `abc_label` STRING COMMENT '货品ABC分类：A,B,C,无。取值范围：A|B|C|无',
    `season_sign` STRING COMMENT '季节性标识：无,夏季,秋季等。取值范围：无|夏季|秋季|冬季|春季',
    `life_cycle` STRING COMMENT '生命周期阶段：长尾,衰退低,无等。取值范围：长尾|衰退低|无|成长期|成熟期|衰退期',
    `grow_coe` DECIMAL(38,18) COMMENT '成长系数，数值型成长指标',
    `grow_type` STRING COMMENT '成长曲线类型：波动型,高衰退型等',
    `cust_preference` STRING COMMENT '业态偏好：面包蛋糕客户偏好,茶饮客户偏好,咖啡客户偏好等',
    `exposure_type` STRING COMMENT '曝光类别：高曝光,中曝光,低曝光。取值范围：高曝光|中曝光|低曝光',
    `click_type` STRING COMMENT '点击类别：高点击,中点击,低点击。取值范围：高点击|中点击|低点击',
    `elastic_coefficient` DECIMAL(38,18) COMMENT '弹性系数，数值型弹性指标',
    `elastic_coefficient_type` STRING COMMENT '弹性类别：弹性商品,不弹性商品。取值范围：弹性商品|不弹性商品',
    `gross_type` STRING COMMENT '毛利类别：高毛利,中毛利,低毛利。取值范围：高毛利|中毛利|低毛利',
    `order_sku_cnt_type` STRING COMMENT '销量类别：高销量,中销量,低销量。取值范围：高销量|中销量|低销量',
    `order_amt_type` STRING COMMENT 'GMV类别：高GMV,中GMV,低GMV。取值范围：高GMV|中GMV|低GMV',
    `turnover_type` STRING COMMENT '周转类别：高周转,低周转。取值范围：高周转|低周转',
    `store_cost_type` STRING COMMENT '库存金额类别：高库存,中库存,低库存。取值范围：高库存|中库存|低库存',
    `store_order_type` STRING COMMENT '库存应用类标签：高库存高销量,中库存高销量,低库存低销量等',
    `order_gross_type` STRING COMMENT '销售应用类标签：高销量中毛利,高销量低毛利,低销量高毛利等',
    `click_order_type` STRING COMMENT '流量应用类标签：高点击高销量,中点击高销量,低点击低销量等',
    `store_quantity` BIGINT COMMENT '仓库库存，当前仓库中的库存数量',
    `lock_quantit` BIGINT COMMENT '锁定库存，已被锁定不可用的库存数量',
    `safe_quantit` BIGINT COMMENT '安全库存，为应对需求波动而设置的安全库存数量',
    `freeze_quantity` BIGINT COMMENT '冻结库存，因各种原因被冻结的库存数量',
    `ues_store_quantity` BIGINT COMMENT '可用库存，当前可用的库存数量',
    `road_quantity` BIGINT COMMENT '在途库存，总在途库存数量',
    `pcs_on_raod_quantity` BIGINT COMMENT '采购在途库存，采购过程中的在途库存数量',
    `alc_on_raod_quantity` BIGINT COMMENT '调拨在途库存，调拨过程中的在途库存数量',
    `store_amt` DECIMAL(38,18) COMMENT '仓库库存成本，当前仓库库存的总成本金额',
    `ues_store_amt` DECIMAL(38,18) COMMENT '可用库存成本，可用库存的总成本金额',
    `lock_amt` DECIMAL(38,18) COMMENT '锁定库存成本，锁定库存的总成本金额',
    `safe_amt` DECIMAL(38,18) COMMENT '安全库存成本，安全库存的总成本金额',
    `freeze_amt` DECIMAL(38,18) COMMENT '冻结库存成本，冻结库存的总成本金额',
    `on_road_amt` DECIMAL(38,18) COMMENT '在途库存成本，总在途库存的成本金额',
    `purchase_road_amt` DECIMAL(38,18) COMMENT '采购在途库存成本，采购在途库存的成本金额',
    `allocate_road_amt` DECIMAL(38,18) COMMENT '调拨在途库存成本，调拨在途库存的成本金额',
    `nearest_doc_3` DECIMAL(38,18) COMMENT 'doc_近3天，近3天的库存周转天数',
    `nearest_doc_7` DECIMAL(38,18) COMMENT 'doc_近7天，近7天的库存周转天数',
    `nearest_doc_14` DECIMAL(38,18) COMMENT 'doc_近14天，近14天的库存周转天数',
    `future_doc` DECIMAL(38,18) COMMENT 'doc_未来预估，未来预估的库存周转天数',
    `duration_on_shelf` DECIMAL(38,18) COMMENT '上架时长，商品上架的时间长度',
    `duration_sale_out` DECIMAL(38,18) COMMENT '下架时长，商品下架的时间长度',
    `sale_out_rate` DECIMAL(38,18) COMMENT '售罄率，商品售罄的比例',
    `unit_cost` DECIMAL(38,18) COMMENT '单价，单个商品的成本价格',
    `init_store_quantity` BIGINT COMMENT '在库库存(期初)，期初在库库存数量',
    `init_store_amt` DECIMAL(38,18) COMMENT '在库金额(期初)，期初在库库存的成本金额',
    `period_store_cnt_3_1` BIGINT COMMENT '1/3效期内库存数量，效期前1/3时间段内的库存数量',
    `period_store_cnt_3_2` BIGINT COMMENT '2/3效期内库存数量，效期前2/3时间段内的库存数量',
    `period_store_amt_3_1` DECIMAL(38,18) COMMENT '1/3效期内库存成本，效期前1/3时间段内的库存成本',
    `period_store_amt_3_2` DECIMAL(38,18) COMMENT '2/3效期内库存成本，效期前2/3时间段内的库存成本',
    `period_store_cnt_2_1` BIGINT COMMENT '1/2效期内库存数量，效期前1/2时间段内的库存数量',
    `period_store_amt_2_1` DECIMAL(38,18) COMMENT '1/2效期内库存成本，效期前1/2时间段内的库存成本',
    `period_store_cnt_4_3` BIGINT COMMENT '4/3效期内库存数量，效期超过1/3时间段内的库存数量',
    `period_store_amt_4_3` DECIMAL(38,18) COMMENT '4/3效期内库存成本，效期超过1/3时间段内的库存成本',
    `in_bound_store_cnt` BIGINT COMMENT '总入库量，总入库数量',
    `in_purchases_cnt` BIGINT COMMENT '采购入库量，采购入库数量',
    `in_allocate_cnt` BIGINT COMMENT '调拨入库量，调拨入库数量',
    `in_transfer_cnt` BIGINT COMMENT '转换入库量，转换入库数量',
    `in_back_cnt` BIGINT COMMENT '退货入库量，退货入库数量',
    `in_other_cnt` BIGINT COMMENT '其他入库量，其他方式入库数量',
    `in_bound_store_amt` DECIMAL(38,18) COMMENT '总入库成本，总入库的成本金额',
    `in_purchases_amt` DECIMAL(38,18) COMMENT '采购入库成本，采购入库的成本金额',
    `in_allocate_amt` DECIMAL(38,18) COMMENT '调拨入库成本，调拨入库的成本金额',
    `in_transfer_amt` DECIMAL(38,18) COMMENT '转换入库成本，转换入库的成本金额',
    `in_back_amt` DECIMAL(38,18) COMMENT '退货入库成本，退货入库的成本金额',
    `in_other_amt` DECIMAL(38,18) COMMENT '其他入库成本，其他方式入库的成本金额',
    `out_bound_store_cnt` BIGINT COMMENT '总出库量，总出库数量',
    `out_sale_cnt` BIGINT COMMENT '销售出库量（含自提），销售出库数量（包含自提）',
    `out_allocate_cnt` BIGINT COMMENT '调拨出库量，调拨出库数量',
    `out_transfer_cnt` BIGINT COMMENT '转换出库量，转换出库数量',
    `out_damage_cnt` BIGINT COMMENT '货损出库量，货损出库数量',
    `out_other_cnt` BIGINT COMMENT '其他出库量，其他方式出库数量',
    `out_bound_store_amt` DECIMAL(38,18) COMMENT '总出库成本，总出库的成本金额',
    `out_sale_amt` DECIMAL(38,18) COMMENT '销售出库成本（含自提），销售出库的成本金额（包含自提）',
    `out_allocate_amt` DECIMAL(38,18) COMMENT '调拨出库成本，调拨出库的成本金额',
    `out_transfer_amt` DECIMAL(38,18) COMMENT '转换出库成本，转换出库的成本金额',
    `out_damage_amt` DECIMAL(38,18) COMMENT '货损出库成本，货损出库的成本金额',
    `out_other_amt` DECIMAL(38,18) COMMENT '其他出库成本，其他方式出库的成本金额',
    `nearest_7_days_sale_quantity` BIGINT COMMENT '近7天销量数量（含自提），近7天销售数量（包含自提）',
    `nearest_7_days_total_sale_cost` DECIMAL(38,18) COMMENT '近7天总销量成本，近7天销售总成本',
    `nearest_7_days_inventory_quantity` BIGINT COMMENT '近7天库存数量（含自提），近7天库存数量（包含自提）',
    `nearest_7_days_total_inventory_cost` DECIMAL(38,18) COMMENT '近7天总库存成本，近7天库存总成本',
    `nearest_30_days_sale_quantity` BIGINT COMMENT '近30天销量数量（含自提），近30天销售数量（包含自提）',
    `nearest_30_days_total_sale_cost` DECIMAL(38,18) COMMENT '近30天总销量成本，近30天销售总成本',
    `nearest_30_days_inventory_quantity` BIGINT COMMENT '近30天库存数量（含自提），近30天库存数量（包含自提）',
    `nearest_30_days_total_inventory_cost` DECIMAL(38,18) COMMENT '近30天总库存成本，近30天库存总成本',
    `month_sale_quality` BIGINT COMMENT '当月销量（含自提），当月销售数量（包含自提）',
    `month_total_sale_cost` DECIMAL(38,18) COMMENT '当月总销量成本（含自提），当月销售总成本（包含自提）',
    `month_inventory_quantity` BIGINT COMMENT '当月库存数量，当月库存数量',
    `month_inventory_total_cost` DECIMAL(38,18) COMMENT '当月库存成本，当月库存总成本',
    `nearest_3_days_sale_avg` DECIMAL(38,18) COMMENT '近3天日均出库量（含自提），近3天日均出库数量（包含自提）',
    `nearest_7_days_sale_avg` DECIMAL(38,18) COMMENT '近7天日均出库量（含自提），近7天日均出库数量（包含自提）',
    `nearest_14_days_sale_avg` DECIMAL(38,18) COMMENT '近14天日均出库量（含自提），近14天日均出库数量（包含自提）',
    `future_sale_out_cnt` DECIMAL(38,18) COMMENT '预估未来日均销量，预估未来日均销售数量',
    `future_allocate_cnt` DECIMAL(38,18) COMMENT '预估未来日均调拨量，预估未来日均调拨数量',
    `imminent_store_cnt` BIGINT COMMENT '临期品库存量，临期商品库存数量',
    `imminent_store_amt` DECIMAL(38,18) COMMENT '临期品成本，临期商品库存成本',
    `unsalable_decided` STRING COMMENT '动销判定：两周低动销，两周无动销。取值范围：两周低动销|两周无动销|-',
    `is_sale_out_rask` STRING COMMENT '售罄风险判定：是，否。取值范围：是|否'
) 
COMMENT '仓+sku库存健康汇总表，包含仓库和SKU维度的库存健康指标、库存数量、库存成本、入库出库统计、动销情况等综合信息'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true') 
LIFECYCLE 30;
```