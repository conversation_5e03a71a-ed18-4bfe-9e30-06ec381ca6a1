```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_largeg_area_sku_order_mi` (
  `month` STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
  `sku_id` STRING COMMENT 'SKU编号，商品最小库存单位唯一标识',
  `spu_name` STRING COMMENT '商品名称，标准产品单元名称',
  `sku_disc` STRING COMMENT '商品描述，包含规格、重量、等级等详细信息',
  `large_area_id` BIGINT COMMENT '运营大区ID，取值范围：正整数或None（表示未分配大区）',
  `large_area_name` STRING COMMENT '运营大区名称，取值范围：具体大区名称或None（表示未分配大区）',
  `cust_cnt` BIGINT COMMENT '交易客户数，统计周期内购买该SKU的客户数量，取值范围：1-2127',
  `large_area_cust_cnt` BIGINT COMMENT '运营大区总客户数，统计周期内该大区的总客户数量，取值范围：133-9615'
)
COMMENT '区域渗透数据表，统计各运营大区内SKU级别的客户交易数据，用于分析商品在各区域的渗透情况'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '区域渗透数据分析表，包含SKU级别的区域交易客户统计',
  'lifecycle' = '30'
);
```