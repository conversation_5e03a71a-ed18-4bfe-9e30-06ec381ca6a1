CREATE TABLE IF NOT EXISTS app_pcs_direct_purchase_kpi_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务日期',
	`direct_purchase_amt` DECIMAL(38,18) COMMENT '直采金额，单位：元',
	`purchases_amt` DECIMAL(38,18) COMMENT '采购金额（非直采），单位：元',
	`cost_flow_amt` DECIMAL(38,18) COMMENT '成本浮动金额，单位：元',
	`direct_delivery_origin_amt` DECIMAL(38,18) COMMENT '直采履约应付金额，单位：元',
	`direct_delivery_real_amt` DECIMAL(38,18) COMMENT '直采履约实付金额，单位：元',
	`direct_delivery_market_amt` DECIMAL(38,18) COMMENT '直采营销费用，单位：元',
	`direct_delivery_cost_amt` DECIMAL(38,18) COMMENT '直采成本费用，单位：元',
	`direct_init_amt` DECIMAL(38,18) COMMENT '直采期末库存金额，单位：元',
	`direct_after_sale_pcs_amt` DECIMAL(38,18) COMMENT '直采采购责售后金额，单位：元',
	`direct_damage_pcs_amt` DECIMAL(38,18) COMMENT '直采采购责货损金额，单位：元'
) 
COMMENT '直采业务KPI指标日粒度统计表，包含直采相关的金额指标和费用明细'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='直采业务核心指标监控表，用于跟踪直采业务的财务表现和运营效率') 
LIFECYCLE 30;