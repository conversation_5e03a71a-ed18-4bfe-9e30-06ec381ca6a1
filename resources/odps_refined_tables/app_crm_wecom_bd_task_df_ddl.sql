CREATE TABLE IF NOT EXISTS app_crm_wecom_bd_task_df(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示年月日',
	`bd_id` BIGINT COMMENT '销售ID，唯一标识一个销售人员',
	`bd_name` STRING COMMENT '销售姓名',
	`m1_name` STRING COMMENT '城市负责人名称（M1），即销售的直接上级',
	`m2_name` STRING COMMENT '区域负责人名称（M2），即M1的直接上级',
	`m3_name` STRING COMMENT '部门负责人名称（M3），即M2的直接上级',
	`region` STRING COMMENT '大区名称，如苏皖大区、华南二区、山东大区、浙江大区等',
	`message_id` STRING COMMENT '消息ID，唯一标识一条消息',
	`message_status` BIGINT COMMENT '消息状态：0-未发送，2-已发送',
	`send_count` BIGINT COMMENT '消息接受客户数，范围0-500'
) 
COMMENT '销售&客户沟通互动看板，记录销售人员与客户的沟通互动情况'
PARTITIONED BY (
	`ds` STRING COMMENT '分区字段，yyyyMMdd格式，表示年月日'
)
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='销售&客户沟通互动看板表，包含销售层级关系、消息发送状态和客户接收统计') 
LIFECYCLE 30;