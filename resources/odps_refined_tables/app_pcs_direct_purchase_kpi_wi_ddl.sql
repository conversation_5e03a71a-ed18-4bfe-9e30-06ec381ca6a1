```sql
CREATE TABLE IF NOT EXISTS app_pcs_direct_purchase_kpi_wi(
    year STRING COMMENT '年份，格式：YYYY',
    week_of_year BIGINT COMMENT '周数，取值范围：1-53',
    monday STRING COMMENT '周一日期，格式：YYYYMMDD（年月日）',
    sunday STRING COMMENT '周日日期，格式：YYYYMMDD（年月日）',
    direct_purchase_amt DECIMAL(38,18) COMMENT '直采金额，单位：元',
    purchases_amt DECIMAL(38,18) COMMENT '采购金额（非直采），单位：元',
    cost_flow_amt DECIMAL(38,18) COMMENT '成本浮动金额，单位：元',
    direct_delivery_origin_amt DECIMAL(38,18) COMMENT '直采履约应付金额，单位：元',
    direct_delivery_real_amt DECIMAL(38,18) COMMENT '直采履约实付金额，单位：元',
    direct_delivery_market_amt DECIMAL(38,18) COMMENT '直采营销费用，单位：元',
    direct_delivery_cost_amt DECIMAL(38,18) COMMENT '直采成本费用，单位：元',
    direct_init_amt DECIMAL(38,18) COMMENT '直采期末库存金额，单位：元',
    direct_after_sale_pcs_amt DECIMAL(38,18) COMMENT '直采采购责售后金额，单位：元',
    direct_damage_pcs_amt DECIMAL(38,18) COMMENT '直采采购责货损金额，单位：元'
)
COMMENT '直采KPI指标表，包含直采业务相关的关键绩效指标数据，用于直采业务分析和监控'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd（年月日）')
STORED AS ALIORC
TBLPROPERTIES (
    'comment'='直采业务KPI统计表，按周维度统计直采相关的金额指标',
    'lifecycle'='30'
);
```