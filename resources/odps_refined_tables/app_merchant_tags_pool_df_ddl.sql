CREATE TABLE IF NOT EXISTS app_merchant_tags_pool_df(
    cust_id BIGINT COMMENT '客户ID，数值型标识，取值范围：7-131187',
    tag_id BIGINT COMMENT '标签ID，数值型标识，当前取值固定为2（枚举类型，仅包含2这个值）',
    tag_value STRING COMMENT '标签值，多个值用英文逗号分隔，存储用户标签的具体取值'
)
COMMENT '圈人平台用户标签池表，用于存储用户与标签的关联关系'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据所属日期')
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
    'comment'='圈人平台用户标签池表，存储用户与标签的映射关系')
LIFECYCLE 30;