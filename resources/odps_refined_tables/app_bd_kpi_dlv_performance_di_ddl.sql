```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_bd_kpi_dlv_performance_di`(
	`bd_id` BIGINT COMMENT '业绩归属的BD_ID，销售人员唯一标识',
	`bd_name` STRING COMMENT '业绩归属的销售姓名',
	`bd_m1` STRING COMMENT 'BD所属M1管理者姓名',
	`bd_m2` STRING COMMENT 'BD所属M2管理者姓名',
	`bd_m3` STRING COMMENT 'BD所属M3管理者姓名',
	`cust_m1` STRING COMMENT '客户所属M1管理者姓名',
	`zone_name` STRING COMMENT '客户所属销售区域名称',
	`is_same_city` STRING COMMENT '是否同城：是-同城，否-非同城',
	`kpi_gmv_achieve_amt` DECIMAL(38,18) COMMENT 'KPI口径GMV达成金额，单位：元',
	`kpi_fruit_gmv_achieve_amt` DECIMAL(38,18) COMMENT 'KPI口径鲜果GMV达成金额，单位：元',
	`dlv_real_amt` DECIMAL(38,18) COMMENT '履约实付GMV，单位：元',
	`no_at_dlv_real_amt` DECIMAL(38,18) COMMENT '非AT履约实付GMV，单位：元',
	`fruit_dlv_real_amt` DECIMAL(38,18) COMMENT '鲜果履约实付GMV，单位：元',
	`dairy_dlv_real_amt` DECIMAL(38,18) COMMENT '乳制品履约实付GMV，单位：元',
	`dairy_no_at_dlv_real_amt` DECIMAL(38,18) COMMENT '乳制品非AT履约实付GMV，单位：元',
	`other_dlv_real_amt` DECIMAL(38,18) COMMENT '其他品履约实付GMV，单位：元',
	`at_dlv_real_amt` DECIMAL(38,18) COMMENT 'AT履约实付GMV，单位：元',
	`dlv_profit_amt` DECIMAL(38,18) COMMENT '履约实付毛利润，单位：元',
	`no_at_dlv_profit_amt` DECIMAL(38,18) COMMENT '非AT履约实付毛利润，单位：元',
	`fruit_dlv_profit_amt` DECIMAL(38,18) COMMENT '鲜果履约实付毛利润，单位：元',
	`dairy_dlv_profit_amt` DECIMAL(38,18) COMMENT '乳制品履约实付毛利润，单位：元',
	`dairy_no_at_dlv_profit_amt` DECIMAL(38,18) COMMENT '乳制品非AT履约实付毛利润，单位：元',
	`other_dlv_profit_amt` DECIMAL(38,18) COMMENT '其他品履约实付毛利润，单位：元',
	`at_dlv_profit_amt` DECIMAL(38,18) COMMENT 'AT履约实付毛利润，单位：元',
	`dlv_cust_num` BIGINT COMMENT '自营品履约客户数',
	`no_at_dlv_cust_num` BIGINT COMMENT '非AT履约客户数',
	`fruit_dlv_cust_num` BIGINT COMMENT '鲜果履约客户数',
	`dairy_dlv_cust_num` BIGINT COMMENT '乳制品履约客户数',
	`dairy_no_at_dlv_cust_num` BIGINT COMMENT '乳制品不含AT履约客户数',
	`other_dlv_cust_num` BIGINT COMMENT '其他品履约客户数',
	`at_dlv_cust_num` BIGINT COMMENT 'AT履约客户数',
	`pop_real_total_gmv` DECIMAL(38,18) COMMENT '全品类交易实付GMV，单位：元',
	`pop_paid_users` BIGINT COMMENT '全品类交易客户数',
	`pop_fruit_real_total_gmv` DECIMAL(38,18) COMMENT '全品类鲜果交易实付GMV，单位：元',
	`pop_fruit_paid_users` BIGINT COMMENT '全品类鲜果交易客户数'
) 
COMMENT '平台销售KPI口径达成_日表，记录销售人员的KPI达成情况和履约业绩数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='销售KPI达成日表，包含销售层级关系、区域信息、各品类GMV达成金额、履约实付金额、毛利润、客户数等核心业务指标') 
LIFECYCLE 30;
```