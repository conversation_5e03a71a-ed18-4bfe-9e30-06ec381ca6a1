CREATE TABLE IF NOT EXISTS app_area_honour_kpi_wi(
	`saturday` STRING COMMENT '周六，格式为yyyyMMdd，表示统计周的起始日期（周六）',
	`friday` STRING COMMENT '周五，格式为yyyyMMdd，表示统计周的结束日期（周五）',
	`service_area` STRING COMMENT '服务区域名称，如：贵阳、华北、福建、华西、华中',
	`point_in_cnt` BIGINT COMMENT '内区点位数，统计区域内服务点数量，取值范围：155-20654',
	`point_out_cnt` BIGINT COMMENT '外区点位数，统计区域外服务点数量，取值范围：0-2671',
	`out_quality` BIGINT COMMENT '出库件数，统计周期内出库商品数量，取值范围：713-122512',
	`trunk_amt` DECIMAL(38,18) COMMENT '干线费用，运输干线产生的费用',
	`delivery_in_amt` DECIMAL(38,18) COMMENT '内区配送成本，区域内配送产生的费用',
	`delivery_out_amt` DECIMAL(38,18) COMMENT '外区配送成本，区域外配送产生的费用',
	`storage_amt` DECIMAL(38,18) COMMENT '仓储成本，商品存储产生的费用',
	`delivery_total_amt` DECIMAL(38,18) COMMENT '总配送费，所有配送费用的总和'
) 
COMMENT '区域维度履约成本KPI周表，按区域统计每周的履约成本相关指标，包括点位数、出库件数、各项成本费用等'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据统计日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='区域维度履约成本KPI周表，用于区域运营成本分析和监控') 
LIFECYCLE 30;