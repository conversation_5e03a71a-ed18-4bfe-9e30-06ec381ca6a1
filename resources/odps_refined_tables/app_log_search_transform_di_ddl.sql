```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_log_search_transform_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
  `query` STRING COMMENT '搜索词:整体/草莓等，枚举值包括：整体、PET冷饮杯（92口径）、越南椰青、0氢化奶、10寸等具体商品搜索词',
  `cust_uv` BIGINT COMMENT '用户UV：独立访客数，统计搜索该词的去重用户数',
  `cust_pv` BIGINT COMMENT '用户PV：页面浏览量，统计搜索该词的总访问次数',
  `new_cust_uv` BIGINT COMMENT '新用户UV：新独立访客数，统计搜索该词的新用户去重数量',
  `sku_impression_uv` BIGINT COMMENT '商品曝光UV：看到商品的独立用户数',
  `sku_impression_pv` BIGINT COMMENT '商品曝光PV：商品被展示的总次数',
  `sku_click_uv` BIGINT COMMENT '商品点击UV：点击商品的独立用户数',
  `sku_click_pv` BIGINT COMMENT '商品点击PV：商品被点击的总次数',
  `sku_cart_uv` BIGINT COMMENT '商品加购UV：将商品加入购物车的独立用户数',
  `sku_cart_pv` BIGINT COMMENT '商品加购PV：商品被加入购物车的总次数',
  `order_cnt` BIGINT COMMENT '下单数（订单数量）：生成订单的数量',
  `order_amt` DECIMAL(38,18) COMMENT '下单金额：订单的总金额',
  `order_uv` BIGINT COMMENT '下单用户UV：下单的独立用户数',
  `order_paid_cnt` BIGINT COMMENT '支付订单数：完成支付的订单数量',
  `order_paid_amt` DECIMAL(38,18) COMMENT '支付金额：实际支付的总金额',
  `order_paid_uv` BIGINT COMMENT '支付订单用户UV：完成支付的独立用户数',
  `order_paid_amt_avg` DECIMAL(38,18) COMMENT '平均支付金额：支付金额/支付订单数',
  `sku_click_uv_rate` DECIMAL(38,18) COMMENT '商品UV点击率（点击/曝光）：商品点击UV/商品曝光UV',
  `sku_click_pv_rate` DECIMAL(38,18) COMMENT '商品PV点击率（点击/曝光）：商品点击PV/商品曝光PV',
  `sku_cart_uv_rate` DECIMAL(38,18) COMMENT '商品UV加购率（加购/曝光）：商品加购UV/商品曝光UV',
  `sku_cart_pv_rate` DECIMAL(38,18) COMMENT '商品PV加购率（加购/曝光）：商品加购PV/商品曝光PV',
  `order_paid_uv_rate` DECIMAL(38,18) COMMENT '订单支付UV转化率（支付用户UV/总UV）：支付用户UV/搜索用户UV'
)
COMMENT '商城搜索词转化汇总表，统计各搜索词的用户行为转化数据，包括曝光、点击、加购、下单、支付等关键指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据所属日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment'='商城搜索词转化行为分析表',
  'lifecycle'='30'
);
```