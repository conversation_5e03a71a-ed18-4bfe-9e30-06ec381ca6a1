```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_m2_sales_performance_mi` (
  `months` STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
  `m2` STRING COMMENT 'M2管理者姓名，销售经理层级',
  `m3` STRING COMMENT 'M3管理者姓名，销售总监层级，枚举值：吕建杰、孙日达',
  `region` STRING COMMENT '销售大区名称，枚举值：西南大区、山东大区、昆明区域、华中大区、闽桂大区等',
  `category` STRING COMMENT '商品类目，枚举值：鲜果、其他类目',
  `gmv_target` DECIMAL(38,18) COMMENT '实付GMV目标金额，单位：元',
  `real_total_amt` DECIMAL(38,18) COMMENT '累计实付GMV金额，单位：元',
  `real_total_amt_last_1d` DECIMAL(38,18) COMMENT '当天实付GMV金额，单位：元',
  `real_total_amt_last_2d` DECIMAL(38,18) COMMENT '前一天实付GMV金额，单位：元',
  `time_coming_rate` DECIMAL(38,18) COMMENT '时间进度完成率，小数形式表示百分比'
) 
COMMENT '销售M2维度达成表现表，按M2管理者维度统计GMV目标达成情况和时间进度'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '销售M2维度达成表现表，用于监控M2管理者的销售业绩达成情况',
  'lifecycle' = '30'
);
```