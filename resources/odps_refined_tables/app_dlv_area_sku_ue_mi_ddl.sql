CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_dlv_area_sku_ue_mi` (
  `month` STRING COMMENT '月份，格式为yyyyMM',
  `large_area_id` BIGINT COMMENT '运营服务大区ID，取值范围：1-93',
  `large_area_name` STRING COMMENT '运营服务大区名称，如：杭州大区、上海大区等',
  `sku_type` STRING COMMENT '商品类型：自营-自营商品，代仓-代仓商品',
  `point_cnt` BIGINT COMMENT '点位数，取值范围：1-10291',
  `cust_cnt` BIGINT COMMENT '客户数，取值范围：1-9415',
  `order_cnt` BIGINT COMMENT '订单数，取值范围：2-31305',
  `origin_total_amt` DECIMAL(38,18) COMMENT '配送应付总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '配送实付总金额',
  `cost_amt` DECIMAL(38,18) COMMENT '配送成本总金额',
  `deliver_amt` DECIMAL(38,18) COMMENT '运费',
  `after_sale_amt` DECIMAL(38,18) COMMENT '售后金额',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储成本',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本',
  `delivery_amt` DECIMAL(38,18) COMMENT '配送成本',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨成本',
  `self_picked_amt` DECIMAL(38,18) COMMENT '自提成本',
  `other_amt` DECIMAL(38,18) COMMENT '其他成本'
) 
COMMENT '运营服务大区商品类型UE表，统计各运营大区不同商品类型的运营指标数据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '运营服务大区商品类型UE分析表，包含点位数、客户数、订单数及各成本金额等运营指标',
  'lifecycle' = '30'
);