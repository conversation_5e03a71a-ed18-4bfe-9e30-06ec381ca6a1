CREATE TABLE IF NOT EXISTS app_stc_warehouse_category_kpi_mi(
	month STRING COMMENT '月份，格式为yyyyMM',
	warehouse_no BIGINT COMMENT '库存仓编号',
	warehouse_name STRING COMMENT '库存仓名称',
	category STRING COMMENT '类目：鲜果-生鲜水果类商品，标品-标准化包装商品',
	origin_total_amt DECIMAL(38,18) COMMENT '履约应付GMV，原始订单总金额',
	real_total_amt DECIMAL(38,18) COMMENT '履约实付GMV，实际支付金额',
	after_sale_amt DECIMAL(38,18) COMMENT '售后总金额',
	after_sale_amt_check DECIMAL(38,18) COMMENT '售后金额品控责任部分',
	damage_cnt BIGINT COMMENT '货损总数量',
	damage_amt DECIMAL(38,18) COMMENT '货损总金额',
	damage_cnt_wah BIGINT COMMENT '货损数量_仓配责任',
	damage_amt_wah DECIMAL(38,18) COMMENT '货损金额_仓配责任',
	damage_cnt_pur BIGINT COMMENT '货损数量_采购责任',
	damage_amt_pur DECIMAL(38,18) COMMENT '货损金额_采购责任',
	damage_cnt_opr BIGINT COMMENT '货损数量_运营责任',
	damage_amt_opr DECIMAL(38,18) COMMENT '货损金额_运营责任',
	damage_cnt_oth BIGINT COMMENT '货损数量_其他责任',
	damage_amt_oth DECIMAL(38,18) COMMENT '货损金额_其他责任',
	sale_cnt BIGINT COMMENT '销售总数量',
	sale_amt DECIMAL(38,18) COMMENT '销售总金额',
	test_cnt BIGINT COMMENT '抽检数量',
	qualified_cnt BIGINT COMMENT '合格数量',
	check_cnt BIGINT COMMENT '货检数量',
	inbound_cnt BIGINT COMMENT '入库数量'
) 
COMMENT '品控KPI统计表，按月份、仓库、类目维度统计货损、售后、销售等关键品控指标'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='品控KPI监控表，用于追踪各仓库各类目商品的货损责任划分、售后处理、销售表现等关键质量指标') 
LIFECYCLE 30;