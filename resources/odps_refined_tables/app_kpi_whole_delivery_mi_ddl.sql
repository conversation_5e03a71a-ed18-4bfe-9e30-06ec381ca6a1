```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_kpi_whole_delivery_mi` (
  `month` STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
  `cust_group` STRING COMMENT '客户类型，取值范围：大客户、平台客户、批发客户、ALL（全部客户）',
  `sku_type` STRING COMMENT '商品类型，取值范围：自营、代仓、全品类、SAAS客户自营、ALL（全部类型）',
  `category` STRING COMMENT '商品类目，取值范围：鲜果、乳制品、其他、ALL（全部类目）',
  `dlv_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额（元）',
  `dlv_real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额（元）',
  `dlv_cost_amt` DECIMAL(38,18) COMMENT '履约总成本（元）',
  `dlv_origin_gross_profit` DECIMAL(38,18) COMMENT '履约应付毛利润（元）',
  `dlv_real_gross_profit` DECIMAL(38,18) COMMENT '履约实付毛利润（元）',
  `dlv_cust_cnt` BIGINT COMMENT '履约客户数（个）',
  `dlv_point_cnt` BIGINT COMMENT '履约点位数（个）',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费（元），含精准送、超时加单费，去除优惠券',
  `after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后金额（元）',
  `damage_amt` DECIMAL(38,18) COMMENT '货损金额（元）',
  `offine_delivey_amt` DECIMAL(38,18) COMMENT '履约费用（元）',
  `offine_no_delivey_amt` DECIMAL(38,18) COMMENT '非履约费用（元）',
  `saas_sign_month_amt` DECIMAL(38,18) COMMENT 'SaaS签约月收入（元）',
  `saas_sign_month_total_amt` DECIMAL(38,18) COMMENT 'SaaS签约总收入（元）',
  `timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送履约应付总金额（元）',
  `timing_real_total_amt` DECIMAL(38,18) COMMENT '省心送履约实付总金额（元）',
  `timing_cost_amt` DECIMAL(38,18) COMMENT '省心送履约总成本（元）',
  `sku_total_store_fee_amt` DECIMAL(38,18) COMMENT '仓_存储费（元）',
  `sku_in_and_out_fee_amt` DECIMAL(38,18) COMMENT '仓_操作费（元）',
  `work_qty` DECIMAL(38,18) COMMENT '结算点位数（个）',
  `dlv_fee_amount_per_point` DECIMAL(38,18) COMMENT '点均配送费用（元/点位）',
  `big_cust_amount` DECIMAL(38,18) COMMENT '大客户用车费用（元）',
  `transfer_amount` DECIMAL(38,18) COMMENT '调拨用车费用（元）',
  `trunk_amount` DECIMAL(38,18) COMMENT '干线用车费用（元）',
  `warehouse_amount` DECIMAL(38,18) COMMENT '仓库用车费用（元）',
  `qst_amount` DECIMAL(38,18) COMMENT '全生态用车费用（元）',
  `trunk_dlv_amount` DECIMAL(38,18) COMMENT '干线城配用车费用（元）',
  `pop_amount` DECIMAL(38,18) COMMENT '全品类用车费用（元）',
  `purchase_total_trunc_fee` DECIMAL(38,18) COMMENT '采购用车费用（元），含线下',
  `purchase_discreet_offline_amount` DECIMAL(38,18) COMMENT '采购线下费用预估值（元）',
  `trunk_dlv_discreet_amt` DECIMAL(38,18) COMMENT '运输履约调整预估值（元）',
  `trunk_undlv_discreet_amt` DECIMAL(38,18) COMMENT '运输非履约调整预估值（元）',
  `special_amt` DECIMAL(38,18) COMMENT '特价活动营销费用（元）',
  `temporary_amt` DECIMAL(38,18) COMMENT '临保活动营销费用（元）',
  `step_amt` DECIMAL(38,18) COMMENT '阶梯价营销费用（元）',
  `cream_amt` DECIMAL(38,18) COMMENT '奶油卡营销费用（元）',
  `fresh_milk_amt` DECIMAL(38,18) COMMENT '鲜奶卡营销费用（元）',
  `after_sale_compensate_amt` DECIMAL(38,18) COMMENT '售后补偿营销费用（元）',
  `industry_activities_amt` DECIMAL(38,18) COMMENT '行业活动营销费用（元）',
  `sale_store_amt` DECIMAL(38,18) COMMENT '销售囤货券营销费用（元）',
  `sale_spots_amt` DECIMAL(38,18) COMMENT '销售现货券营销费用（元）',
  `sales_customer_amt` DECIMAL(38,18) COMMENT '销售客情券营销费用（元）',
  `sales_activity_amt` DECIMAL(38,18) COMMENT '销售月活券营销费用（元）',
  `sales_category_amt` DECIMAL(38,18) COMMENT '销售品类券营销费用（元）',
  `area_new_amt` DECIMAL(38,18) COMMENT '区域拉新券营销费用（元）',
  `area_recall_amt` DECIMAL(38,18) COMMENT '区域召回券营销费用（元）',
  `full_reduction_amt` DECIMAL(38,18) COMMENT '满减活动营销费用（元）',
  `platform_activity_amt` DECIMAL(38,18) COMMENT '平台活动券营销费用（元）'
) 
COMMENT '履约KPI汇总表，包含各类履约相关的关键绩效指标数据，用于业务分析和决策支持'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期，如20250917表示2025年9月17日'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '履约KPI汇总表，包含各类履约相关的关键绩效指标数据',
  'lifecycle' = '30'
);
```