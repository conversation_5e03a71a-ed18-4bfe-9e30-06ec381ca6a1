CREATE TABLE IF NOT EXISTS app_kpi_wholesale_wi(
	year STRING COMMENT '年份，格式：yyyy',
	week_of_year BIGINT COMMENT '周数，取值范围：1-53',
	monday STRING COMMENT '周一日期，格式：yyyyMMdd，表示年月日',
	sunday STRING COMMENT '周日日期，格式：yyyyMMdd，表示年月日',
	sku_type STRING COMMENT '商品类型；取值范围：自营/代仓',
	order_gmv_amt DECIMAL(38,18) COMMENT '交易应付总金额',
	deliver_gmv_amt DECIMAL(38,18) COMMENT '履约应付总金额',
	deliver_cust_cnt BIGINT COMMENT '履约客户数',
	deliver_cust_arpu DECIMAL(38,18) COMMENT '履约ARPU(应付总金额/客户数)',
	deliver_order_cnt BIGINT COMMENT '履约订单数',
	deliver_order_avg DECIMAL(38,18) COMMENT '履约订单均价(应付总金额/订单数)',
	deliver_cost_amt DECIMAL(38,18) COMMENT '履约总成本',
	deliver_gross_profit DECIMAL(38,18) COMMENT '履约毛利润(应付总金额-总成本)',
	deliver_gross_profit_rate DECIMAL(38,18) COMMENT '履约毛利率'
) 
PARTITIONED BY (ds STRING COMMENT '分区字段，yyyyMMdd格式，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='交易口径KPI指标日汇总表，包含批发业务的核心交易和履约指标数据') 
LIFECYCLE 30;