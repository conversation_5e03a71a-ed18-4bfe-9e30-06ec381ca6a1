CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_dlv_pop_supplier_final_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示年月日',
  `supplier` STRING COMMENT '供应商名称',
  `order_sku_cnt` BIGINT COMMENT '下单商品数，统计供应商下单的商品数量',
  `order_sku_weight` DECIMAL(38,18) COMMENT '下单商品总毛重（斤），供应商下单商品的总重量',
  `order_total_amt` DECIMAL(38,18) COMMENT '供货总金额（元），供应商供货的总金额',
  `take_total_amt` DECIMAL(38,18) COMMENT '提货费用（元），供应商提货产生的费用',
  `after_sale_short_sku_cnt` BIGINT COMMENT '售后缺货商品数，统计售后缺货的商品数量',
  `after_sale_short_sku_weight` DECIMAL(38,18) COMMENT '售后缺货商品总毛重（斤），售后缺货商品的总重量',
  `after_sale_short_total_amt` DECIMAL(38,18) COMMENT '售后缺货总金额（元），售后缺货产生的总金额',
  `after_sale_quality_sku_cnt` BIGINT COMMENT '售后质量商品数，统计售后质量问题的商品数量',
  `after_sale_quality_sku_weight` DECIMAL(38,18) COMMENT '售后质量商品总毛重（斤），售后质量问题商品的总重量',
  `after_sale_quality_total_amt` DECIMAL(38,18) COMMENT '售后质量总金额（元），售后质量问题产生的总金额',
  `commission_after_sale_short_amt` DECIMAL(38,18) COMMENT '售后缺货金额/(1+佣金比例)，计算佣金后的售后缺货金额',
  `commission_after_sale_quality_amt` DECIMAL(38,18) COMMENT '售后质量金额/(1+佣金比例)，计算佣金后的售后质量问题金额'
)
COMMENT '供应商对账汇总表，用于统计供应商的订单、售后及费用等对账信息'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '供应商对账汇总表，包含供应商的订单统计、售后统计及费用统计等信息',
  'lifecycle' = '30'
);