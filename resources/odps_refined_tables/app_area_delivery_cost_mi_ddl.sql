CREATE TABLE IF NOT EXISTS app_area_delivery_cost_mi(
	`month` STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
	`area_no` BIGINT COMMENT '城配仓编号，唯一标识一个城配仓，取值范围：1-144',
	`area_name` STRING COMMENT '城配仓名称，如：杭州仓、上海仓等',
	`service_area` STRING COMMENT '服务区域，如：华东、华北等',
	`area_type` STRING COMMENT '城配仓类型：外区-外部区域仓，内区-内部区域仓',
	`path_cnt` BIGINT COMMENT '线路数量，取值范围：5-428',
	`point_cnt` BIGINT COMMENT '当前月点位数，取值范围：36-7883',
	`km_cnt` DECIMAL(38,18) COMMENT '配送总公里数',
	`delivery_amt` DECIMAL(38,18) COMMENT '当月配送总费用',
	`point_cnt_b1w` BIGINT COMMENT '上月总点位数，取值范围：71-16504',
	`total_amt_b1w` DECIMAL(38,18) COMMENT '上月总配送费用',
	`delivery_total_amt` DECIMAL(38,18) COMMENT '配送GVM（总交易额）'
) 
COMMENT '城配仓维度配送成本月表，按城配仓统计配送成本、线路数、点位数等关键指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，yyyyMM格式，表示数据所属月份') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='城配仓维度配送成本月表，用于分析各城配仓的配送效率、成本和运营指标') 
LIFECYCLE 30;