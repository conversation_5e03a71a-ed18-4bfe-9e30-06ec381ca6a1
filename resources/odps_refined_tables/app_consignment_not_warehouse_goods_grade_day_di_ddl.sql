CREATE TABLE IF NOT EXISTS app_consignment_not_warehouse_goods_grade_day_di(
	sku_id STRING COMMENT 'SKU ID，商品唯一标识',
	spu_name STRING COMMENT '商品名称',
	disc STRING COMMENT '商品规格描述',
	grade STRING COMMENT '近30天动销分层标签：A-GMV前75%（优质商品），B-75%-95%（中等商品），C-剩余5%（低动销商品）',
	outdated BIGINT COMMENT 'SKU生命周期状态：-1-上新处理中，0-使用中，1-已删除',
	item_label STRING COMMENT '淘汰标识：0-正常商品，1-淘汰商品',
	date_tag STRING COMMENT '日期标签，yyyyMMdd格式'
)
COMMENT '全品类代销不入仓近30天动销分层标签表，用于商品动销表现分层分析'
PARTITIONED BY (ds STRING COMMENT '分区字段，数据日期，yyyyMMdd格式') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='全品类代销不入仓商品近30天动销分层标签表，基于GMV分布进行ABC分层') 
LIFECYCLE 30;