```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_operate_kpi_trade_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务日期',
  `origin_total_amt` DECIMAL(38,18) COMMENT '交易应付总金额，包含所有应付金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '交易实付总金额，用户实际支付的金额',
  `cust_cnt` BIGINT COMMENT '交易客户数，当日有交易行为的客户数量',
  `cust_arpu` DECIMAL(38,18) COMMENT 'ARPU(平均每用户收入)，计算公式：应付总金额/客户数',
  `order_cnt` BIGINT COMMENT '交易订单数，当日产生的订单总数',
  `order_avg` DECIMAL(38,18) COMMENT '订单均价，计算公式：应付总金额/订单数',
  `consign_origin_total_amt` DECIMAL(38,18) COMMENT '代售应付总金额，代售业务的应付金额',
  `consign_real_total_amt` DECIMAL(38,18) COMMENT '代售实付总金额，代售业务的实付金额',
  `consign_preferential_amt` DECIMAL(38,18) COMMENT '代售营销金额，代售业务的营销优惠金额',
  `consign_cust_cnt` BIGINT COMMENT '代售客户数，代售业务的客户数量，取值范围：>=0',
  `consign_order_cnt` BIGINT COMMENT '代售订单数，代售业务的订单数量，取值范围：>=0',
  `register_cust_cnt` BIGINT COMMENT '注册客户数，当日新注册的客户数量，取值范围：>=0',
  `new_active_cust_cnt` BIGINT COMMENT '有效拉新客户数，注册且首日下单金额>=15元的客户数量，取值范围：>=0',
  `lose_cust_cnt` BIGINT COMMENT '交易流失客户数，90天内活跃用户近60天未下单客户数，取值范围：>=0',
  `lose_cust_ratio` DECIMAL(38,18) COMMENT '交易流失率，流失客户数占总客户数的比例，取值范围：0-1'
)
COMMENT '交易（订单）运营KPI指标汇总表，包含交易相关的核心运营指标数据'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '交易运营KPI指标日汇总表，用于监控和分析交易业务的核心指标',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```