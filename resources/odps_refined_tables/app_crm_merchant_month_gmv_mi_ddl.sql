```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_crm_merchant_month_gmv_mi` (
  `cust_id` BIGINT COMMENT '商户ID，唯一标识商户',
  `cust_name` STRING COMMENT '商户名称',
  `city_id` BIGINT COMMENT '商户所在运营区域ID',
  `merchant_total_gmv` DECIMAL(38,18) COMMENT '商户总GMV（商品交易总额），单位：元',
  `distribution_gmv` DECIMAL(38,18) COMMENT '配送业务GMV，单位：元',
  `delivery_unit_price` DECIMAL(38,18) COMMENT '配送客单价，单位：元/单',
  `distribution_amout` BIGINT COMMENT '配送次数',
  `fruit_gmv` DECIMAL(38,18) COMMENT '鲜果品类GMV，单位：元',
  `dairy_gmv` DECIMAL(38,18) COMMENT '乳制品品类GMV，单位：元',
  `non_dairy_gmv` DECIMAL(38,18) COMMENT '非乳制品品类GMV，单位：元',
  `brand_gmv` DECIMAL(38,18) COMMENT '自营品牌GMV，单位：元',
  `reward_gmv` DECIMAL(38,18) COMMENT '奖励SKU的GMV，单位：元',
  `core_merchant_tag` BIGINT COMMENT '核心客户标记：0-否，1-是',
  `bd_id` BIGINT COMMENT 'BD编号，公海商户为0',
  `bd_name` STRING COMMENT 'BD名称',
  `l1` DECIMAL(38,18) COMMENT 'L1指标（具体业务含义需确认）',
  `l2` DECIMAL(38,18) COMMENT 'L2指标（具体业务含义需确认）',
  `sku_num` BIGINT COMMENT '本月累计下单商品种类数（SKU数量）',
  `spu_num` BIGINT COMMENT '本月累计下单SPU种类数',
  `province` STRING COMMENT '省份',
  `city` STRING COMMENT '城市',
  `area` STRING COMMENT '区域/区县',
  `agent_gmv` DECIMAL(38,18) COMMENT '代售业务GMV，单位：元'
)
COMMENT '商户本月GMV统计表，按月份分区存储商户各项GMV指标数据'
PARTITIONED BY (
  `ym` STRING COMMENT '分区字段，年月格式：yyyyMM'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '商户GMV月度统计表，包含商户基本信息、各类GMV指标、地域信息和BD关联信息',
  'lifecycle' = '365'
);
```