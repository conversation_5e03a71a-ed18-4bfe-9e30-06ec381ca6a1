```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_saas_order_after_sale_detail_summary_di` (
  `tenant_id` BIGINT COMMENT '租户ID',
  `time_tag` STRING COMMENT '时间标签，格式：yyyyMMdd',
  `after_sale_order_no` STRING COMMENT '售后单号',
  `order_no` STRING COMMENT '订单编号',
  `order_item_id` STRING COMMENT '订单项编号',
  `order_time` DATETIME COMMENT '下单时间，格式：年月日时分秒',
  `finished_time` DATETIME COMMENT '售后成功时间，格式：年月日时分秒',
  `item_id` BIGINT COMMENT '商品item_id',
  `item_code` STRING COMMENT '商品自有编码',
  `item_title` STRING COMMENT '商品标题',
  `item_specification` STRING COMMENT '商品规格',
  `item_amount` BIGINT COMMENT '订单商品数量',
  `after_sale_amount` BIGINT COMMENT '售后商品数量',
  `after_sale_type` BIGINT COMMENT '售后类型：0-已到货，1-未到货',
  `service_type` BIGINT COMMENT '售后服务类型：1-退款，2-退款录入账单，3-退货退款，4-退货退款录入账单，5-换货，6-补发，7-退款录入余额，8-退货退款录入余额',
  `responsibility_type` BIGINT COMMENT '售后责任方：0-供应商，1-品牌方，2-门店',
  `reason` STRING COMMENT '售后原因',
  `total_refund_price` DECIMAL(38,18) COMMENT '售后总金额',
  `item_refund_price` DECIMAL(38,18) COMMENT '商品退款金额',
  `delivery_refund_fee` DECIMAL(38,18) COMMENT '运费退款金额',
  `goods_sku` STRING COMMENT '货品sku',
  `goods_title` STRING COMMENT '货品名称',
  `goods_specification` STRING COMMENT '货品规格',
  `goods_supplier_name` STRING COMMENT '货品供应商名称',
  `goods_type` BIGINT COMMENT '货品类型：1-鲜沐直供，2-代仓',
  `goods_agent_fee` DECIMAL(38,18) COMMENT '货品代仓费用',
  `goods_refund_price` DECIMAL(38,18) COMMENT '货品退款金额',
  `goods_supply_price` DECIMAL(38,18) COMMENT '货品采购单价',
  `pay_type` BIGINT COMMENT '支付方式：1-线上支付，2-账期，3-余额支付',
  `store_id` BIGINT COMMENT '门店ID',
  `store_name` STRING COMMENT '门店名称',
  `first_classification` STRING COMMENT '一级分组',
  `second_classification` STRING COMMENT '二级分组',
  `store_no` STRING COMMENT '门店编号',
  `supplier_id` BIGINT COMMENT '供应商id',
  `after_sale_time` DATETIME COMMENT '售后发起时间，格式：年月日时分秒',
  `goods_delivery_fee_refund` DECIMAL(38,18) COMMENT '货品配送费售后金额',
  `delivery_refund_fee_flag` BIGINT COMMENT '退运费标识：0-不退，1-退',
  `order_confirm_receipt_time` DATETIME COMMENT '订单确认收货时间，格式：年月日时分秒',
  `supplier_sku` STRING COMMENT '鲜沐sku',
  `after_sale_unit` STRING COMMENT '售后单位',
  `province` STRING COMMENT '省',
  `city` STRING COMMENT '市',
  `area` STRING COMMENT '区',
  `address` STRING COMMENT '收货地址'
)
COMMENT 'SAAS对账单-售后明细汇总表，包含售后订单的详细信息、商品信息、门店信息和地理位置信息'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = 'SAAS对账单-售后明细汇总表，用于记录和分析售后订单的详细数据',
  'lifecycle' = '30'
)
```