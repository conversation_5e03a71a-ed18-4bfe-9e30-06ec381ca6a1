```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_self_category_delivery_warehouse_kpi_mi` (
  `month` STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
  `category` STRING COMMENT '商品品类：鲜果，乳制品，其他',
  `origin_total_amt` DECIMAL(38,18) COMMENT '原始总金额（元）',
  `real_total_amt` DECIMAL(38,18) COMMENT '实际总金额（元）',
  `cost_amt` DECIMAL(38,18) COMMENT '成本金额（元）',
  `timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送业务原始总金额（元）',
  `timing_real_total_amt` DECIMAL(38,18) COMMENT '省心送业务实际总金额（元）',
  `cust_cnt` BIGINT COMMENT '客户数量',
  `order_cnt` BIGINT COMMENT '订单数量',
  `point_cnt` BIGINT COMMENT '点位总数',
  `day_point_cnt` BIGINT COMMENT '日均点位数量',
  `sku_cnt` BIGINT COMMENT 'SKU数量',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费金额（元）',
  `cust_runoff` DECIMAL(38,18) COMMENT '客户流失率，取值范围0-1',
  `after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后总金额（元）',
  `inventory_loss_amt` DECIMAL(38,18) COMMENT '盘亏总金额（元）',
  `inventory_profit_amt` DECIMAL(38,18) COMMENT '盘盈总金额（元）',
  `damage_amt` DECIMAL(38,18) COMMENT '货损总金额（元）',
  `damage_rate` DECIMAL(38,18) COMMENT '货损占比，取值范围0-1',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储成本（元）',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '干线运输成本（元）',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送成本（元）',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提成本（元）',
  `other_amt` DECIMAL(38,18) COMMENT '其他成本（元）',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨成本（元）'
)
COMMENT '履约口径KPI指标日汇总表（自营业务）- 包含商品品类维度的履约相关财务和运营指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '自营业务履约口径KPI指标日度汇总表，按商品品类和月份维度统计各项财务和运营指标',
  'lifecycle' = '30'
);
```