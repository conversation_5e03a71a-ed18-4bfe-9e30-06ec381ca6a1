```sql
CREATE TABLE IF NOT EXISTS app_kpi_cust_category_trade_mi(
    month STRING COMMENT '月份，格式：yyyyMM',
    cust_team STRING COMMENT '客户团队类型，枚举值：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户',
    category STRING COMMENT '品类，枚举值：鲜果、乳制品、其他',
    origin_total_amt DECIMAL(38,18) COMMENT '应付总金额，单位：元',
    real_total_amt DECIMAL(38,18) COMMENT '实付总金额，单位：元',
    cust_cnt BIGINT COMMENT '客户数，统计周期内去重客户数量',
    cust_arpu DECIMAL(38,18) COMMENT 'ARPU值（平均每用户收入），计算公式：应付总金额/客户数，单位：元',
    order_cnt BIGINT COMMENT '订单数，统计周期内总订单数量',
    order_avg DECIMAL(38,18) COMMENT '订单均价，计算公式：应付总金额/订单数，单位：元',
    after_sale_noreceived_amt DECIMAL(38,18) COMMENT '未到货售后总金额，单位：元',
    after_sale_rate DECIMAL(38,18) COMMENT '退货率，计算公式：未到货售后总金额/应付总金额，百分比值',
    dire_origin_total_amt DECIMAL(38,18) COMMENT '直发采购应付总金额，单位：元',
    delivery_amt DECIMAL(38,18) COMMENT '运费（运费+超时加单费），单位：元',
    timing_origin_total_amt DECIMAL(38,18) COMMENT '省心送应付总金额，单位：元'
) 
COMMENT '交易口径KPI指标月汇总表，按月份、客户团队类型、品类维度统计的交易相关指标'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
    'comment'='交易口径KPI指标月汇总表，包含应付金额、实付金额、客户数、订单数、ARPU、退货率等核心交易指标') 
LIFECYCLE 30;
```