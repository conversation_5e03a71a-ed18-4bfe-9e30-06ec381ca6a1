```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_pcs_supplier_rebate_cumulative_df` (
  `year` BIGINT COMMENT '年度，格式：YYYY',
  `period` STRING COMMENT '周期，取值范围：H1(上半年)、H2(下半年)、Q1-Q4(季度)、M1-M12(月度)',
  `supplier_id` BIGINT COMMENT '供货商编号',
  `supplier_name` STRING COMMENT '供货商名称',
  `warehouse_no` BIGINT COMMENT '仓库编号',
  `warehouse_name` STRING COMMENT '仓库名称',
  `commodity_temperature_zone` STRING COMMENT '商品温区，取值范围：冷藏、冷冻、常温',
  `purchase_order_amount_in_period` DECIMAL(38,18) COMMENT '周期内采购下单金额（含税）',
  `purchase_order_amount_excluding_tax` DECIMAL(38,18) COMMENT '采购下单金额（不含税）',
  `purchase_order_quantity_in_period` BIGINT COMMENT '周期内采购下单件数',
  `purchase_order_weight_in_period` DECIMAL(38,18) COMMENT '周期内采购下单重量（单位：千克）',
  `purchase_order_volume_in_period` DECIMAL(38,18) COMMENT '周期内采购下单体积（单位：立方米）',
  `purchase_inbound_amount_in_period` DECIMAL(38,18) COMMENT '周期内采购入库金额（含税）',
  `purchase_inbound_amount_excluding_tax` DECIMAL(38,18) COMMENT '采购入库金额（不含税）',
  `purchase_inbound_quantity_in_period` BIGINT COMMENT '周期内采购入库件数',
  `purchase_inbound_weight_in_period` DECIMAL(38,18) COMMENT '周期内采购入库重量（单位：千克）',
  `purchase_inbound_volume_in_period` DECIMAL(38,18) COMMENT '周期内采购入库体积（单位：立方米）',
  `purchase_reservation_amount_in_period` DECIMAL(38,18) COMMENT '周期内采购预约金额（含税）',
  `purchase_reservation_amount_excluding_tax` DECIMAL(38,18) COMMENT '采购预约金额（不含税）',
  `purchase_reservation_quantity_in_period` BIGINT COMMENT '周期内采购预约件数',
  `purchase_reservation_weight_in_period` DECIMAL(38,18) COMMENT '周期内采购预约重量（单位：千克）',
  `purchase_reservation_volume_in_period` DECIMAL(38,18) COMMENT '周期内采购预约体积（单位：立方米）',
  `sale_amount_in_period` DECIMAL(38,18) COMMENT '周期内销售金额（含税）',
  `sale_quantity_in_period` BIGINT COMMENT '周期内销售件数',
  `sale_weight_in_period` DECIMAL(38,18) COMMENT '周期内销售重量（单位：千克）',
  `sale_volume_in_period` DECIMAL(38,18) COMMENT '周期内销售体积（单位：立方米）'
) 
COMMENT '供应商返利目标累计表（剔除部分SKU），用于统计供应商在特定周期内的采购和销售数据，作为返利计算的依据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据日期'
)
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
               'comment'='供应商返利目标累计表，包含采购下单、入库、预约和销售等关键业务指标') 
LIFECYCLE 30;
```