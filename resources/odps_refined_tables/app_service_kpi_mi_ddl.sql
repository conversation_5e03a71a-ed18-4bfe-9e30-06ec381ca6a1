CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_service_kpi_mi`(
	`month` STRING COMMENT '月份，格式为yyyyMM',
	`cust_team` STRING COMMENT '客户类型：全量客户，平台客户；取值范围：全量客户、平台客户',
	`channel_type` STRING COMMENT '渠道类型：鲜沐，SAAS；取值范围：鲜沐、SAAS',
	`after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后总金额（单位：元）',
	`after_sale_received_quality_amt` DECIMAL(38,18) COMMENT '已到货售后总金额（质量问题）（单位：元）',
	`after_sale_received_warehouse_amt` DECIMAL(38,18) COMMENT '已到货售后总金额（仓配问题）（单位：元）',
	`after_sale_received_other_amt` DECIMAL(38,18) COMMENT '已到货售后总金额（其他问题）（单位：元）',
	`delivery_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额（单位：元）',
	`after_sale_received_ratio` DECIMAL(38,18) COMMENT '已到货售后率（已到货售后总金额/履约应付总金额）',
	`delivery_evaluation_low_cnt` BIGINT COMMENT '司机评价差评数(3星以下)',
	`delivery_evaluation_cnt` BIGINT COMMENT '司机评价总数'
) 
COMMENT '客服KPI指标表，包含客服团队的售后金额、履约金额、售后率、司机评价等关键绩效指标'
PARTITIONED BY (`ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='客服KPI指标表，用于监控和分析客服团队的绩效表现') 
LIFECYCLE 30;