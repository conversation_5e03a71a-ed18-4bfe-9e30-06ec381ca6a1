CREATE TABLE IF NOT EXISTS app_self_cust_delivery_kpi_mi(
	`month` STRING COMMENT '日期，格式为yyyyMM，表示年月',
	`cust_team` STRING COMMENT '客户团队类型，取值范围：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户',
	`origin_total_amt` DECIMAL(38,18) COMMENT '原始总金额',
	`real_total_amt` DECIMAL(38,18) COMMENT '实际总金额',
	`cost_amt` DECIMAL(38,18) COMMENT '成本',
	`timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送原始总金额',
	`timing_real_total_amt` DECIMAL(38,18) COMMENT '省心送实际总金额',
	`cust_cnt` BIGINT COMMENT '客户数',
	`order_cnt` BIGINT COMMENT '订单数',
	`point_cnt` BIGINT COMMENT '点位数',
	`day_point_cnt` BIGINT COMMENT '日均点位',
	`sku_cnt` BIGINT COMMENT 'SKU数量',
	`delivery_amt` DECIMAL(38,18) COMMENT '运费',
	`after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后总金额',
	`storage_amt` DECIMAL(38,18) COMMENT '仓储成本',
	`arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本',
	`deliver_amt` DECIMAL(38,18) COMMENT '配送成本',
	`self_picked_amt` DECIMAL(38,18) COMMENT '采购自提成本',
	`other_amt` DECIMAL(38,18) COMMENT '其他成本',
	`allocation_amt` DECIMAL(38,18) COMMENT '调拨成本',
	`cust_runoff` DECIMAL(38,18) COMMENT '客户流失率，取值范围0-1之间的小数'
) 
COMMENT '履约口径KPI指标日汇总表（自营），包含自营业务的各项履约相关KPI指标数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='自营业务履约KPI指标日度汇总表，用于监控和分析自营业务的履约表现') 
LIFECYCLE 30;