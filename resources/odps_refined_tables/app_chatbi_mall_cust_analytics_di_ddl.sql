```sql
CREATE TABLE IF NOT EXISTS app_chatbi_mall_cust_analytics_di(
    `cust_id` BIGINT COMMENT '客户ID，与埋点/订单里的cust_id一致，数值型标识',
    `cust_name` STRING COMMENT '客户名称/门店名称，文本类型',
    `cust_phone` STRING COMMENT '客户电话，文本类型',
    `cust_register_province` STRING COMMENT '客户注册省份，比如浙江、江苏、上海、广东，文本类型',
    `cust_register_city` STRING COMMENT '客户注册城市，比如杭州市、宁波市、苏州市、深圳市，文本类型',
    `cust_register_area` STRING COMMENT '客户注册行政区/区县，比如西湖区、江北区、姑苏区、福田区、连江县，文本类型',
    `cust_register_date` STRING COMMENT '客户注册日期，格式：yyyyMMdd，年月日格式',
    `is_new_register_cust` BIGINT COMMENT '是否为当日新注册客户，1是，0否；用于衡量新增，枚举值：0-否，1-是',
    `cust_type` STRING COMMENT '客户类型，枚举：面包蛋糕/茶饮/咖啡/其他/甜品冰淇淋/西餐等，文本类型',
    `area_name` STRING COMMENT '运营区域名称，比如杭州、宁波、苏州，文本类型',
    `area_no` BIGINT COMMENT '运营区域编号，数值型标识',
    `admin_name` STRING COMMENT '大客户名称；为空则代表平台客户/单店，文本类型',
    `admin_id` STRING COMMENT '大客户ID；保持字符串避免跨系统类型不一致，文本类型',
    `bd_id` BIGINT COMMENT '客户所属BD ID（仅对私海客户有值），数值型标识，-1表示无BD',
    `bd_name` STRING COMMENT '客户所属BD姓名（仅对私海客户有值），文本类型',
    `cust_sku_viewed_cnt` BIGINT COMMENT '当日该客户浏览过的SKU去重数量，用于衡量浏览活跃度，数值型计数',
    `cust_sku_clicked_cnt` BIGINT COMMENT '当日该客户点击过的SKU去重数量，事件类型=cl，数值型计数',
    `cust_stay_time_minutes` DECIMAL(18,2) COMMENT '当日累计停留时长(分钟)，数值型时长，保留2位小数',
    `cust_searched_cnt` BIGINT COMMENT '当日搜索行为次数，数值型计数',
    `cust_first_order_date` STRING COMMENT '客户首单日期，格式如：20250101；来自订单宽表，年月日格式',
    `is_cust_first_order_date` BIGINT COMMENT '当日是否为客户首单日，1是，0否，枚举值：0-否，1-是',
    `cust_order_cnt` BIGINT COMMENT '当日下单的去重订单数，数值型计数',
    `cust_order_real_total_amt` DECIMAL(38,18) COMMENT '当日订单实际总金额(优惠后)，数值型金额，保留18位小数',
    `cust_order_origin_total_amt` DECIMAL(38,18) COMMENT '当日订单原始总金额(优惠前)，数值型金额，保留18位小数',
    `cust_order_sku_ids` BIGINT COMMENT '当日下单涉及的去重SKU数量，数值型计数',
    `cust_order_sku_cnt` BIGINT COMMENT '当日下单SKU件数合计，数值型计数'
) 
COMMENT '商城客户行为与订单分析按日明细表：聚合了埋点日志(dwd_log_mall_di)与订单(dwd_trd_order_df)的关键客户维度指标，用于新增、活跃、转化等分析'
PARTITIONED BY (
    `ds` STRING COMMENT '分区日期，格式：yyyymmdd，年月日格式'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment'='商城客户行为与订单分析按日明细表，包含客户基本信息、行为指标和订单指标',
    'columnar.nested.type'='true'
)
LIFECYCLE 1000;
```