CREATE TABLE IF NOT EXISTS app_service_area_warehouse_cost_wi(
	`year` STRING COMMENT '年份，格式：YYYY',
	`week_of_year` STRING COMMENT '周数，取值范围：1-53',
	`monday` STRING COMMENT '周一日期，格式：yyyyMMdd，表示年月日',
	`sunday` STRING COMMENT '周日日期，格式：yyyyMMdd，表示年月日',
	`service_area` STRING COMMENT '服务区域，取值范围：华东、华中、华北、华南、华西、广西、昆明、福建、贵阳等',
	`total_storage_amt` DECIMAL(38,18) COMMENT '总仓储费用（元）',
	`self_storage_amt` DECIMAL(38,18) COMMENT '自营业务总仓储费用（元）',
	`heytea_storage_amt` DECIMAL(38,18) COMMENT '喜茶业务总仓储费用（元）',
	`warehouse_fixed_amt` DECIMAL(38,18) COMMENT '仓储固定资产折旧费用（元）',
	`trunk_fixed_amt` DECIMAL(38,18) COMMENT '干线固定资产折旧费用（元）',
	`total_out_sku_cnt` BIGINT COMMENT '总出库商品件数',
	`self_out_sku_cnt` BIGINT COMMENT '自营业务出库商品件数',
	`heytea_out_sku_cnt` BIGINT COMMENT '喜茶业务出库商品件数',
	`product_loss_amt` DECIMAL(38,18) COMMENT '产品损耗费用（元）',
	`self_amt` DECIMAL(38,18) COMMENT '自提费用（元）',
	`allocate_amt` DECIMAL(38,18) COMMENT '调拨费用（元）',
	`nodelivery_amt` DECIMAL(38,18) COMMENT '非履约费用（元）',
	`trunk_total_amt` DECIMAL(38,18) COMMENT '总干线费用（元）',
	`self_trunk_amt` DECIMAL(38,18) COMMENT '自营业务干线费用（元）',
	`heytea_trunk_amt` DECIMAL(38,18) COMMENT '喜茶业务干线费用（元）',
	`self_trunk_km_cnt` DECIMAL(38,18) COMMENT '自营业务干线总公里数',
	`big_cust_total_amt` DECIMAL(38,18) COMMENT '大客户用车金额（元）',
	`out_point_cnt` BIGINT COMMENT '外区点位数',
	`in_point_cnt` BIGINT COMMENT '内区点位数',
	`total_point_cnt` BIGINT COMMENT '总点位数',
	`heytea_point_cnt` BIGINT COMMENT '喜茶点位数',
	`self_point_cnt` BIGINT COMMENT '自营点位数',
	`heytea_deliver_amt` DECIMAL(38,18) COMMENT '喜茶配送费用（元）',
	`self_delivery_amt` DECIMAL(38,18) COMMENT '自营配送费用（元）',
	`total_delivery_amt` DECIMAL(38,18) COMMENT '总配送费用（元）'
) 
COMMENT '仓储成本汇总表，按服务区域统计每周的仓储、干线、配送等各项成本费用'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：yyyyMMdd，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='仓储成本汇总表，按周维度统计各服务区域的仓储运营成本数据') 
LIFECYCLE 30;