```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_after_sale_rate_di` (
  `city_id` BIGINT COMMENT '城市ID，唯一标识城市的数字编码',
  `city_name` STRING COMMENT '城市名称，如：杭州、上海等',
  `sku_id` STRING COMMENT '商品SKU编码，唯一标识具体商品规格',
  `sku_type` STRING COMMENT '商品类型：自营-平台自营商品，代仓-第三方仓库代发商品',
  `spu_name` STRING COMMENT '商品名称，即商品的标准产品名称',
  `sku_disc` STRING COMMENT 'SKU描述，包含规格信息如：1L*12盒、400g*1包等',
  `category1` STRING COMMENT '一级类目，商品所属的大类，如：鲜果、其他等',
  `warehouse_no` BIGINT COMMENT '库存仓编号，唯一标识仓库的数字编码',
  `warehouse_name` STRING COMMENT '库存仓名称，如：嘉兴总仓等',
  `external_asl_amt` DECIMAL(38,18) COMMENT '客户原因或未配送售后金额，单位：元',
  `external_asl_suborder_cnt` BIGINT COMMENT '客户原因或未配送售后子订单数量',
  `short_asl_amt` DECIMAL(38,18) COMMENT '缺货售后金额，单位：元',
  `short_asl_suborder_cnt` BIGINT COMMENT '缺货售后子订单数量',
  `quality_asl_amt` DECIMAL(38,18) COMMENT '质量售后金额，单位：元',
  `quality_asl_suborder_cnt` BIGINT COMMENT '质量售后子订单数量',
  `less_asl_amt` DECIMAL(38,18) COMMENT '少称售后金额，单位：元',
  `less_asl_suborder_cnt` BIGINT COMMENT '少称售后子订单数量',
  `other_asl_amt` DECIMAL(38,18) COMMENT '其他售后金额，单位：元',
  `other_asl_suborder_cnt` BIGINT COMMENT '其他售后子订单数量',
  `asl_amt` DECIMAL(38,18) COMMENT '总售后金额，单位：元',
  `asl_suborder_cnt` BIGINT COMMENT '总售后子订单数量',
  `dlv_real_gmv_with_coupon` DECIMAL(38,18) COMMENT '配送实付金额+券优惠金额，单位：元',
  `dlv_sub_order_cnt` BIGINT COMMENT '配送子订单数量'
)
COMMENT '售后率统计表，按城市、商品SKU、仓库维度统计各类售后情况的金额和订单数量，用于分析售后率指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment'='售后率分析表，包含各类售后原因的金额和订单统计',
  'lifecycle'='30'
)
```