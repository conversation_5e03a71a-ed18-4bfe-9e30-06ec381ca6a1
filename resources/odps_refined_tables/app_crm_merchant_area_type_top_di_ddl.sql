CREATE TABLE IF NOT EXISTS app_crm_merchant_area_type_top_di(
	`month_tag` STRING COMMENT '月份标记，格式为yyyyMM，如202509表示2025年9月',
	`month_type` BIGINT COMMENT '所选时间类型：0-本月（枚举值：0）',
	`type` STRING COMMENT '商户经营类型',
	`area_no` BIGINT COMMENT '运营区域编号，取值范围：1001-44269',
	`category_id_list` STRING COMMENT '品类TOP10，以英文逗号分隔',
	`pd_id_list` STRING COMMENT '商品SPU TOP10，以英文逗号分隔'
) 
COMMENT '商户同区域同行业品类和商品SPU的TOP10排名表'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='基于商户经营类型、运营区域和时间维度的品类和商品SPU排名统计表') 
LIFECYCLE 30;