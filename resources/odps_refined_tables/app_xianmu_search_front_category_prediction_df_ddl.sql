CREATE TABLE IF NOT EXISTS app_xianmu_search_front_category_prediction_df(
	`query` STRING COMMENT '搜索词，用户输入的查询关键词',
	`predicated_front_category_name` STRING COMMENT '预测的前端类目名称，基于用户点击历史预测的类目结果，多个类目用逗号分隔'
)
COMMENT '根据用户的点击历史记录做的query-前端类目的预测结果表，来源于app_xianmu_search_category_prediction_df。包含搜索词与预测类目的映射关系'
PARTITIONED BY (
	`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES ('comment'='搜索词前端类目预测结果表，用于搜索推荐和类目优化')
LIFECYCLE 180;