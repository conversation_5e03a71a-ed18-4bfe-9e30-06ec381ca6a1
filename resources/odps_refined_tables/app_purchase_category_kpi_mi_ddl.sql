CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_purchase_category_kpi_mi`(
  `month` STRING COMMENT '日期，格式为yyyyMM，表示年月',
  `category` STRING COMMENT '商品品类，取值范围：鲜果、乳制品、其他',
  `origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额，采购订单的应付金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额，采购订单的实际支付金额',
  `cost_amt` DECIMAL(38,18) COMMENT '商品成本，采购商品的成本金额',
  `store_cost_amt` DECIMAL(38,18) COMMENT '期末库存成本，期末库存的商品成本金额',
  `sale_amt` DECIMAL(38,18) COMMENT '销售出库成本，销售出库的商品成本金额',
  `on_sale_sku_cnt` BIGINT COMMENT '有销售及自提出库的sku数，有销售或自提出库记录的商品SKU数量',
  `init_sku_cnt` BIGINT COMMENT '有期初库存的sku数，期初有库存的商品SKU数量',
  `sale_out_time` DECIMAL(38,18) COMMENT '售罄时长，商品从入库到售罄的时间（单位：小时）',
  `on_sale_time` DECIMAL(38,18) COMMENT '上架时长，商品从上架到售罄的时间（单位：小时）',
  `check_sku_cnt` BIGINT COMMENT '抽检数量，抽检的商品数量',
  `qualified_cnt` BIGINT COMMENT '合格数量，抽检合格的商品数量',
  `after_sale_amt` DECIMAL(38,18) COMMENT '采购责售后金额，采购责任导致的售后赔付金额'
)
COMMENT '采购KPI统计表，按月份和商品品类统计采购相关的关键绩效指标，包括金额、成本、库存、销售、质检等维度数据'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，yyyyMMdd格式，表示年月日'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '采购KPI统计表，用于采购业务分析和绩效监控',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;