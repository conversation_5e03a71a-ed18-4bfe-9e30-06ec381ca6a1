```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_cust_lifecycle_progress_mi` (
  `month` STRING COMMENT '月份，格式：yyyyMM，表示数据统计的月份',
  `cust_team` STRING COMMENT '客户团队类型，枚举：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户',
  `cust_type` STRING COMMENT '客户类型，枚举：面包蛋糕、西餐、咖啡、甜品冰淇淋、茶饮、其他',
  `register_province` STRING COMMENT '客户注册时所在省份',
  `register_city` STRING COMMENT '客户注册时所在城市',
  `life_cycle_detail` STRING COMMENT '客户生命周期标签（细分），如：A2、A3、B2、L1、L2等',
  `progress_type` STRING COMMENT '进度类型，枚举：下2-3天、下1天、未下单等',
  `cust_cnt` BIGINT COMMENT '总客户数量',
  `close_cust_cnt` BIGINT COMMENT '当月闭店客户数量',
  `order_amt_avg_1_cust_cnt` BIGINT COMMENT '当月次均价0元客户数量',
  `order_amt_avg_2_cust_cnt` BIGINT COMMENT '当月次均价0-50元客户数量',
  `order_amt_avg_3_cust_cnt` BIGINT COMMENT '当月次均价50-200元客户数量',
  `order_amt_avg_4_cust_cnt` BIGINT COMMENT '当月次均价200-400元客户数量',
  `order_amt_avg_5_cust_cnt` BIGINT COMMENT '当月次均价400-600元客户数量',
  `order_amt_avg_6_cust_cnt` BIGINT COMMENT '当月次均价600元以上客户数量',
  `fruit_amt` DECIMAL(38,18) COMMENT '当月鲜果品类实付GMV（总交易额）',
  `fruit_cust_cnt` BIGINT COMMENT '当月购买鲜果品类客户数量',
  `dairy_amt` DECIMAL(38,18) COMMENT '当月乳制品品类实付GMV（总交易额）',
  `dairy_cust_cnt` BIGINT COMMENT '当月购买乳制品品类客户数量',
  `other_amt` DECIMAL(38,18) COMMENT '当月其他品类实付GMV（总交易额）',
  `other_cust_cnt` BIGINT COMMENT '当月购买其他品类客户数量',
  `self_amt` DECIMAL(38,18) COMMENT '当月自营品牌实付GMV（总交易额）',
  `self_cust_cnt` BIGINT COMMENT '当月购买自营品牌客户数量',
  `spu_cnt_avg` DECIMAL(38,18) COMMENT '当月平均SPU（标准化产品单元）数量',
  `fruit_spu_cnt_avg` DECIMAL(38,18) COMMENT '当月平均鲜果品类SPU数量',
  `dairy_spu_cnt_avg` DECIMAL(38,18) COMMENT '当月平均乳制品品类SPU数量',
  `other_spu_cnt_avg` DECIMAL(38,18) COMMENT '当月平均标品SPU数量',
  `lastmonth_order_amt_avg_1_cust_cnt` BIGINT COMMENT '上月次均价0元客户数量',
  `lastmonth_order_amt_avg_2_cust_cnt` BIGINT COMMENT '上月次均价0-50元客户数量',
  `lastmonth_order_amt_avg_3_cust_cnt` BIGINT COMMENT '上月次均价50-200元客户数量',
  `lastmonth_order_amt_avg_4_cust_cnt` BIGINT COMMENT '上月次均价200-400元客户数量',
  `lastmonth_order_amt_avg_5_cust_cnt` BIGINT COMMENT '上月次均价400-600元客户数量',
  `lastmonth_order_amt_avg_6_cust_cnt` BIGINT COMMENT '上月次均价600元以上客户数量',
  `lastmonth_fruit_amt` DECIMAL(38,18) COMMENT '上月鲜果品类实付GMV（总交易额）',
  `lastmonth_fruit_cust_cnt` BIGINT COMMENT '上月购买鲜果品类客户数量',
  `lastmonth_dairy_amt` DECIMAL(38,18) COMMENT '上月乳制品品类实付GMV（总交易额）',
  `lastmonth_dairy_cust_cnt` BIGINT COMMENT '上月购买乳制品品类客户数量',
  `lastmonth_other_amt` DECIMAL(38,18) COMMENT '上月其他品类实付GMV（总交易额）',
  `lastmonth_other_cust_cnt` BIGINT COMMENT '上月购买其他品类客户数量',
  `lastmonth_self_amt` DECIMAL(38,18) COMMENT '上月自营品牌实付GMV（总交易额）',
  `lastmonth_self_cust_cnt` BIGINT COMMENT '上月购买自营品牌客户数量',
  `lastmonth_spu_cnt_avg` DECIMAL(38,18) COMMENT '上月平均SPU（标准化产品单元）数量',
  `lastmonth_fruit_spu_cnt_avg` DECIMAL(38,18) COMMENT '上月平均鲜果品类SPU数量',
  `lastmonth_dairy_spu_cnt_avg` DECIMAL(38,18) COMMENT '上月平均乳制品品类SPU数量',
  `lastmonth_other_spu_cnt_avg` DECIMAL(38,18) COMMENT '上月平均标品SPU数量'
) 
COMMENT '客户进度月汇总表，按月统计客户生命周期进度、购买行为、品类偏好等关键指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据加载日期'
)
STORED AS ALIORC  
TBLPROPERTIES (
  'comment'='客户进度月维度汇总表，包含客户团队类型、客户类型、生命周期标签、购买行为等多维度统计指标',
  'columnar.nested.type'='true'
) 
LIFECYCLE 30;
```