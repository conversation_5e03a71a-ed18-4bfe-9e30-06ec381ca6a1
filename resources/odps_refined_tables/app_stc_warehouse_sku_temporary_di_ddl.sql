```sql
CREATE TABLE IF NOT EXISTS app_stc_warehouse_sku_temporary_di(
    warehouse_no BIGINT COMMENT '库存仓编号',
    warehouse_name STRING COMMENT '库存仓名称',
    sku_id STRING COMMENT 'SKU编号，商品最小库存单位标识',
    spu_name STRING COMMENT 'SPU名称，标准产品单元名称',
    category1 STRING COMMENT '一级类目名称',
    category4_id STRING COMMENT '四级类目ID',
    category4 STRING COMMENT '四级类目名称',
    sku_brand STRING COMMENT 'SKU品牌名称',
    sku_property STRING COMMENT 'SKU性质：常规、临保、拆包、破袋',
    disc STRING COMMENT 'SKU描述，包含规格包装信息',
    warn_days BIGINT COMMENT '到期预警天数，距离保质期到期前的预警天数',
    unit_amt DECIMAL(38,18) COMMENT '成本单价，单位成本金额',
    store_quantity BIGINT COMMENT '在仓库存数量，当前仓库中的库存数量',
    sale_out_quality BIGINT COMMENT '历史两周销售出库数量，过去14天的销售出库量',
    allocate_out_quality BIGINT COMMENT '历史两周调拨出库数量，过去14天的调拨出库量',
    avg_quality BIGINT COMMENT '过去2周日均出库量，日均出库数量',
    temporary_date STRING COMMENT '临保日期，格式为yyyyMMdd，商品临保日期',
    estimated_date DATETIME COMMENT '预计售罄日期，格式为yyyy-MM-dd HH:mm:ss，预计商品售完日期',
    temporary_cnt BIGINT COMMENT '预计临保件数，预计会临保的商品数量',
    temporary_amt DECIMAL(38,18) COMMENT '预计临保成本，临保商品的总成本金额',
    estimated_sale_cnt DECIMAL(38,18) COMMENT '预估出库数量，预计未来出库的商品数量',
    quality_date STRING COMMENT '保质期日期，格式为yyyyMMdd，商品保质期到期日期',
    damage_date DATETIME COMMENT '货损时间，格式为yyyy-MM-dd HH:mm:ss，商品可能发生货损的日期',
    doc BIGINT COMMENT '在库/预销数量，当前在库或预销的商品数量',
    doc_cnt BIGINT COMMENT 'doc汇总数量，在库预销数量的汇总',
    damage_time BIGINT COMMENT '距离货损时长，距离货损日期的天数',
    quality_time BIGINT COMMENT '距离临保时长，距离临保日期的天数',
    damage_rask STRING COMMENT '货损风险：-表示无风险，货损风险表示有货损风险',
    temporary_rask STRING COMMENT '临保风险：-表示无风险，临保风险表示有临保风险',
    sky_type STRING COMMENT '仓储类型：自营、代仓、代售',
    estimated_damage_cnt BIGINT COMMENT '预计货损件数，预计会发生货损的商品数量',
    estimated_damage_amt DECIMAL(38,18) COMMENT '预计货损成本，货损商品的总成本金额',
    quality_date_tag STRING COMMENT '保质期标签：60天内、60天外，基于距离保质期的时间分类',
    temporary_date_tag STRING COMMENT '临保日期标签：60天内、60天外，基于距离临保日期的时间分类',
    temporary_tag STRING COMMENT '是否有临保风险：是、否，标识商品是否存在临保风险'
)
COMMENT '库存仓和SKU维度的临保数据表，包含商品库存、临保预警、货损风险等关键指标'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，数据统计日期')
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = '库存仓SKU临保数据分析表，用于监控商品临保状态和库存风险管理',
    'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```