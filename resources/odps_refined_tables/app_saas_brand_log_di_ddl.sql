```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_saas_brand_log_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
  `brand_alias` STRING COMMENT '品牌名称，SaaS平台上的品牌别名',
  `login_pv` BIGINT COMMENT '总登录PV（页面浏览量），统计品牌用户登录页面的总访问次数',
  `login_uv` BIGINT COMMENT '总登录UV（独立访客数），统计品牌独立用户的登录次数',
  `mall_pv` BIGINT COMMENT '商场模块PV，统计品牌用户在商场模块的页面浏览量',
  `mall_uv` BIGINT COMMENT '商场模块UV，统计品牌用户在商场模块的独立访客数',
  `customer_pv` BIGINT COMMENT '门店模块PV，统计品牌用户在门店管理模块的页面浏览量',
  `customer_uv` BIGINT COMMENT '门店模块UV，统计品牌用户在门店管理模块的独立访客数',
  `goods_pv` BIGINT COMMENT '商品模块PV，统计品牌用户在商品管理模块的页面浏览量',
  `goods_uv` BIGINT COMMENT '商品模块UV，统计品牌用户在商品管理模块的独立访客数',
  `order_pv` BIGINT COMMENT '订单模块PV，统计品牌用户在订单管理模块的页面浏览量',
  `order_uv` BIGINT COMMENT '订单模块UV，统计品牌用户在订单管理模块的独立访客数',
  `purchasing_pv` BIGINT COMMENT '采购模块PV，统计品牌用户在采购管理模块的页面浏览量',
  `purchasing_uv` BIGINT COMMENT '采购模块UV，统计品牌用户在采购管理模块的独立访客数',
  `store_pv` BIGINT COMMENT '仓库模块PV，统计品牌用户在仓库管理模块的页面浏览量',
  `store_uv` BIGINT COMMENT '仓库模块UV，统计品牌用户在仓库管理模块的独立访客数',
  `data_pv` BIGINT COMMENT '数据模块PV，统计品牌用户在数据分析模块的页面浏览量',
  `data_uv` BIGINT COMMENT '数据模块UV，统计品牌用户在数据分析模块的独立访客数',
  `finance_pv` BIGINT COMMENT '财务模块PV，统计品牌用户在财务管理模块的页面浏览量',
  `finance_uv` BIGINT COMMENT '财务模块UV，统计品牌用户在财务管理模块的独立访客数'
)
COMMENT 'SaaS品牌埋点数据表，记录各品牌在SaaS平台各功能模块的访问行为数据，用于分析品牌使用情况和用户行为'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据加载的分区日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = 'SaaS品牌埋点数据表，包含各品牌在各功能模块的PV/UV统计',
  'lifecycle' = '30'
)
```