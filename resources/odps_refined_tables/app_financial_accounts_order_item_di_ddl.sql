CREATE TABLE IF NOT EXISTS app_financial_accounts_order_item_di(
	`tenant_id` BIGINT COMMENT '租户ID，取值范围：8-123',
	`brand_id` BIGINT COMMENT '品牌ID，取值范围：113-1180838',
	`type` BIGINT COMMENT '费用类型枚举：0-订单明细，1-售后单明细',
	`order_no` STRING COMMENT '订单号',
	`order_item_id` BIGINT COMMENT '订单明细ID，取值范围：1776080-1787110',
	`order_time` DATETIME COMMENT '下单时间，格式：年月日时分秒',
	`after_sale_order_no` STRING COMMENT '售后单号',
	`store_id` BIGINT COMMENT '门店ID，取值范围：410-540093',
	`store_name` STRING COMMENT '门店名称',
	`delivery_address` STRING COMMENT '配送地址',
	`sku` STRING COMMENT '商品SKU编码',
	`pd_id` BIGINT COMMENT '商品PDID，取值范围：28-15887',
	`pd_name` STRING COMMENT '商品名称',
	`weight` STRING COMMENT '商品规格描述',
	`sub_type` BIGINT COMMENT '商品二级性质枚举：1-自营代销不入仓，2-自营代销入仓，3-自营经销，4-代仓代仓',
	`order_type` STRING COMMENT '订单类型/售后单类型',
	`handle_type` STRING COMMENT '售后服务原因',
	`remark` STRING COMMENT '订单备注/售后原因',
	`finish_time` DATETIME COMMENT '履约/售后完成时间，格式：年月日时分秒',
	`quantity` BIGINT COMMENT '下单个数/售后数量，取值范围：1-3952',
	`payable_amount` DECIMAL(38,18) COMMENT '应付单价',
	`actually_paid_amount` DECIMAL(38,18) COMMENT '实付单价',
	`total_actually_paid_amount` DECIMAL(38,18) COMMENT '实付/售后总价',
	`delivery_fee` DECIMAL(38,18) COMMENT '运费',
	`item_unit` STRING COMMENT '售后单位'
) 
COMMENT '账期订单费用项明细表，包含订单和售后单的费用明细数据，用于财务账期结算和分析'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据日期')
STORED AS ALIORC
TBLPROPERTIES ('comment'='账期订单费用项明细表，记录订单和售后单的财务明细信息')
LIFECYCLE 30;