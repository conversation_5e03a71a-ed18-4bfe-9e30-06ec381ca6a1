CREATE TABLE IF NOT EXISTS app_kpi_cust_sku_trade_mi(
	month STRING COMMENT '月份，格式为yyyyMM',
	cust_class STRING COMMENT '客户类型，取值范围：大客户（茶百道）、大客户（非茶百道）、批发、普通（非品牌）、普通（品牌）',
	sku_type STRING COMMENT '商品类型，取值范围：自营、代仓',
	origin_total_amt DECIMAL(38,18) COMMENT '应付总金额，原始订单总金额',
	real_total_amt DECIMAL(38,18) COMMENT '实付总金额，实际支付金额',
	cust_cnt BIGINT COMMENT '客户数，去重后的客户数量',
	order_cnt BIGINT COMMENT '订单数，订单总数',
	delivery_amt DECIMAL(38,18) COMMENT '应付运费金额，运费金额',
	out_times_amt DECIMAL(38,18) COMMENT '超时加单费，超时产生的额外费用'
) 
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='交易（订单）KPI金额汇总表，包含客户类型、商品类型的交易金额、客户数、订单数等关键指标统计') 
LIFECYCLE 30;