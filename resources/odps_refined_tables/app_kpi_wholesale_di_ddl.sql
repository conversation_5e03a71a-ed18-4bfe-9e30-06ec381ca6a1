CREATE TABLE IF NOT EXISTS app_kpi_wholesale_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务发生日期',
	`sku_type` STRING COMMENT '商品类型; 取值范围：自营/代仓',
	`order_gmv_amt` DECIMAL(38,18) COMMENT '交易应付总金额，单位：元',
	`deliver_gmv_amt` DECIMAL(38,18) COMMENT '履约应付总金额，单位：元',
	`deliver_cust_cnt` BIGINT COMMENT '履约客户数，统计当日完成履约的独立客户数量',
	`deliver_cust_arpu` DECIMAL(38,18) COMMENT '履约ARPU(应付总金额/客户数)，单位：元/客户',
	`deliver_order_cnt` BIGINT COMMENT '履约订单数，统计当日完成履约的订单数量',
	`deliver_order_avg` DECIMAL(38,18) COMMENT '履约订单均价(应付总金额/订单数)，单位：元/订单',
	`deliver_cost_amt` DECIMAL(38,18) COMMENT '履约总成本，单位：元',
	`deliver_gross_profit` DECIMAL(38,18) COMMENT '履约毛利润(应付总金额-总成本)，单位：元',
	`deliver_gross_profit_rate` DECIMAL(38,18) COMMENT '履约毛利率，计算公式：(毛利润/应付总金额)*100%'
) 
COMMENT '交易口径KPI指标日汇总表，包含批发业务的核心交易和履约指标数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据统计日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='交易口径KPI指标日汇总表，用于批发业务的日常经营分析') 
LIFECYCLE 30;