CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_sku_marketing_mi`(
	`month` STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
	`sku_id` STRING COMMENT 'SKU编码，商品最小库存单位的唯一标识',
	`spu_name` STRING COMMENT '商品名称，即SPU名称，表示标准产品单元',
	`sku_disc` STRING COMMENT '商品描述，包含规格信息，如10KG*1箱表示10公斤每箱',
	`category1` STRING COMMENT '一级类目，商品所属的一级分类，如乳制品',
	`coupon_order_label` DECIMAL(38,18) COMMENT '营销费用订单占比，取值范围0-1，表示优惠券费用占订单金额的比例',
	`cust_cnt` BIGINT COMMENT '客户数，统计周期内购买该SKU的客户数量，取值范围1-3333'
)
COMMENT '人群营销结构表，记录商品营销相关的客户行为和费用占比数据'
PARTITIONED BY (
	`ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示数据日期，如20250917表示2025年9月17日'
)
STORED AS ALIORC
TBLPROPERTIES ('comment'='人群营销结构分析表，用于分析商品营销效果和客户购买行为') 
LIFECYCLE 30;