CREATE TABLE IF NOT EXISTS app_pcs_saas_purchase_detail_report_df(
	`purchase_date` DATETIME COMMENT '采购日期，格式为年月日时分秒',
	`purchase_no` STRING COMMENT '采购批次编号',
	`purchaser` STRING COMMENT '采购人标识',
	`warehouse_no` BIGINT COMMENT '仓库编号，取值范围：1-158',
	`warehouse_name` STRING COMMENT '仓库名称',
	`purchase_status` STRING COMMENT '采购单状态，枚举值：全部入库、部分入库',
	`sku_id` BIGINT COMMENT '商品SKU ID，取值范围：100080-118162',
	`spu_id` BIGINT COMMENT '商品SPU ID，取值范围：1496-111149',
	`xianmu_sku` STRING COMMENT '鲜沐SKU编码',
	`xianmu_spu_id` BIGINT COMMENT '鲜沐SPU ID，取值范围：1320-13203',
	`name` STRING COMMENT '商品名称',
	`supplier_id` BIGINT COMMENT '供应商ID，取值范围：1823-3047',
	`supplier` STRING COMMENT '供应商名称',
	`specification` STRING COMMENT '商品规格描述',
	`unit` STRING COMMENT '规格单位',
	`inbound_status` STRING COMMENT '入库状态，枚举值：部分入库',
	`inbound_date` DATETIME COMMENT '入库时间，格式为年月日时分秒',
	`purchase_quantity` BIGINT COMMENT '采购数量，取值范围：1-1000000',
	`purchase_amount` DECIMAL(38,18) COMMENT '采购金额',
	`inbound_quantity` BIGINT COMMENT '实际入库数量，取值范围：0-1000000',
	`inbound_amount` DECIMAL(38,18) COMMENT '实际入库金额',
	`tenant_id` BIGINT COMMENT '租户ID，取值范围：2-102',
	`category_id` BIGINT COMMENT '三级类目ID，取值范围：526-1169',
	`time_tag` STRING COMMENT '时间标签，格式为yyyyMMdd',
	`warehouse_service_provider` STRING COMMENT '仓库服务商名称',
	`back_amount` DECIMAL(38,18) COMMENT '退货金额，批次+sku累计退货金额',
	`back_quantity` BIGINT COMMENT '退货数量，批次+sku累计退货数量，取值范围：0-500',
	`brand_name` STRING COMMENT '品牌名称，枚举值：测试、None',
	`price` DECIMAL(38,18) COMMENT '采购单价'
) 
COMMENT 'SaaS采购明细表，记录采购订单的详细信息，包括采购、入库、退货等全流程数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='SaaS采购明细结果表，包含采购全流程的详细数据记录') 
LIFECYCLE 30;