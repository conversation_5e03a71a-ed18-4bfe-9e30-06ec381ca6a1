CREATE TABLE IF NOT EXISTS app_kpi_operate_large_category_delivery_mi(
	`month` STRING COMMENT '月份，格式：yyyyMM',
	`large_area_name` STRING COMMENT '运营服务大区名称',
	`category` STRING COMMENT '商品品类：鲜果，乳制品，其他',
	`origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV（总金额）',
	`real_total_amt` DECIMAL(38,18) COMMENT '履约实付GMV（实际支付金额）',
	`marketing_amt` DECIMAL(38,18) COMMENT '营销费用',
	`cost_amt` DECIMAL(38,18) COMMENT '成本金额',
	`origin_gross` DECIMAL(38,18) COMMENT '应付毛利润',
	`real_gross` DECIMAL(38,18) COMMENT '实付毛利润',
	`origin_gross_margin` DECIMAL(38,18) COMMENT '应付毛利率',
	`real_gross_margin` DECIMAL(38,18) COMMENT '实付毛利率',
	`cust_cnt` BIGINT COMMENT '履约客户数',
	`point_cnt` BIGINT COMMENT '点位数',
	`origin_pre_cust_price` DECIMAL(38,18) COMMENT '应付客单价',
	`real_pre_cust_price` DECIMAL(38,18) COMMENT '实付客单价',
	`timing_origin_amt` DECIMAL(38,18) COMMENT '省心送应付GMV',
	`timing_real_amt` DECIMAL(38,18) COMMENT '省心送实付GMV',
	`consign_origin_amt` DECIMAL(38,18) COMMENT '代售应付GMV',
	`consign_real_amt` DECIMAL(38,18) COMMENT '代售实付GMV',
	`consign_marketing_amt` DECIMAL(38,18) COMMENT '代售营销费用',
	`consign_origin_gross` DECIMAL(38,18) COMMENT '代售应付毛利润',
	`consign_real_gross` DECIMAL(38,18) COMMENT '代售实付毛利润',
	`consign_cust_cnt` BIGINT COMMENT '代售履约客户数',
	`storage_amt` DECIMAL(38,18) COMMENT '仓储费',
	`arterial_roads_amt` DECIMAL(38,18) COMMENT '单点干线费',
	`self_picked_amt` DECIMAL(38,18) COMMENT '采购自提费',
	`allocation_amt` DECIMAL(38,18) COMMENT '调拨费',
	`other_amt` DECIMAL(38,18) COMMENT '其他费用',
	`deliver_amt` DECIMAL(38,18) COMMENT '配送费'
) 
COMMENT '运营履约KPI表（平台客户），包含各品类在各运营大区的履约相关指标数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：yyyyMMdd') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='运营履约KPI统计表，按月份、大区、品类维度统计GMV、毛利率、客户数、费用等核心指标') 
LIFECYCLE 30;