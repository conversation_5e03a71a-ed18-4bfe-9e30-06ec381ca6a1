```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_mkt_deliver_preferential_roi_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计日期',
  `city_id` BIGINT COMMENT '运营服务区ID，取值范围：1001-44264',
  `city_name` STRING COMMENT '运营服务区名称，如：义乌、长沙普冷、上海等',
  `large_area_id` BIGINT COMMENT '运营服务大区ID，取值范围：1-91',
  `large_area_name` STRING COMMENT '运营服务大区名称，如：杭州大区、长沙大区、上海大区等',
  `cust_team` STRING COMMENT '客户团队类型，枚举值：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户',
  `life_cycle_detail` STRING COMMENT '生命周期标签（细），枚举值：S2、W、A2等，表示不同的客户生命周期阶段',
  `preferential_type` STRING COMMENT '营销活动类型，枚举值：行业活动券、临保活动等，表示不同类型的营销活动',
  `preferential_amt` DECIMAL(38,18) COMMENT '营销金额，单位为元，表示营销活动的优惠金额',
  `origin_total_amt` DECIMAL(38,18) COMMENT '履约应付金额，单位为元，表示原始应付金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '履约实付金额，单位为元，表示实际支付金额',
  `cost_amt` DECIMAL(38,18) COMMENT '履约成本金额，单位为元，表示履约成本金额'
) 
COMMENT '履约维度营销活动ROI拆解报表，用于分析营销活动在履约维度的投入产出比'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据分区日期'
)
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
               'comment'='履约维度营销活动ROI拆解报表，包含营销活动的金额、成本、客户生命周期等关键指标') 
LIFECYCLE 30;
```