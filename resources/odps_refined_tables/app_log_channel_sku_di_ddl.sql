CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_log_channel_sku_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示年月日',
  `fsku_id` STRING COMMENT 'SKU编号，商品最小库存单位标识',
  `category1` STRING COMMENT '一级类目，商品最高层级分类',
  `category2` STRING COMMENT '二级类目，商品次高层级分类',
  `category3` STRING COMMENT '三级类目，商品中层级分类',
  `category4` STRING COMMENT '四级类目，商品最细粒度分类',
  `sku_type` STRING COMMENT 'SKU类型：1-自营，2-代仓，3-代售',
  `spu_name` STRING COMMENT '商品名称，标准产品单元名称',
  `sku_disc` STRING COMMENT '商品描述，包含规格、包装等信息',
  `model` STRING COMMENT '渠道类型，如banner、search、recommend等',
  `uv` BIGINT COMMENT '曝光UV，独立访客数',
  `pv` BIGINT COMMENT '曝光PV，页面浏览量',
  `click_uv` BIGINT COMMENT '点击UV，独立点击用户数',
  `click_pv` BIGINT COMMENT '点击PV，总点击次数',
  `addbug_uv` BIGINT COMMENT '加购UV，独立加购用户数',
  `addbug_pv` BIGINT COMMENT '加购PV，总加购次数',
  `cust_cnt` BIGINT COMMENT '交易人数，完成购买的用户数量'
)
COMMENT '流量分渠道SKU转化表，记录各渠道SKU级别的流量和转化指标'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示年月日'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '流量分渠道SKU转化分析表，用于分析各渠道SKU的曝光、点击、加购和交易转化情况',
  'lifecycle' = '30'
);