```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_finance_cost_mi` (
  `month` STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
  `service_area` STRING COMMENT '大区名称',
  `warehouse_no` BIGINT COMMENT '库存仓ID，数值型标识',
  `warehouse_name` STRING COMMENT '库存仓名称',
  `category1` STRING COMMENT '商品一级类目，枚举值：鲜果/乳制品/其他',
  `sell_cost_amt` DECIMAL(38,18) COMMENT '含税销售成本，金额类型，保留18位小数',
  `damage_cost_amt` DECIMAL(38,18) COMMENT '含税货损成本，金额类型，保留18位小数',
  `sample_cost_amt` DECIMAL(38,18) COMMENT '含税出样成本，金额类型，保留18位小数',
  `stocktake_cost_amt` DECIMAL(38,18) COMMENT '含税盘盈盘亏成本，金额类型，保留18位小数',
  `sell_cost_amt_notax` DECIMAL(38,18) COMMENT '不含税销售成本，金额类型，保留18位小数',
  `damage_cost_amt_notax` DECIMAL(38,18) COMMENT '不含税货损成本，金额类型，保留18位小数',
  `sample_cost_amt_notax` DECIMAL(38,18) COMMENT '不含税出样成本，金额类型，保留18位小数',
  `stocktake_cost_amt_notax` DECIMAL(38,18) COMMENT '不含税盘盈盘亏成本，金额类型，保留18位小数'
) 
COMMENT '财务口径收入数据表，包含各仓位的销售成本、货损成本、出样成本、盘盈盘亏成本等财务数据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期，如20250917表示2025年9月17日'
)
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
               'comment'='财务成本明细表，用于财务分析和成本核算') 
LIFECYCLE 30;
```