CREATE TABLE IF NOT EXISTS app_sale_kpi_trade_mi(
	`month` STRING COMMENT '月份，格式为yyyyMM',
	`order_origin_total_amt` DECIMAL(38,18) COMMENT '交易应付总金额，单位为元',
	`order_real_total_amt` DECIMAL(38,18) COMMENT '交易实付总金额，单位为元',
	`order_cust_cnt` BIGINT COMMENT '交易客户数，取值范围：≥0',
	`order_cust_arpu` DECIMAL(38,18) COMMENT 'ARPU(应付总金额/客户数)，单位为元/客户',
	`order_cnt` BIGINT COMMENT '交易订单数，取值范围：≥0',
	`all_cust_ratio` DECIMAL(38,18) COMMENT '市占率，取值范围：0-1',
	`delivery_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额，单位为元',
	`delivery_real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额，单位为元',
	`delivery_cust_cnt` BIGINT COMMENT '履约活跃客户数，取值范围：≥0',
	`delivery_origin_profit` DECIMAL(38,18) COMMENT '履约应付毛利润，单位为元',
	`delivery_real_profit` DECIMAL(38,18) COMMENT '履约实付毛利润，单位为元',
	`delivery_after_profit` DECIMAL(38,18) COMMENT '履约后毛利润，单位为元',
	`delivery_days_avg` DECIMAL(38,18) COMMENT '履约频次，单位为次/天',
	`delivery_point_cnt` BIGINT COMMENT '履约点位数，取值范围：≥0',
	`delivery_amt` DECIMAL(38,18) COMMENT '自营履约费用，单位为元',
	`new_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '新客履约应付总金额，单位为元',
	`new_delivery_real_total_amt` DECIMAL(38,18) COMMENT '新客履约实付总金额，单位为元',
	`new_delivery_cust_cnt` BIGINT COMMENT '新客履约活跃客户数，取值范围：≥0',
	`new_delivery_real_profit` DECIMAL(38,18) COMMENT '新客履约实付毛利润，单位为元',
	`old_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '老客履约应付总金额，单位为元',
	`old_delivery_real_total_amt` DECIMAL(38,18) COMMENT '老客履约实付总金额，单位为元',
	`old_delivery_cust_cnt` BIGINT COMMENT '老客履约活跃客户数，取值范围：≥0',
	`old_delivery_real_profit` DECIMAL(38,18) COMMENT '老客履约实付毛利润，单位为元',
	`order_sku_cnt` BIGINT COMMENT '交易SKU款数，取值范围：≥0',
	`order_sku_weight` DECIMAL(38,18) COMMENT '交易SKU重量，单位为KG',
	`delivery_sku_cnt` BIGINT COMMENT '履约SKU款数，取值范围：≥0',
	`delivery_sku_weight` DECIMAL(38,18) COMMENT '履约SKU重量，单位为KG'
) 
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='销售KPI指标汇总表，包含交易和履约相关的核心业务指标数据') 
LIFECYCLE 30;