CREATE TABLE IF NOT EXISTS app_pms_sku_purchse_price_df(
	statistics_date DATETIME COMMENT '统计日期，格式为年月日时分秒',
	warehouse_no BIGINT COMMENT '仓库编号，取值范围：1-155',
	sku STRING COMMENT 'SKU编码',
	type BIGINT COMMENT '抽数类型：1-近7天采购平均价（目前只有1种类型）',
	average_price DECIMAL(38,18) COMMENT '平均价'
)
COMMENT '历史七天采购品平均价表，记录近7天SKU采购平均价格数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，yyyyMMdd格式的日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='历史七天采购品平均价表，按天分区存储采购SKU的平均价格数据') 
LIFECYCLE 30;