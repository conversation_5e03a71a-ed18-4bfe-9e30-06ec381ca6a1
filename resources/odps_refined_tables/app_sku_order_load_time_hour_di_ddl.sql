CREATE TABLE IF NOT EXISTS app_sku_order_load_time_hour_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
	`hour` BIGINT COMMENT '小时，取值范围0-23，表示一天中的具体小时',
	`sku_id` STRING COMMENT 'SKU编号，商品最小库存单位的唯一标识',
	`spu_name` STRING COMMENT '商品名称，标准产品单元的名称',
	`sku_disc` STRING COMMENT '商品描述，包含规格、包装等详细信息',
	`cust_cnt` BIGINT COMMENT '交易客户数，统计周期内购买该SKU的独立客户数量，取值范围1-101',
	`loading_cust_cnt` BIGINT COMMENT '登录客户数，统计周期内浏览或访问该SKU的独立客户数量，取值范围0-891'
) 
COMMENT '下单时间段小时偏好SKU数据表，按小时维度统计各SKU的交易和浏览客户数量，用于分析用户购买行为的时间分布特征'
PARTITIONED BY (`ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示数据所属的业务日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='下单时间段小时偏好SKU数据分析表') 
LIFECYCLE 30;