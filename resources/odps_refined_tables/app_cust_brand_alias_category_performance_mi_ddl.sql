CREATE TABLE IF NOT EXISTS app_cust_brand_alias_category_performance_mi(
	month STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
	brand_alias STRING COMMENT '品牌别名',
	category STRING COMMENT '品类:鲜果,乳制品,其他',
	order_origin_amt DECIMAL(38,18) COMMENT '交易应付总金额',
	order_real_amt DECIMAL(38,18) COMMENT '交易实付总金额',
	delivery_origin_amt DECIMAL(38,18) COMMENT '履约应付总金额',
	delivery_real_amt DECIMAL(38,18) COMMENT '履约实付总金额',
	delivery_cash_real_amt DECIMAL(38,18) COMMENT '履约现结实付总金额',
	delivery_bill_real_amt DECIMAL(38,18) COMMENT '履约账期实付总金额',
	delivery_cost_amt DECIMAL(38,18) COMMENT '履约商品成本金额',
	delivery_real_gross DECIMAL(38,18) COMMENT '履约实付毛利润',
	delivery_real_gross_rate DECIMAL(38,18) COMMENT '履约实付毛利率',
	delivery_cust_cnt BIGINT COMMENT '履约客户数，取值范围：1-431',
	delivery_point_cnt BIGINT COMMENT '履约累计点位数，取值范围：1-475',
	after_sale_received_amt DECIMAL(38,18) COMMENT '已到货售后总金额',
	after_sale_rate DECIMAL(38,18) COMMENT '售后比例(已到货售后金额/履约实付GMV)'
) 
COMMENT '大客户品类粒度监控表，按品牌别名和品类维度统计交易和履约相关指标'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='大客户品类粒度监控表，用于监控大客户在不同品牌别名和品类维度下的交易和履约表现') 
LIFECYCLE 30;