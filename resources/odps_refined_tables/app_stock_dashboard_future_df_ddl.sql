CREATE TABLE IF NOT EXISTS app_stock_dashboard_future_df(
    `view_date` DATETIME COMMENT '视图日期，格式为年月日时分秒',
    `pd_id` BIGINT COMMENT '产品ID，取值范围：2-18915',
    `sku_id` STRING COMMENT 'SKU商品编号',
    `warehouse_no` BIGINT COMMENT '仓库编号，固定值为2',
    `on_way_quantity` BIGINT COMMENT '采购在途库存数量，取值范围：0-5',
    `transfer_in_quantity` BIGINT COMMENT '调拨在途库存数量，固定值为0',
    `on_way_order_quantity` BIGINT COMMENT '采购订单在途数量，固定值为0',
    `po_on_way_quantity` BIGINT COMMENT '采购订单口径在途数量，取值范围：0-25',
    `transfer_order_in_quantity` BIGINT COMMENT '调拨单口径在途数量，固定值为0',
    `transfer_order_plan_in_quantity` BIGINT COMMENT '调拨计划入在途数量，固定值为0'
)
COMMENT '罗盘未来数据表，包含商品库存预测和未来在途数据'
PARTITIONED BY (
    `ds` BIGINT NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = '罗盘未来数据表，用于库存预测和供应链规划',
    'columnar.nested.type' = 'true'
)
LIFECYCLE 30;