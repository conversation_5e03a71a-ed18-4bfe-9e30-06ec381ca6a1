```sql
CREATE TABLE IF NOT EXISTS app_saas_merchant_store_purchase_report_di(
    `tenant_id` BIGINT COMMENT '租户ID',
    `store_id` BIGINT COMMENT '门店ID',
    `store_no_delete` BIGINT COMMENT '门店编号（已废弃字段，值为None）',
    `store_name` STRING COMMENT '门店名称',
    `type` BIGINT COMMENT '门店类型：0-直营店，1-加盟店，2-托管店',
    `group_name` STRING COMMENT '门店分组名称',
    `product_no` BIGINT COMMENT '商品编码（item_id）',
    `product_name` STRING COMMENT '商品名称',
    `specification` STRING COMMENT '商品规格描述',
    `specification_unit` STRING COMMENT '规格单位（如：箱、盒、袋等）',
    `warehouse_type` BIGINT COMMENT '商品类型：0-自营，1-经销，2-其他',
    `delivery_type` BIGINT COMMENT '仓储类型：0-自营仓，1-第三方仓（当前数据为None）',
    `market_classification` STRING COMMENT '前台商品分组',
    `category` STRING COMMENT '后台商品类目',
    `brand` STRING COMMENT '品牌（当前数据为None）',
    `quantity` BIGINT COMMENT '订单商品数量',
    `amount` DECIMAL(38,18) COMMENT '订单商品总金额',
    `delivery_time` DATETIME COMMENT '配送日期时间，格式：yyyy-MM-dd HH:mm:ss',
    `received_quantity` BIGINT COMMENT '实际收货数量',
    `reissue_quantity` BIGINT COMMENT '补发数量',
    `after_sale_amount` DECIMAL(38,18) COMMENT '售后总金额',
    `store_no` STRING COMMENT '门店编号（字符串格式）',
    `goods_type` BIGINT COMMENT '商品类型：0-无货商品，1-报价货品，2-自营货品'
)
COMMENT 'SaaS门店采购明细报表，包含门店采购订单的商品明细信息、配送信息、售后信息等'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式：yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = 'SaaS门店采购明细报表，用于分析门店采购行为和商品销售情况',
    'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```