CREATE TABLE IF NOT EXISTS app_dlv_delivery_brand_replace_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
	`brand_name` STRING COMMENT '品牌名称，即品牌公司的全称',
	`brand_alias` STRING COMMENT '大客户别称，即品牌在业务中的常用简称',
	`origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额，单位为元，保留18位小数精度',
	`real_total_amt` DECIMAL(38,18) COMMENT '实付总金额，单位为元，保留18位小数精度',
	`deliver_total_weight` DECIMAL(38,18) COMMENT '配送总重量，单位为千克，保留18位小数精度',
	`brand_cnt` BIGINT COMMENT '客户数（品牌），统计的品牌数量，取值范围为1（每个品牌计数为1）',
	`cust_cnt` BIGINT COMMENT '门店数，统计的门店数量，取值范围为1-31',
	`order_cnt` BIGINT COMMENT '订单数，统计的订单数量，取值范围为2-32',
	`data_source` STRING COMMENT '数据来源，枚举类型：鲜沐、SaaS'
) 
COMMENT '履约代仓品牌汇总表，记录代仓履约业务中各品牌的汇总统计信息'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据加载的分区日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='履约代仓品牌汇总事实表，包含品牌维度的金额、重量、数量等业务指标统计') 
LIFECYCLE 30;