CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_recommendation_final` (
  `key` STRING COMMENT '推荐key，枚举类型：product_recommend_sku-商品推荐，product_recommend_mid_v2-用户推荐',
  `hashkey` STRING COMMENT '推荐hash key，取值：m_id(用户ID)或sku(商品ID)，长度为5-11位的数字字符串',
  `value` STRING COMMENT '推荐value，格式：逗号分隔的推荐ID列表，包含10-20个推荐项'
)
COMMENT '推荐结果汇总表，将推荐结果聚合为列表格式，用于同步至Redis缓存系统'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式：yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '推荐结果汇总表，将推荐结果聚合为列表格式，用于同步至Redis缓存系统',
  'lifecycle' = '30'
);