CREATE TABLE IF NOT EXISTS app_saas_purchases_outbound_detail_aggregate_offline_df(
	`outbound_time` DATETIME COMMENT '出库时间，格式：年月日时分秒',
	`batch_no` STRING COMMENT '采购批次编号',
	`refund_batch_no` STRING COMMENT '退款单编号',
	`outbound_stock` BIGINT COMMENT '出库数量，取值范围：1-500',
	`outbound_price` DECIMAL(38,18) COMMENT '出库金额',
	`purchases_stock` BIGINT COMMENT '采购数量，取值范围：1-1300',
	`purchases_price` DECIMAL(38,18) COMMENT '采购金额',
	`sku_no` STRING COMMENT 'SKU编号',
	`sku_name` STRING COMMENT 'SKU名称',
	`specification` STRING COMMENT '商品规格',
	`packaging` STRING COMMENT '包装单位，枚举值：包、箱等',
	`saas_sku_no` STRING COMMENT 'SaaS系统SKU编号',
	`saas_sku_name` STRING COMMENT 'SaaS系统SKU名称',
	`saas_specification` STRING COMMENT 'SaaS系统商品规格',
	`saas_packaging` STRING COMMENT 'SaaS系统包装单位，枚举值：包、箱等',
	`outbound_create_user_id` BIGINT COMMENT '出库发起人用户ID',
	`outbound_create_user_name` STRING COMMENT '出库发起人姓名',
	`outbound_create_user_phone` STRING COMMENT '出库发起人电话号码',
	`inbound_create_user_id` BIGINT COMMENT '采购人用户ID',
	`inbound_create_user_name` STRING COMMENT '采购人姓名',
	`inbound_create_user_phone` STRING COMMENT '采购人电话号码',
	`warehouse_id` BIGINT COMMENT '库存仓库ID，取值范围：1-175',
	`warehouse_name` STRING COMMENT '库存仓库名称',
	`tenant_id` BIGINT COMMENT '租户ID，取值范围：2-116',
	`tenant_name` STRING COMMENT '租户名称',
	`supplier_id` BIGINT COMMENT '供应商ID，取值范围：1823-3342',
	`supplier_name` STRING COMMENT '供应商名称',
	`supplier_type` BIGINT COMMENT '供应商类型，枚举值：0-总仓供应商，1-个人供应商，2-其他类型供应商'
) 
COMMENT '采购退货出库单离线聚合表，包含采购退货出库的详细信息、商品信息、人员信息和供应商信息'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：yyyyMMdd') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='采购退货出库单离线聚合明细表，用于存储采购退货出库业务的完整数据记录') 
LIFECYCLE 30;