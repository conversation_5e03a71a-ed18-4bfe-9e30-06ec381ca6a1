```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_crm_saas_order_item_df_extra_h` (
  `order_no` STRING COMMENT '订单号，唯一标识一个订单',
  `sku_id` STRING COMMENT '订单项-SKU ID，商品库存单位的唯一标识',
  `spu_id` BIGINT COMMENT '订单项-商品SPU ID，标准产品单位的唯一标识',
  `spu_name` STRING COMMENT '订单项-商品名称，商品的标准化名称',
  `weight` STRING COMMENT '订单项-重量(规格)，商品的重量规格描述',
  `sku_cnt` BIGINT COMMENT '订单项-数量，该SKU在订单中的购买数量（范围：1-60，平均值：1.78）',
  `payable_price` DECIMAL(38,18) COMMENT '订单项-单价，商品的可支付价格，保留18位小数精度',
  `order_item_status` BIGINT COMMENT '订单项-状态：3-待配送，4-待收货，5-已收货，6-其他状态（根据数据分布，3为最常见状态）',
  `picture_path` STRING COMMENT '订单项-图片路径，商品图片的存储路径或URL地址'
)
COMMENT 'SaaS鲜沐订单明细表（小时粒度）- 包含鲜沐平台订单项级别的详细信息，包括商品信息、价格、状态等'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据所属的日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = 'SaaS鲜沐订单项明细小时表，用于存储订单项级别的交易数据',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```