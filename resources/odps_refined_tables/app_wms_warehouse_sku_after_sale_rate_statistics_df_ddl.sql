CREATE TABLE IF NOT EXISTS app_wms_warehouse_sku_after_sale_rate_statistics_df(
	pt STRING COMMENT '统计日期，格式为yyyyMMdd，表示年月日',
	warehouse_no BIGINT COMMENT '仓库编码，取值范围：2-155',
	warehouse_name STRING COMMENT '仓库名称',
	sku STRING COMMENT 'SKU编码',
	after_sale_rate_type BIGINT COMMENT '售后率类型，1-近3天售后率（目前只有类型1）',
	after_sale_rate DECIMAL(38,18) COMMENT '售后率，小数形式表示'
) 
COMMENT '仓库SKU售后率统计表，记录各仓库各SKU的售后率数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，yyyyMMdd格式，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='仓库SKU售后率统计表，用于分析各仓库商品的售后情况') 
LIFECYCLE 30;