CREATE TABLE IF NOT EXISTS app_pcs_saas_purchase_back_detail_report_df(
	back_date DATETIME COMMENT '采购退货日期，格式为年月日时分秒',
	back_no STRING COMMENT '退货批次号',
	back_type BIGINT COMMENT '退货类型：0-未入库退货，1-已入库退货',
	operator STRING COMMENT '退货操作发起人姓名',
	purchaser STRING COMMENT '采购人姓名',
	sku_id BIGINT COMMENT '商品SKU ID',
	spu_id BIGINT COMMENT '商品SPU ID',
	xianmu_sku STRING COMMENT '鲜沐平台SKU编码',
	xianmu_spu_id BIGINT COMMENT '鲜沐平台SPU ID',
	name STRING COMMENT '商品名称',
	specification STRING COMMENT '商品规格描述',
	unit STRING COMMENT '规格单位',
	back_warehouse_no BIGINT COMMENT '退货仓库编号',
	back_warehouse_name STRING COMMENT '退货仓库名称',
	production_date DATETIME COMMENT '商品生产日期，格式为年月日时分秒',
	quality_date DATETIME COMMENT '商品保质期截止日期，格式为年月日时分秒',
	outbound_status STRING COMMENT '出库状态：全部出库/部分出库/未出库等',
	back_quantity BIGINT COMMENT '退货数量',
	back_amount DECIMAL(38,18) COMMENT '退货金额，精确到18位小数',
	tenant_id BIGINT COMMENT '租户ID',
	category_id BIGINT COMMENT '三级类目ID',
	time_tag STRING COMMENT '时间标签，格式为yyyyMMdd',
	supplier STRING COMMENT '供应商名称',
	warehouse_service_provider STRING COMMENT '仓库服务商名称',
	price DECIMAL(38,18) COMMENT '采购单价，精确到18位小数'
) 
COMMENT 'SaaS采购退货明细表，记录采购退货的详细信息，包括退货商品、数量、金额、仓库、供应商等信息'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='SaaS采购退货明细事实表，用于采购退货业务分析和报表生成') 
LIFECYCLE 30;