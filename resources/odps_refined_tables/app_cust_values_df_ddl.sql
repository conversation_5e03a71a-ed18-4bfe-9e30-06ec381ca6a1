CREATE TABLE IF NOT EXISTS app_cust_values_df(
	`cust_id` BIGINT COMMENT '客户ID，唯一标识客户',
	`cust_name` STRING COMMENT '客户名称',
	`area_name` STRING COMMENT '运营区域名称',
	`cust_type` STRING COMMENT '客户类型；枚举值：面包蛋糕,西餐,咖啡,甜品冰淇淋,茶饮,其他',
	`zone_size` STRING COMMENT '销售区域名称',
	`first_delivery_date` DATETIME COMMENT '首次履约日期，年月日时分秒格式',
	`last_delivery_date` STRING COMMENT '最近履约日期，年月日时分秒格式的字符串',
	`total_origin_amt_30` DECIMAL(38,18) COMMENT '近30天履约应付金额',
	`total_origin_amt_60` DECIMAL(38,18) COMMENT '近31-60天履约应付金额',
	`total_origin_amt_90` DECIMAL(38,18) COMMENT '近61-90天履约应付金额',
	`avg_origin_amt` DECIMAL(38,18) COMMENT '近30天,31-60天,61-90天履约应付金额的平均值',
	`stddev_origin_amt` DECIMAL(38,18) COMMENT '近30天,31-60天,61-90天履约应付金额的标准差',
	`cust_life_cycle` STRING COMMENT '客户生命周期标签；枚举值：跳跃活跃,流失等',
	`cust_life_ltv` DECIMAL(38,18) COMMENT '月LTV（生命周期总价值）',
	`cust_life_ltv_level` STRING COMMENT '月LTV价值等级；枚举值：高,中,低',
	`cust_life_ltv_values` DECIMAL(38,18) COMMENT '存活LTV价值',
	`cust_life_ltv_values_level` STRING COMMENT '存活LTV价值等级；枚举值：高,中,低'
) 
COMMENT '生命周期标签表及价值表，包含客户的基本信息、履约数据和生命周期价值分析'
PARTITIONED BY (
	`ds` STRING NOT NULL COMMENT '分区字段，yyyyMMdd格式，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
	'comment' = '客户生命周期和价值分析表，用于客户分层和运营分析',
	'columnar.nested.type' = 'true'
)
LIFECYCLE 30;