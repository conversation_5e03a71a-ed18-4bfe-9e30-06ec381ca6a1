```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_dlv_area_city_cust_sku_timing_di` (
  `date` STRING COMMENT '日期，格式：yyyyMMdd',
  `large_area_id` BIGINT COMMENT '运营服务大区ID',
  `large_area_name` STRING COMMENT '运营服务大区名称',
  `city_id` BIGINT COMMENT '运营服务区ID',
  `city_name` STRING COMMENT '运营服务区名称',
  `cust_id` BIGINT COMMENT '客户ID',
  `cust_name` STRING COMMENT '客户名称',
  `cust_type` STRING COMMENT '客户类型，枚举值：面包蛋糕,西餐,咖啡,甜品冰淇淋,茶饮,其他',
  `cust_team` STRING COMMENT '客户团队类型，枚举值：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户、批发',
  `sku_id` STRING COMMENT 'SKU编号',
  `spu_id` BIGINT COMMENT 'SPU ID',
  `spu_name` STRING COMMENT '商品名称',
  `sku_disc` STRING COMMENT '商品描述',
  `timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送交易应付GMV，历史截止当日累计值',
  `timing_real_total_amt` DECIMAL(38,18) COMMENT '省心送交易实付GMV，历史截止当日累计值',
  `timing_order_cnt` BIGINT COMMENT '省心送交易订单数，历史截止当日累计值',
  `timing_dlv_origin_total_amt` DECIMAL(38,18) COMMENT '省心送履约应付GMV，历史截止当日累计值',
  `timing_dlv_real_total_amt` DECIMAL(38,18) COMMENT '省心送履约实付GMV，历史截止当日累计值',
  `timing_dlv_order_cnt` BIGINT COMMENT '省心送履约订单数，历史截止当日累计值',
  `timing_no_dlv_origin_total_amt` DECIMAL(38,18) COMMENT '省心送未履约应付GMV，历史截止当日累计值',
  `timing_no_dlv_real_total_amt` DECIMAL(38,18) COMMENT '省心送未履约实付GMV，历史截止当日累计值',
  `timing_no_dlv_sku_cnt` BIGINT COMMENT '省心送未履约商品数量，历史截止当日累计值',
  `timing_dlv_7_day_sku_cnt` BIGINT COMMENT '省心送未来7天预约配送商品数量',
  `timing_dlv_14_day_sku_cnt` BIGINT COMMENT '省心送未来14天预约配送商品数量',
  `timing_last_date` DATETIME COMMENT '省心送最后下单时间，格式：年月日时分秒',
  `last_date` DATETIME COMMENT '最后下单时间，格式：年月日时分秒'
) 
COMMENT '运营服务区+客户+库存仓SKU省心送交易明细表，包含省心送业务的交易、履约、未履约等关键指标数据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment'='运营服务区+客户+库存仓SKU省心送交易明细表',
  'lifecycle'='30'
);
```