CREATE TABLE IF NOT EXISTS app_saas_self_goods_cost_price_df(
	tenant_id BIGINT COMMENT '租户ID，取值范围：2-32',
	sku_id BIGINT COMMENT '鲜沐SKU主键ID，取值范围：5916-31975',
	sku_code STRING COMMENT '鲜沐SKU编码',
	province STRING COMMENT '省份名称',
	city STRING COMMENT '城市名称',
	area STRING COMMENT '区域名称',
	price DECIMAL(38,18) COMMENT '货品成本价格，单位：元',
	valid_time DATETIME COMMENT '价格生效日期，格式：年月日时分秒'
)
COMMENT 'SAAS自营货品成本价格表，记录各租户在不同地区的货品成本价格信息'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd')
STORED AS ALIORC
TBLPROPERTIES ('comment'='SAAS自营货品成本价格表，包含租户、SKU、地区维度的成本价格数据')
LIFECYCLE 30;