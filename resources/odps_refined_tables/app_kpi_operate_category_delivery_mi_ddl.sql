```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_kpi_operate_category_delivery_mi`(
  `month` STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
  `category` STRING COMMENT '商品品类，取值范围：鲜果、乳制品、其他',
  `origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV，原始应付总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '履约实付GMV，实际支付总金额',
  `marketing_amt` DECIMAL(38,18) COMMENT '营销费用，用于营销推广的费用',
  `cost_amt` DECIMAL(38,18) COMMENT '成本金额，商品采购和运营成本',
  `origin_gross` DECIMAL(38,18) COMMENT '应付毛利润，原始应付金额减去成本后的利润',
  `real_gross` DECIMAL(38,18) COMMENT '实付毛利润，实际支付金额减去成本后的利润',
  `origin_gross_margin` DECIMAL(38,18) COMMENT '应付毛利率，应付毛利润占应付GMV的比例',
  `real_gross_margin` DECIMAL(38,18) COMMENT '实付毛利率，实付毛利润占实付GMV的比例',
  `cust_cnt` BIGINT COMMENT '履约客户数，完成履约的客户数量',
  `point_cnt` BIGINT COMMENT '点位数，服务或销售网点的数量',
  `origin_pre_cust_price` DECIMAL(38,18) COMMENT '应付客单价，平均每个客户的应付金额',
  `real_pre_cust_price` DECIMAL(38,18) COMMENT '实付客单价，平均每个客户的实际支付金额',
  `timing_origin_amt` DECIMAL(38,18) COMMENT '省心送应付GMV，定时配送服务的原始应付金额',
  `timing_real_amt` DECIMAL(38,18) COMMENT '省心送实付GMV，定时配送服务的实际支付金额',
  `consign_origin_amt` DECIMAL(38,18) COMMENT '代售应付GMV，代售服务的原始应付金额',
  `consign_real_amt` DECIMAL(38,18) COMMENT '代售实付GMV，代售服务的实际支付金额',
  `consign_marketing_amt` DECIMAL(38,18) COMMENT '代售营销费用，代售服务的营销推广费用',
  `consign_origin_gross` DECIMAL(38,18) COMMENT '代售应付毛利润，代售服务的原始应付利润',
  `consign_real_gross` DECIMAL(38,18) COMMENT '代售实付毛利润，代售服务的实际支付利润',
  `consign_cust_cnt` BIGINT COMMENT '代售履约客户数，使用代售服务的客户数量',
  `turnover_day_cnt` DECIMAL(38,18) COMMENT '周转天数，商品从进货到售出的平均天数',
  `damage_amt` DECIMAL(38,18) COMMENT '货损金额，商品损坏或损耗的金额',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储费，商品存储相关的费用',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '单点干线费，主要运输路线的费用',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提费，客户自提商品产生的费用',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨费，商品调拨运输产生的费用',
  `other_amt` DECIMAL(38,18) COMMENT '其他费，其他未分类的费用',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送费，商品配送服务产生的费用'
) 
COMMENT '运营履约KPI表（平台客户），包含商品品类维度的运营履约关键绩效指标数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，如20250917表示2025年9月17日') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='运营履约KPI数据分析表，用于监控和分析平台客户的运营履约绩效指标') 
LIFECYCLE 30;
```