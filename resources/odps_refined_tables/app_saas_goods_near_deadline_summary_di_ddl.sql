```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_saas_goods_near_deadline_summary_di` (
  `time_tag` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计日期',
  `tenant_id` BIGINT COMMENT 'SKU租户ID，取值范围：2-109',
  `sku_id` BIGINT COMMENT 'SaaS SKU ID，取值范围：100590-121662',
  `warehouse_no` BIGINT COMMENT '库存仓ID，取值范围：2-173',
  `warehouse_name` STRING COMMENT '仓库名称，如：普冷武汉仓、嘉兴总仓、福州总仓等',
  `batch` STRING COMMENT '批次编号，用于标识商品批次',
  `expiration_date` DATETIME COMMENT '有效期，格式为yyyy-MM-dd HH:mm:ss，表示商品过期时间',
  `enter_deadline_date` DATETIME COMMENT '进入临期的日期，格式为yyyy-MM-dd HH:mm:ss，表示商品进入临期状态的时间',
  `enter_deadline_batch_stock` BIGINT COMMENT '进入临期批次库存，取值范围：1-20000，表示进入临期时的库存数量',
  `ending_batch_stock` BIGINT COMMENT '期末库存，取值范围：0-20000，表示统计周期结束时的库存数量',
  `item_id` BIGINT COMMENT '商品ID，取值范围：591-38813',
  `sale_price` DECIMAL(38,18) COMMENT '售价，商品销售价格'
)
COMMENT 'SaaS近15天临期货品汇总表，统计各租户临期商品的库存、批次、有效期等信息'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = 'SaaS近15天临期货品汇总表，用于监控和分析临期商品情况',
  'lifecycle' = '30'
);
```