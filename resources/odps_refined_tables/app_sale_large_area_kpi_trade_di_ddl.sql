```sql
CREATE TABLE IF NOT EXISTS app_sale_large_area_kpi_trade_di(
    `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的业务日期',
    `large_area_name` STRING COMMENT '运营服务大区名称，枚举值包括：成都大区、福州大区、重庆大区、可可快递服务区、南宁大区等',
    `order_origin_total_amt` DECIMAL(38,18) COMMENT '交易应付总金额，单位为元，包含所有订单的原始应付金额',
    `order_real_total_amt` DECIMAL(38,18) COMMENT '交易实付总金额，单位为元，包含所有订单的实际支付金额',
    `order_cust_cnt` BIGINT COMMENT '交易客户数，统计周期内下单的独立客户数量',
    `order_cust_arpu` DECIMAL(38,18) COMMENT 'ARPU(平均每用户收入)，计算公式：交易应付总金额/交易客户数，单位为元',
    `order_cnt` BIGINT COMMENT '交易订单数，统计周期内所有订单的数量',
    `lose_cust_cnt` BIGINT COMMENT '交易流失客户数，定义为90天内活跃用户近60天未下单客户数',
    `lose_cust_ratio` DECIMAL(38,18) COMMENT '交易流失率，计算公式：流失客户数/总客户数',
    `delivery_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额，单位为元，包含履约订单的原始应付金额',
    `delivery_real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额，单位为元，包含履约订单的实际支付金额',
    `delivery_cust_cnt` BIGINT COMMENT '履约活跃客户数，统计周期内参与履约的独立客户数量',
    `delivery_origin_profit` DECIMAL(38,18) COMMENT '履约应付毛利润，单位为元，基于应付金额计算的毛利润',
    `delivery_real_profit` DECIMAL(38,18) COMMENT '履约实付毛利润，单位为元，基于实付金额计算的毛利润',
    `delivery_after_profit` DECIMAL(38,18) COMMENT '履约后毛利润，单位为元，扣除相关费用后的净利润',
    `delivery_days_avg` DECIMAL(38,18) COMMENT '履约频次，统计周期内平均履约天数，通常为1表示日频',
    `delivery_point_cnt` BIGINT COMMENT '履约点位数，统计周期内参与履约的网点或点位数量',
    `delivery_amt` DECIMAL(38,18) COMMENT '自营履约费用，单位为元，自营业务产生的履约相关费用',
    `new_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '新客履约应付总金额，单位为元，新客户履约订单的原始应付金额',
    `new_delivery_real_total_amt` DECIMAL(38,18) COMMENT '新客履约实付总金额，单位为元，新客户履约订单的实际支付金额',
    `new_delivery_cust_cnt` BIGINT COMMENT '新客履约活跃客户数，统计周期内新客户参与履约的独立客户数量',
    `new_delivery_real_profit` DECIMAL(38,18) COMMENT '新客履约实付毛利润，单位为元，新客户基于实付金额计算的毛利润',
    `old_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '老客履约应付总金额，单位为元，老客户履约订单的原始应付金额',
    `old_delivery_real_total_amt` DECIMAL(38,18) COMMENT '老客履约实付总金额，单位为元，老客户履约订单的实际支付金额',
    `old_delivery_cust_cnt` BIGINT COMMENT '老客履约活跃客户数，统计周期内老客户参与履约的独立客户数量',
    `old_delivery_real_profit` DECIMAL(38,18) COMMENT '老客履约实付毛利润，单位为元，老客户基于实付金额计算的毛利润',
    `order_sku_cnt` BIGINT COMMENT '交易SKU款数，统计周期内交易订单涉及的SKU种类数量',
    `order_sku_weight` DECIMAL(38,18) COMMENT '交易SKU重量，单位为千克(KG)，所有交易SKU的总重量',
    `delivery_sku_cnt` BIGINT COMMENT '履约SKU款数，统计周期内履约订单涉及的SKU种类数量',
    `delivery_sku_weight` DECIMAL(38,18) COMMENT '履约SKU重量，单位为千克(KG)，所有履约SKU的总重量'
)
COMMENT '销售KPI指标汇总表，按大区维度统计交易和履约相关的关键绩效指标，包含交易金额、客户数、订单数、流失率、履约费用、SKU数量等核心业务指标'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据加载的日期分区')
STORED AS ALIORC
TBLPROPERTIES (
    'comment'='销售大区KPI交易指标日粒度汇总表',
    'columnar.nested.type'='true'
)
LIFECYCLE 30;
```