CREATE TABLE IF NOT EXISTS app_pcs_direct_purchase_kpi_mi(
	`month` STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
	`direct_purchase_amt` DECIMAL(38,18) COMMENT '直采金额，单位：元',
	`purchases_amt` DECIMAL(38,18) COMMENT '采购金额（非直采），单位：元',
	`cost_flow_amt` DECIMAL(38,18) COMMENT '成本浮动金额，可为负值，表示成本减少',
	`direct_delivery_origin_amt` DECIMAL(38,18) COMMENT '直采履约应付金额，单位：元',
	`direct_delivery_real_amt` DECIMAL(38,18) COMMENT '直采履约实付金额，单位：元',
	`direct_delivery_market_amt` DECIMAL(38,18) COMMENT '直采营销费用，单位：元',
	`direct_delivery_cost_amt` DECIMAL(38,18) COMMENT '直采成本费用，单位：元',
	`direct_init_amt` DECIMAL(38,18) COMMENT '直采期末库存金额，单位：元',
	`direct_after_sale_pcs_amt` DECIMAL(38,18) COMMENT '直采采购责售后金额，单位：元',
	`direct_damage_pcs_amt` DECIMAL(38,18) COMMENT '直采采购责货损金额，单位：元'
) 
PARTITIONED BY (`ds` STRING COMMENT '分区字段，yyyyMMdd格式，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='直采KPI指标表，包含直采相关的财务和运营指标数据') 
LIFECYCLE 30;