```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_saas_trade_wi` (
  `week_of_year` BIGINT COMMENT '周数，一年中的第几周，取值范围：1-53',
  `monday` STRING COMMENT '周一日期，格式：yyyyMMdd，表示周一的年月日',
  `sunday` STRING COMMENT '周日日期，格式：yyyyMMdd，表示周日的年月日',
  `store_cnt` BIGINT COMMENT '门店总注册数',
  `direct_store_cnt` BIGINT COMMENT '直营门店注册数',
  `join_store_cnt` BIGINT COMMENT '加盟门店注册数',
  `managed_store_cnt` BIGINT COMMENT '托管门店注册数',
  `store_order_cnt` BIGINT COMMENT '门店总交易数',
  `direct_store_order_cnt` BIGINT COMMENT '直营门店交易数',
  `join_store_order_cnt` BIGINT COMMENT '加盟门店交易数',
  `managed_store_order_cnt` BIGINT COMMENT '托管门店交易数',
  `total_gmv` DECIMAL(38,18) COMMENT '总交易GMV（Gross Merchandise Volume，商品交易总额）',
  `direct_store_gmv` DECIMAL(38,18) COMMENT '直营门店GMV',
  `join_store_gmv` DECIMAL(38,18) COMMENT '加盟门店GMV',
  `managed_store_gmv` DECIMAL(38,18) COMMENT '托管门店GMV',
  `dc_sku_gmv` DECIMAL(38,18) COMMENT '代仓商品交易GMV',
  `dc_sku_gmv_rate` DECIMAL(38,18) COMMENT '代仓商品交易GMV占比，取值范围：0-1',
  `khzy_sku_gmv` DECIMAL(38,18) COMMENT '客户自营商品交易GMV',
  `khzy_sku_gmv_rate` DECIMAL(38,18) COMMENT '客户自营商品交易GMV占比，取值范围：0-1',
  `xianmu_sku_gmv` DECIMAL(38,18) COMMENT '鲜沐商品交易GMV',
  `xianmu_sku_gmv_rate` DECIMAL(38,18) COMMENT '鲜沐商品交易GMV占比，取值范围：0-1',
  `bill_gmv` DECIMAL(38,18) COMMENT '账期GMV',
  `bill_gmv_rate` DECIMAL(38,18) COMMENT '账期GMV占比，取值范围：0-1',
  `cash_gmv` DECIMAL(38,18) COMMENT '现结GMV',
  `cash_gmv_rate` DECIMAL(38,18) COMMENT '现结GMV占比，取值范围：0-1',
  `xianmu_supply_sku_gmv` DECIMAL(38,18) COMMENT '鲜沐商品供应GMV',
  `xianmu_sku_add_gmv` DECIMAL(38,18) COMMENT '鲜沐商品品牌加价后GMV',
  `xianmu_sku_income_gmv` DECIMAL(38,18) COMMENT '鲜沐商品品牌加价收入',
  `xianmu_sku_income_gmv_rate` DECIMAL(38,18) COMMENT '鲜沐商品品牌加价收入占比，取值范围：0-1',
  `dc_after_sale_nonarrival_sku_cnt` BIGINT COMMENT '代仓商品售后数量(未到货)',
  `dc_after_sale_nonarrival_amt` DECIMAL(38,18) COMMENT '代仓商品售后金额(未到货)',
  `dc_after_sale_arrival_sku_cnt` BIGINT COMMENT '代仓商品售后数量(已到货)',
  `dc_after_sale_arrival_amt` DECIMAL(38,18) COMMENT '代仓商品售后金额(已到货)',
  `khzy_after_sale_nonarrival_sku_cnt` BIGINT COMMENT '客户自营商品售后数量(未到货)',
  `khzy_after_sale_nonarrival_amt` DECIMAL(38,18) COMMENT '客户自营商品售后金额(未到货)',
  `khzy_after_sale_arrival_sku_cnt` BIGINT COMMENT '客户自营商品售后数量(已到货)',
  `khzy_after_sale_arrival_amt` DECIMAL(38,18) COMMENT '客户自营商品售后金额(已到货)',
  `xianmu_after_sale_nonarrival_sku_cnt` BIGINT COMMENT '鲜沐商品售后数量(未到货)',
  `xianmu_after_sale_nonarrival_amt` DECIMAL(38,18) COMMENT '鲜沐商品售后金额(未到货)',
  `xianmu_after_sale_arrival_sku_cnt` BIGINT COMMENT '鲜沐商品售后数量(已到货)',
  `xianmu_after_sale_arrival_amt` DECIMAL(38,18) COMMENT '鲜沐商品售后金额(已到货)',
  `fruit_gmv` DECIMAL(38,18) COMMENT '鲜果交易GMV',
  `not_fruit_gmv` DECIMAL(38,18) COMMENT '标品交易GMV',
  `khzy_gmv` DECIMAL(38,18) COMMENT '客户自营GMV(剔除供应商直发)',
  `supplier_direct_gmv` DECIMAL(38,18) COMMENT '供应商直发GMV'
) 
COMMENT 'SaaS总体监控周汇总表，包含门店注册、交易、GMV、售后等关键业务指标的周度汇总数据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据所属日期的年月日'
)
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
  'comment'='SaaS业务周度监控汇总表，用于跟踪和分析SaaS平台的整体业务表现') 
LIFECYCLE 30;
```