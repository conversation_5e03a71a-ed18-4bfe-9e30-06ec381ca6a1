CREATE TABLE IF NOT EXISTS app_damage_sale_ratio_report_df(
	`damage_date` DATETIME COMMENT '货损日期，年月日时分秒格式',
	`sku_id` BIGINT COMMENT 'SKU ID，商品最小库存单位标识',
	`spu_id` BIGINT COMMENT 'SPU ID，商品标准产品单位标识',
	`xianmu_sku_id` STRING COMMENT '鲜沐SKU ID，鲜沐平台商品编码',
	`xianmu_spu_id` BIGINT COMMENT '鲜沐SPU ID，鲜沐平台商品标准编码',
	`name` STRING COMMENT '商品名称',
	`specification` STRING COMMENT '商品规格描述',
	`unit` STRING COMMENT '规格单位，如：袋、箱、包等',
	`warehouse_no` BIGINT COMMENT '仓库编号，唯一标识仓库',
	`warehouse_name` STRING COMMENT '仓库名称',
	`purchaser` STRING COMMENT '采购人姓名',
	`tenant_id` BIGINT COMMENT '租户ID，标识不同业务租户',
	`category_id` BIGINT COMMENT '一级类目ID，商品分类标识',
	`damage_quantity` BIGINT COMMENT '货损数量，商品损坏的数量',
	`damage_amount` DECIMAL(38,18) COMMENT '货损金额，商品损坏的金额',
	`outbound_quantity` BIGINT COMMENT '销售出库数量，已销售商品数量',
	`outbound_amount` DECIMAL(38,18) COMMENT '销售出库金额，已销售商品金额',
	`back_quantity` BIGINT COMMENT '退货数量，客户退回的商品数量',
	`back_amount` DECIMAL(38,18) COMMENT '退货金额，客户退回的商品金额',
	`damage_sale_ratio` DECIMAL(38,18) COMMENT '货损比，货损金额与销售金额的比率',
	`time_tag` STRING COMMENT '时间标签，yyyyMMdd格式的日期字符串',
	`warehouse_service_provider` STRING COMMENT '仓库服务商名称'
) 
COMMENT '损售比报表，统计商品货损与销售的比例关系，用于分析商品损耗情况'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，yyyyMMdd格式的日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='损售比分析报表，包含商品货损、销售、退货等关键指标数据') 
LIFECYCLE 30;