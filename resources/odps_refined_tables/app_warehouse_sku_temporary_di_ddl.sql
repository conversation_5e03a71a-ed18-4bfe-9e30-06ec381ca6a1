```sql
CREATE TABLE IF NOT EXISTS app_warehouse_sku_temporary_di(
    warehouse_no BIGINT COMMENT '库存仓编号',
    warehouse_name STRING COMMENT '库存仓名称',
    sku_id STRING COMMENT 'SKU编号，商品最小库存单位唯一标识',
    spu_name STRING COMMENT 'SPU名称，标准产品单位名称',
    category1 STRING COMMENT '一级类目名称',
    category4_id STRING COMMENT '四级类目ID',
    category4 STRING COMMENT '四级类目名称',
    sku_brand STRING COMMENT 'SKU品牌名称',
    sku_property STRING COMMENT 'SKU性质：常规、临保、拆包、破袋',
    disc STRING COMMENT 'SKU描述，包含规格包装信息',
    quality_date STRING COMMENT '保质期日期，格式：yyyyMMdd，表示年月日',
    warn_days BIGINT COMMENT '到期预警天数，取值范围：0-365天',
    unit_amt DECIMAL(38,18) COMMENT '成本单价，精确到18位小数',
    store_quantity BIGINT COMMENT '在仓库存数量，取值范围：1-500560',
    sale_out_quality BIGINT COMMENT '历史两周销售出库数量，取值范围：0-2320',
    allocate_out_quality BIGINT COMMENT '历史两周调拨出库数量，取值范围：0-954',
    avg_quality BIGINT COMMENT '过去2周日均出库量，取值范围：0-166',
    store_use_days BIGINT COMMENT '库存可用天数，-1表示无限期或无法计算，取值范围：-1-11382',
    temporary_date STRING COMMENT '临保日期，格式：yyyyMMdd，表示年月日',
    estimated_date STRING COMMENT '预计售罄日期，格式：yyyyMMdd，表示年月日，"-"表示无法预估',
    damage_rask STRING COMMENT '货损风险：是、否'
)
COMMENT '库存仓、SKU维度临保数据表，记录各仓库SKU的临保状态、库存情况、销售预测和货损风险评估'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd，表示年月日')
STORED AS ALIORC
TBLPROPERTIES ('comment'='库存仓SKU临保数据表，用于监控和管理临期商品库存')
LIFECYCLE 30;
```