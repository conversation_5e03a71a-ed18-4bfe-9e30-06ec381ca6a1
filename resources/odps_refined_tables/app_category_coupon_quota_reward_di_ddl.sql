CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_category_coupon_quota_reward_di` (
  `day_tag` STRING COMMENT '数据更新标记，格式：yyyyMMdd，表示数据所属日期',
  `admin_id` BIGINT COMMENT 'M1管理者ID，-1表示未知或系统默认值',
  `admin_name` STRING COMMENT 'M1管理者姓名',
  `bd_id` BIGINT COMMENT '申请人ID，-1表示未知或系统默认值',
  `bd_name` STRING COMMENT '申请人姓名',
  `amount` DECIMAL(38,18) COMMENT '奖励金额，单位：元'
)
COMMENT '类目优惠券配额奖励明细表，记录BD申请优惠券配额的奖励金额明细'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据采集日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '类目优惠券配额奖励明细表，记录BD申请优惠券配额的奖励金额明细',
  'lifecycle' = '30'
);