CREATE TABLE IF NOT EXISTS app_kpi_category_trade_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示年月日',
	`category` STRING COMMENT '商品品类，取值范围：鲜果、乳制品、其他',
	`origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额，单位为元',
	`real_total_amt` DECIMAL(38,18) COMMENT '实付总金额，单位为元',
	`cust_cnt` BIGINT COMMENT '客户数，统计当日下单的独立客户数量',
	`cust_arpu` DECIMAL(38,18) COMMENT 'ARPU值（Average Revenue Per User），计算公式：应付总金额/客户数，单位为元/客户',
	`order_cnt` BIGINT COMMENT '订单数，统计当日产生的订单总数',
	`order_avg` DECIMAL(38,18) COMMENT '订单均价，计算公式：应付总金额/订单数，单位为元/订单',
	`after_sale_noreceived_amt` DECIMAL(38,18) COMMENT '未到货售后总金额，指商品未送达情况下的售后金额，单位为元',
	`after_sale_rate` DECIMAL(38,18) COMMENT '退货率，计算公式：未到货售后总金额/应付总金额，为百分比小数形式',
	`dire_origin_total_amt` DECIMAL(38,18) COMMENT '直发采购应付总金额，指直发模式的采购金额，单位为元',
	`delivery_amt` DECIMAL(38,18) COMMENT '运费总金额，包含运费和超时加单费，单位为元',
	`timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送应付总金额，指省心送服务的应付金额，单位为元'
) 
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='交易口径KPI指标日汇总表，按品类维度统计每日交易相关核心指标，包括销售额、客户数、订单数、ARPU、退货率等关键业务指标') 
LIFECYCLE 30;