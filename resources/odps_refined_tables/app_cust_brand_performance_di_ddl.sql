CREATE TABLE IF NOT EXISTS app_cust_brand_performance_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务日期',
	`order_origin_amt` DECIMAL(38,18) COMMENT '交易应付总金额，订单原始金额，单位为元',
	`order_real_amt` DECIMAL(38,18) COMMENT '交易实付总金额，订单实际支付金额，单位为元',
	`delivery_origin_amt` DECIMAL(38,18) COMMENT '履约应付总金额，履约原始金额，单位为元',
	`delivery_real_amt` DECIMAL(38,18) COMMENT '履约实付总金额，履约实际支付金额，单位为元',
	`delivery_cash_real_amt` DECIMAL(38,18) COMMENT '履约现结实付总金额，现金支付部分，单位为元',
	`delivery_bill_real_amt` DECIMAL(38,18) COMMENT '履约账期实付总金额，账期支付部分，单位为元',
	`delivery_cost_amt` DECIMAL(38,18) COMMENT '履约商品成本金额，商品成本，单位为元',
	`delivery_real_gross` DECIMAL(38,18) COMMENT '履约实付毛利润，实付金额减去成本，单位为元',
	`delivery_real_gross_rate` DECIMAL(38,18) COMMENT '履约实付毛利率，毛利润/实付金额，取值范围0-1',
	`delivery_cust_cnt` BIGINT COMMENT '履约客户数，完成履约的客户数量',
	`delivery_point_cnt` BIGINT COMMENT '履约累计点位数，完成履约的点位总数',
	`after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后总金额，已收货的售后金额，单位为元',
	`after_sale_rate` DECIMAL(38,18) COMMENT '售后比例，已到货售后金额/履约实付GMV，取值范围0-1'
) 
COMMENT '大客户整体监控表，用于监控大客户的交易、履约和售后相关指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='大客户整体监控表，包含交易金额、履约数据、售后情况等核心业务指标') 
LIFECYCLE 30;