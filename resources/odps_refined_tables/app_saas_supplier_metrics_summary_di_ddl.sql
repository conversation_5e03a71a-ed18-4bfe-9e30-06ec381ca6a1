CREATE TABLE IF NOT EXISTS app_saas_supplier_metrics_summary_di(
	time_tag STRING COMMENT '日期标签，格式为yyyyMMdd，表示数据统计的日期',
	tenant_id BIGINT COMMENT '租户ID，标识SaaS平台中的不同客户租户',
	supplier_no BIGINT COMMENT '供应商编号，唯一标识每个供应商',
	supplier_name STRING COMMENT '供应商名称',
	supplier_type STRING COMMENT '供应商类型，取值范围：企业（生产商）、企业（经销商）等',
	purchase_to_warehouse_on_time_rate DECIMAL(38,18) COMMENT '近30天采购到仓准时率，百分比数值，范围0-100',
	fully_stocked_purchase_tasks_num_30d BIGINT COMMENT '近30天完全入库的采购入库任务数量',
	fully_stocked_on_time_purchase_tasks_num_30d BIGINT COMMENT '近30天完全入库且准时入库的采购入库任务数量',
	to_warehouse_accuracy DECIMAL(38,18) COMMENT '近30天采购到仓准确率，百分比数值，范围0-100',
	purchase_tasks_num_30d BIGINT COMMENT '近30天采购入库单总数',
	received_equal_incoming_tasks_num BIGINT COMMENT '近30天实收数量等于应入数量的入库单数量'
) 
COMMENT 'SaaS供应商指标汇总表，包含供应商的基本信息和近30天的采购绩效指标'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据加载日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='SaaS平台供应商绩效指标汇总表，用于分析供应商的采购准时率、准确率等关键绩效指标') 
LIFECYCLE 30;