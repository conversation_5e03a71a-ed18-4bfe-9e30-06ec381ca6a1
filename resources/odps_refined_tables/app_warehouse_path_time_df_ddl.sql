CREATE TABLE IF NOT EXISTS app_warehouse_path_time_df(
	`in_warehouse_no` BIGINT COMMENT '转入仓编号，取值范围：2-177',
	`out_warehouse_no` BIGINT COMMENT '转出仓编号，取值范围：2-147', 
	`cost_time` BIGINT COMMENT '用时(天)，取值范围：2-4天，平均用时约3.88天'
)
COMMENT '开放仓到仓之间路途时间表，记录不同仓库之间的运输时间成本'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='开放仓到仓之间路途时间分析表，用于仓储物流路径优化') 
LIFECYCLE 30;