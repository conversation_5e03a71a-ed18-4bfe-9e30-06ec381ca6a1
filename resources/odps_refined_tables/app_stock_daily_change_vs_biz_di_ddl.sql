CREATE TABLE IF NOT EXISTS app_stock_daily_change_vs_biz_di(
	pt STRING COMMENT '数据时间点，yyyyMMdd格式',
	change_date DATETIME COMMENT '变更日期，年月日时分秒格式',
	warehouse_no BIGINT COMMENT '仓库编码，数值型标识',
	warehouse_name STRING COMMENT '仓库名称',
	sku STRING COMMENT 'SKU编码，商品唯一标识',
	pd_name STRING COMMENT '商品名称',
	specifications STRING COMMENT '商品规格描述',
	change_type STRING COMMENT '变更类型：销售出库、采购入库、调拨入库、调拨出库、盘点调整等',
	stock_change_quantity BIGINT COMMENT '库存流水变更数量，正数表示入库，负数表示出库',
	biz_change_quantity BIGINT COMMENT '业务单据变更数量，正数表示入库，负数表示出库'
)
COMMENT '库存每日变更对比业务单表，用于对比库存流水变更与业务单据变更的数量差异'
PARTITIONED BY (ds STRING COMMENT '分区字段，yyyyMMdd格式')
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='库存变更业务对比分析表，监控库存流水与业务单据的一致性')
LIFECYCLE 30;