CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_dlv_deliver_timing_rate_month_df` (
  `month` STRING COMMENT '月份，格式为yyyyMM，如201702表示2017年2月',
  `timing_deliver_30_cust_cnt` BIGINT COMMENT '30天内省心送完成的客户数',
  `timing_deliver_30_60_cust_cnt` BIGINT COMMENT '30-60天内省心送完成的客户数',
  `timing_deliver_60_90_cust_cnt` BIGINT COMMENT '60-90天内省心送完成的客户数',
  `timing_deliver_90_cust_cnt` BIGINT COMMENT '90天以上省心送完成的客户数',
  `timing_deliver_30_origin_amt` DECIMAL(38,18) COMMENT '30天内省心送完成应付总金额',
  `timing_deliver_30_60_origin_amt` DECIMAL(38,18) COMMENT '30-60天内省心送完成应付总金额',
  `timing_deliver_60_90_origin_amt` DECIMAL(38,18) COMMENT '60-90天内省心送完成应付总金额',
  `timing_deliver_90_origin_amt` DECIMAL(38,18) COMMENT '90天以上省心送完成应付总金额',
  `timing_deliver_30_real_amt` DECIMAL(38,18) COMMENT '30天内省心送完成实付总金额',
  `timing_deliver_30_60_real_amt` DECIMAL(38,18) COMMENT '30-60天内省心送完成实付总金额',
  `timing_deliver_60_90_real_amt` DECIMAL(38,18) COMMENT '60-90天内省心送完成实付总金额',
  `timing_deliver_90_real_amt` DECIMAL(38,18) COMMENT '90天以上省心送完成实付总金额',
  `timing_order_cust_cnt` BIGINT COMMENT '当日省心送客户数',
  `timing_order_origin_amt` DECIMAL(38,18) COMMENT '当日省心送应付总金额',
  `timing_order_real_amt` DECIMAL(38,18) COMMENT '当日省心送实付总金额'
)
COMMENT '完成配送省心送明细数据，按月统计省心送业务的客户数、应付金额和实付金额等指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '省心送业务完成配送统计月表',
  'lifecycle' = '30'
)