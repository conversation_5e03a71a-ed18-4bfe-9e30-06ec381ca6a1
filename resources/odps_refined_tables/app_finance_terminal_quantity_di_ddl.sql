```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_finance_terminal_quantity_di`(
  `service_area` STRING COMMENT '大区，如：华东、华南等',
  `warehouse_no` BIGINT COMMENT '库存仓ID，取值范围：2-59',
  `warehouse_name` STRING COMMENT '库存仓名称，如：上海总仓',
  `sku` STRING COMMENT '商品SKU编码，唯一标识商品',
  `pd_name` STRING COMMENT '商品名称',
  `quantity` BIGINT COMMENT '期末库存数量，取值范围：0-57816',
  `cost_amt` DECIMAL(38,18) COMMENT '期末库存总金额（含税）',
  `date_flag` STRING COMMENT '业务日期标识，格式：yyyyMMdd',
  `category1` STRING COMMENT '商品一级类目，如：乳制品、其他等',
  `cost_amt_notax` DECIMAL(38,18) COMMENT '期末库存总金额（不含税）',
  `sub_type` BIGINT COMMENT '商品二级性质：1-自营代销不入仓、2-自营代销入仓、3-自营经销、4-代仓代仓',
  `settle_type` STRING COMMENT '结算类型'
)
COMMENT '财务口径期末库存表，记录各仓库商品的期末库存数量和金额信息'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式：yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '财务口径期末库存表，用于财务核算和库存分析',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```