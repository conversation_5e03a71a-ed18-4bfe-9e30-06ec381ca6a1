CREATE TABLE IF NOT EXISTS app_self_category_delivery_warehouse_kpi_wi(
	`year` STRING COMMENT '年份，格式：yyyy',
	`week_of_year` BIGINT COMMENT '周数，取值范围：1-53',
	`monday` STRING COMMENT '周一日期，格式：yyyyMMdd',
	`sunday` STRING COMMENT '周日日期，格式：yyyyMMdd',
	`category` STRING COMMENT '商品品类，枚举值：鲜果、乳制品、其他',
	`origin_total_amt` DECIMAL(38,18) COMMENT '原始总金额（元）',
	`real_total_amt` DECIMAL(38,18) COMMENT '实际总金额（元）',
	`cost_amt` DECIMAL(38,18) COMMENT '成本金额（元）',
	`timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送原始总金额（元）',
	`timing_real_total_amt` DECIMAL(38,18) COMMENT '省心送实际总金额（元）',
	`cust_cnt` BIGINT COMMENT '客户数量',
	`order_cnt` BIGINT COMMENT '订单数量',
	`point_cnt` BIGINT COMMENT '点位数量',
	`day_point_cnt` BIGINT COMMENT '日均点位数量',
	`sku_cnt` BIGINT COMMENT 'SKU数量',
	`delivery_amt` DECIMAL(38,18) COMMENT '运费金额（元）',
	`after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后总金额（元）',
	`inventory_loss_amt` DECIMAL(38,18) COMMENT '盘亏总金额（元）',
	`inventory_profit_amt` DECIMAL(38,18) COMMENT '盘盈总金额（元）',
	`damage_amt` DECIMAL(38,18) COMMENT '货损总金额（元）',
	`damage_rate` DECIMAL(38,18) COMMENT '货损占比',
	`storage_amt` DECIMAL(38,18) COMMENT '仓储成本（元）',
	`arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本（元）',
	`deliver_amt` DECIMAL(38,18) COMMENT '配送成本（元）',
	`self_picked_amt` DECIMAL(38,18) COMMENT '采购自提成本（元）',
	`other_amt` DECIMAL(38,18) COMMENT '其他成本（元）',
	`allocation_amt` DECIMAL(38,18) COMMENT '调拨成本（元）'
) 
COMMENT '履约口径KPI指标日汇总表（自营），按周维度统计各品类的履约相关财务和运营指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='自营商品履约KPI周度汇总表，包含财务指标、运营指标和成本分析') 
LIFECYCLE 30;