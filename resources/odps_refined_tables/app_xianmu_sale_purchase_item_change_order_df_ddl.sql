```sql
CREATE TABLE IF NOT EXISTS app_xianmu_sale_purchase_item_change_order_df(
    purchase_no STRING COMMENT '采购批次号，唯一标识一个采购批次',
    sku STRING COMMENT '商品SKU编码，唯一标识一个商品',
    pd_name STRING COMMENT '商品名称',
    weight STRING COMMENT '商品规格/重量，示例数据中显示为"None"表示无规格信息',
    supplier STRING COMMENT '供货商名称',
    quantity BIGINT COMMENT '采购数量，整数类型',
    actual_quantity BIGINT COMMENT '实收数量，整数类型',
    price_type STRING COMMENT '价格形式，枚举类型：指定价',
    cost DECIMAL(38,18) COMMENT '单个成本，精确到小数点后18位',
    total_cost DECIMAL(38,18) COMMENT '总成本，精确到小数点后18位'
)
COMMENT '销转采采购单明细表，记录销售转采购的采购单商品明细信息，包括采购批次、商品信息、数量、成本和供应商等数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期')
STORED AS ALIORC
TBLPROPERTIES ('comment'='销转采采购单明细表，用于存储销售转采购流程中的采购商品明细数据')
LIFECYCLE 30;
```