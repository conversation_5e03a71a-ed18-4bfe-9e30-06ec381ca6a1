CREATE TABLE IF NOT EXISTS app_inventory_consignment_in_warehouse_di(
	`supplier_id` BIGINT COMMENT '供应商ID',
	`supplier_name` STRING COMMENT '供应商名称',
	`warehouse_no` BIGINT COMMENT '仓库编号',
	`warehouse_name` STRING COMMENT '仓库名称',
	`batch_no` STRING COMMENT '批次号',
	`sku` STRING COMMENT 'SKU编码，商品唯一标识',
	`sku_desc` STRING COMMENT '商品描述，包含规格信息',
	`spu_name` STRING COMMENT 'SPU名称，商品品类名称',
	`stock_quantity` BIGINT COMMENT '库存数量',
	`stock_amount` DECIMAL(38,18) COMMENT '库存金额',
	`stock_type` BIGINT COMMENT '库存类型：0-正常库存，1-异常库存',
	`production_date` DATETIME COMMENT '生产日期，年月日时分秒格式',
	`quality_date` DATETIME COMMENT '保质期截止日期，年月日时分秒格式',
	`date_flag` STRING COMMENT '日期标识，yyyyMMdd格式的业务日期',
	`custom_sku` STRING COMMENT '供应商自有SKU编码',
	`sub_type` BIGINT COMMENT '库存子类型：1-自营-代销不入仓，2-自营-代销入仓，3-自营-经销，4-代仓-代仓'
)
COMMENT '代销入仓库存表，记录代销模式下入仓商品的库存信息，包括库存数量、金额、生产日期、保质期等详细信息'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，yyyyMMdd格式的日期分区')
STORED AS ALIORC
TBLPROPERTIES ('comment'='代销入仓库存明细表，用于库存管理和分析')
LIFECYCLE 30;