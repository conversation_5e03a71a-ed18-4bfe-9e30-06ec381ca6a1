```sql
CREATE TABLE IF NOT EXISTS app_saas_brand_city_delivery_mi(
    month STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
    brand_alias STRING COMMENT '品牌名称，如：GIGI LUCKY舒芙蕾',
    province STRING COMMENT '省份名称，如：广东省',
    city STRING COMMENT '城市名称，如：东莞市、中山市、佛山市',
    area STRING COMMENT '区域/街道名称，如：万江街道、南城街道、东区街道、石岐街道、顺德区',
    point_cnt BIGINT COMMENT '点位数，取值范围：1-224，均值10.57，标准差17.28'
) 
COMMENT 'SaaS履约网络可视化表，展示各品牌在不同城市区域的点位分布情况'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期')
STORED AS ALIORC  
TBLPROPERTIES ('comment'='SaaS履约网络可视化分析表，用于监控品牌在各地区的服务覆盖情况') 
LIFECYCLE 30;
```