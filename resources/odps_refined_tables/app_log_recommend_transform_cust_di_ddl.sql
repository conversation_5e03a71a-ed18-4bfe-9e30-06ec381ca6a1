CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_log_recommend_transform_cust_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
  `cust_id` BIGINT COMMENT '客户ID，唯一标识一个客户',
  `scene` STRING COMMENT '推荐场景：首页、商品详情页、购物车页、分类页',
  `abexperiments_experiment_id` STRING COMMENT 'AB实验ID，标识具体的实验方案，取值范围：具体实验ID或"无"',
  `abexperiments_experiment_place` STRING COMMENT 'AB实验位置，标识实验在页面的具体位置，取值范围：具体位置或"无"',
  `abexperiments_variant_id` STRING COMMENT 'AB实验变体ID，标识实验的具体变体版本，取值范围：具体变体ID或"无"',
  `is_new` STRING COMMENT '是否当日注册新用户，取值范围：是/否/None',
  `sku_impression_uv` BIGINT COMMENT '商品曝光独立用户数，统计去重后的用户数量',
  `sku_impression_pv` BIGINT COMMENT '商品曝光页面浏览量，统计总的曝光次数',
  `sku_click_uv` BIGINT COMMENT '商品点击独立用户数，统计去重后的点击用户',
  `sku_click_pv` BIGINT COMMENT '商品点击页面浏览量，统计总的点击次数',
  `sku_cart_uv` BIGINT COMMENT '商品加购独立用户数，统计去重后的加购用户',
  `sku_cart_pv` BIGINT COMMENT '商品加购页面浏览量，统计总的加购次数',
  `order_cnt` BIGINT COMMENT '下单订单数量，统计用户下单的订单数',
  `order_amt` DECIMAL(38,18) COMMENT '下单金额，用户下单的总金额，精度为38位，小数位18位',
  `order_paid_cnt` BIGINT COMMENT '支付订单数量，统计用户实际支付的订单数',
  `order_paid_amt` DECIMAL(38,18) COMMENT '支付金额，用户实际支付的总金额，精度为38位，小数位18位',
  `order_paid_amt_avg` DECIMAL(38,18) COMMENT '平均支付金额，支付金额除以支付订单数的平均值，精度为38位，小数位18位'
)
COMMENT '商城推荐转化汇总表，统计各推荐场景下的用户行为转化数据，包括曝光、点击、加购、下单、支付等关键指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，用于按天分区管理数据'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment'='商城推荐系统转化效果分析表，用于监控和优化推荐算法的转化效果',
  'lifecycle'='30'
);