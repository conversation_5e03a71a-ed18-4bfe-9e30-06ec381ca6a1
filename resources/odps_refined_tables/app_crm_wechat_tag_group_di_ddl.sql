CREATE TABLE IF NOT EXISTS app_crm_wechat_tag_group_di(
	group_name STRING COMMENT '标签组名称',
	merchant_label STRING COMMENT '客户标签',
	type BIGINT COMMENT '变动类型: 0-新增标签;1-删除标签;2-不变标签',
	day_tag STRING COMMENT '数据日期，格式为yyyyMMdd',
	rank BIGINT COMMENT '组内排序，取值范围1-39'
) 
COMMENT 'CRM企业微信标签组表，记录企业微信客户标签的分组信息'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='CRM企业微信标签组表，包含标签组名称、客户标签、变动类型等信息') 
LIFECYCLE 30;