CREATE TABLE IF NOT EXISTS app_log_purchase_function_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
	`type` STRING COMMENT '实验分组：V3-实验组V3、V4-实验组V4、对照组-对照组',
	`cust_cnt` BIGINT COMMENT '客户数，访问采购助手页面的独立客户数量',
	`view_uv` BIGINT COMMENT '榜单曝光uv，看到商品榜单的独立用户数',
	`click_uv` BIGINT COMMENT '榜单商品点击uv，点击榜单商品的独立用户数',
	`click_buy_uv` BIGINT COMMENT '榜单唤起购买uv，通过榜单商品唤起购买行为的独立用户数',
	`frequently_shop_uv` BIGINT COMMENT '常购商品曝光uv，看到常购商品推荐的独立用户数',
	`click_jump_uv` BIGINT COMMENT '商品点击跳转uv，点击商品跳转到详情页的独立用户数',
	`buy_uv` BIGINT COMMENT '常购商品唤起购买uv，通过常购商品唤起购买行为的独立用户数',
	`add_buy_uv` BIGINT COMMENT '加购点击uv，点击加购按钮的独立用户数',
	`click_select_uv` BIGINT COMMENT '点击去选购UV，点击去选购按钮的独立用户数',
	`remove_buy_uv` BIGINT COMMENT '移除常购商品uv，移除常购商品的独立用户数'
) 
COMMENT '采购助手页面流量数据表，记录采购助手页面各功能模块的用户行为数据，包括曝光、点击、购买等关键指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据所属日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='采购助手页面流量数据分析表，用于监控和优化采购助手功能效果') 
LIFECYCLE 30;