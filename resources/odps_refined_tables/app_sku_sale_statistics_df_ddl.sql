```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_sku_sale_statistics_df` (
  `warehouse_no` BIGINT COMMENT '库存仓号，取值范围：1-155',
  `warehouse_name` STRING COMMENT '库存仓名，如：杭州总仓、上海总仓、嘉兴总仓、广州总仓等',
  `transfer_out_sku` STRING COMMENT '转出SKU编码',
  `out_sale_cnt_b15d` DECIMAL(38,18) COMMENT '转出SKU过去15天平均销量',
  `out_sale_cnt_b7d` DECIMAL(38,18) COMMENT '转出SKU过去7天平均销量',
  `out_max_sale_cnt` BIGINT COMMENT '转出SKU过去七天销量峰值，取值范围：0-670',
  `out_min_sale_cnt` BIGINT COMMENT '转出SKU前一天单个订单最小销量，取值范围：0-10',
  `transfer_in_sku` STRING COMMENT '转入SKU编码',
  `in_sale_cnt_b15d` DECIMAL(38,18) COMMENT '转入SKU过去15天平均销量',
  `in_sale_cnt_b7d` DECIMAL(38,18) COMMENT '转入SKU过去7天平均销量',
  `in_max_sale_cnt` BIGINT COMMENT '转入SKU过去七天销量峰值，取值范围：0-670',
  `in_min_sale_cnt` BIGINT COMMENT '转入SKU前一天单个订单最小销量，取值范围：0-300',
  `date_flag` STRING COMMENT '时间字段标识，格式：yyyyMMdd',
  `in_max_sale_thirty` DECIMAL(38,18) COMMENT '转入SKU前三十天每日总销量峰值',
  `in_max_sale_sixty` DECIMAL(38,18) COMMENT '转入SKU前六十天每日总销量峰值',
  `in_sell_out_num_one` BIGINT COMMENT '转入SKU前一天售罄次数，取值范围：0-2',
  `in_sell_out_time_one` DECIMAL(38,18) COMMENT '转入SKU前一天售罄时长（单位：小时）',
  `in_sell_out_num_seven` BIGINT COMMENT '转入SKU前七天售罄次数，取值范围：0-2',
  `in_sell_out_time_seven` DECIMAL(38,18) COMMENT '转入SKU前七天售罄时长（单位：小时）',
  `in_sell_out_num_thirty` BIGINT COMMENT '转入SKU前三十天售罄次数，取值范围：0-6',
  `in_sell_out_time_thirty` DECIMAL(38,18) COMMENT '转入SKU前三十天售罄时长（单位：小时）',
  `in_sell_out_num_sixty` BIGINT COMMENT '转入SKU前六十天售罄次数，取值范围：0-8',
  `in_sell_out_time_sixty` DECIMAL(38,18) COMMENT '转入SKU前六十天售罄时长（单位：小时）',
  `out_max_sale_thirty` DECIMAL(38,18) COMMENT '转出SKU前三十天每日总销量峰值',
  `out_max_sale_sixty` DECIMAL(38,18) COMMENT '转出SKU前六十天每日总销量峰值',
  `out_sell_out_num_one` BIGINT COMMENT '转出SKU前一天售罄次数，取值范围：0-2',
  `out_sell_out_time_one` DECIMAL(38,18) COMMENT '转出SKU前一天售罄时长（单位：小时）',
  `out_sell_out_num_seven` BIGINT COMMENT '转出SKU前七天售罄次数，取值范围：0-3',
  `out_sell_out_time_seven` DECIMAL(38,18) COMMENT '转出SKU前七天售罄时长（单位：小时）',
  `out_sell_out_num_thirty` BIGINT COMMENT '转出SKU前三十天售罄次数，取值范围：0-4',
  `out_sell_out_time_thirty` DECIMAL(38,18) COMMENT '转出SKU前三十天售罄时长（单位：小时）',
  `out_sell_out_num_sixty` BIGINT COMMENT '转出SKU前六十天售罄次数，取值范围：0-8',
  `out_sell_out_time_sixty` DECIMAL(38,18) COMMENT '转出SKU前六十天售罄时长（单位：小时）'
) 
COMMENT '转换仓库+SKU销量数据统计表，包含转出和转入SKU的销量统计、售罄情况等指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = 'SKU销量转换统计表，用于分析仓库间SKU转换的销售表现',
  'lifecycle' = '30'
);
```