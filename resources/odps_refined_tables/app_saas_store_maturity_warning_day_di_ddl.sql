CREATE TABLE IF NOT EXISTS app_saas_store_maturity_warning_day_di(
	warehouse_no BIGINT COMMENT '仓库编号',
	warehouse_name STRING COMMENT '仓库名称',
	warehouse_provider STRING COMMENT '仓库服务商名称',
	pd_id BIGINT COMMENT '货品编码',
	sku STRING COMMENT 'SKU编码',
	saas_sku_id BIGINT COMMENT 'SaaS系统SKU ID',
	category_id BIGINT COMMENT '商品类目ID',
	sku_tenant_id BIGINT COMMENT 'SKU所属租户ID',
	warehouse_tenant_id BIGINT COMMENT '仓库所属租户ID',
	batch STRING COMMENT '商品批次号',
	quantity BIGINT COMMENT '库存数量',
	storage_days BIGINT COMMENT '可储存天数',
	in_store_date DATETIME COMMENT '入库日期，格式：年月日时分秒',
	storage_expiration_date DATETIME COMMENT '储存到期日期，格式：年月日时分秒',
	storage_remaining_days BIGINT COMMENT '储存剩余天数',
	approaching_maturity_percentage DECIMAL(38,18) COMMENT '临期百分比（保存百分数，如100表示100%）',
	status BIGINT COMMENT '储存状态：1-正常、2-临期、3-已滞库',
	day_tag BIGINT COMMENT '数据所属日期，格式：yyyyMMdd',
	use_flag BIGINT COMMENT '使用标识：0-停用、1-启用'
)
COMMENT '按天汇总的SaaS存货周转预警数据表，用于监控商品库存的临期和滞库情况'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd')
STORED AS ALIORC
TBLPROPERTIES ('comment'='SaaS存货周转预警日度数据表，包含仓库、商品、库存状态等详细信息')
LIFECYCLE 30;