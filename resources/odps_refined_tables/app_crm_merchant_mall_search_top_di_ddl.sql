CREATE TABLE IF NOT EXISTS app_crm_merchant_mall_search_top_di(
	`day_tag` STRING COMMENT '日期标记，格式为yyyyMMdd，表示数据所属的日期',
	`merchant_id` BIGINT COMMENT '商户ID，唯一标识一个商户，取值范围：2-165057',
	`product_name` STRING COMMENT '商品名称，用户搜索的商品名称',
	`search_num` BIGINT COMMENT '搜索次数，该商品在指定日期的搜索次数，取值范围：1-27'
) 
COMMENT '商户商城搜索记录表，存储近三天内搜索次数排名前10的商品搜索记录'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据采集日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='商户商城搜索记录表，用于分析商户在商城中的搜索行为模式') 
LIFECYCLE 30;