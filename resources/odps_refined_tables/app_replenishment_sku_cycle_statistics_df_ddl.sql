CREATE TABLE IF NOT EXISTS app_replenishment_sku_cycle_statistics_df(
	`supplier_id` BIGINT COMMENT '供应商ID，唯一标识供应商',
	`supplier_name` STRING COMMENT '供应商名称',
	`warehouse_no` BIGINT COMMENT '仓库编号，唯一标识仓库',
	`warehouse_name` STRING COMMENT '仓库名称',
	`sku` STRING COMMENT 'SKU编码，唯一标识商品',
	`purchase_quantity` BIGINT COMMENT '采购数量，统计周期内的采购总量',
	`unsubscribe_quantity` BIGINT COMMENT '退订数量，统计周期内的退订总量',
	`return_quantity` BIGINT COMMENT '退货数量，统计周期内的退货总量',
	`cycle_type` BIGINT COMMENT '周期类型：0-月度，1-季度，2-半年度，3-年度'
)
COMMENT '补货SKU周期统计表，记录各供应商、仓库、SKU在不同统计周期内的采购、退订、退货数量统计'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期')
STORED AS ALIORC
TBLPROPERTIES (
	'comment'='补货SKU周期统计表，用于分析商品补货周期内的采购和退货行为',
	'columnar.nested.type'='true'
)
LIFECYCLE 30;