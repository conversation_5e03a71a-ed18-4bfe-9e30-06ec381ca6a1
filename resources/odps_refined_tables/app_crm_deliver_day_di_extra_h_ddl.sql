```sql
CREATE TABLE IF NOT EXISTS app_crm_deliver_day_di_extra_h(
	order_no STRING COMMENT '订单编号，唯一标识一个订单',
	deliver_date STRING COMMENT '配送日期，格式为yyyyMMdd，表示年月日',
	cust_id BIGINT COMMENT '商家编号，唯一标识一个商家',
	cust_name STRING COMMENT '商家名称',
	address STRING COMMENT '商家详细地址',
	gmv_1d DECIMAL(38,18) COMMENT '配送金额，当日配送订单的总金额',
	zone_name STRING COMMENT '区域名称，如：无锡、杭州湾、大粤西等',
	coefficient STRING COMMENT '系数，取值范围为0.1-0.6的字符串',
	bd_name STRING COMMENT 'BD（商务拓展）姓名',
	m1 STRING COMMENT 'M1管理者（销售主管）姓名，即BD的直接上级',
	m2 STRING COMMENT 'M2管理者（销售经理）姓名，即M1的直接上级',
	spu_cnt BIGINT COMMENT 'SPU（标准化产品单元）数量，取值范围1-13',
	coefficient2 STRING COMMENT '系数S2，取值范围为0-0.2的字符串'
) 
COMMENT '当日下单的订单的配送计划表（销售业绩专用），按小时级别更新，包含订单配送信息、商家信息、区域划分、BD层级关系和业绩系数等数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='销售业绩专用的当日订单配送计划表，小时级更新') 
LIFECYCLE 32;
```