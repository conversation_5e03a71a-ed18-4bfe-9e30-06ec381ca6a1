```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_finance_store_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据所属的业务日期',
  `service_area` STRING COMMENT '大区名称，如：云南、华东等',
  `warehouse_no` BIGINT COMMENT '库存仓ID，数值型标识，取值范围：2-155',
  `warehouse_name` STRING COMMENT '库存仓名称，如：昆明总仓、上海总仓等',
  `category1` STRING COMMENT '商品一级类目，枚举类型：鲜果/乳制品/其他',
  `store_amt` DECIMAL(38,18) COMMENT '含税在库金额，单位：元，保留18位小数精度',
  `store_amt_notax` DECIMAL(38,18) COMMENT '不含税在库金额，单位：元，保留18位小数精度',
  `road_amt` DECIMAL(38,18) COMMENT '含税在途金额，单位：元，保留18位小数精度',
  `road_amt_notax` DECIMAL(38,18) COMMENT '不含税在途金额，单位：元，保留18位小数精度'
) 
COMMENT '财务口径收入数据表，存储各仓库按商品类目划分的在库和在途金额数据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据加载日期'
)
STORED AS ALIORC  
TBLPROPERTIES (
  'comment' = '财务口径收入数据表，包含各区域仓库的商品库存和运输中的金额统计'
) 
LIFECYCLE 30;
```