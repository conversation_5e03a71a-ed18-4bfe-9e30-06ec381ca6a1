```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_finance_recharge_record_details_di` (
  `recharge_record_no` STRING COMMENT '变动记录编号，唯一标识每笔变动记录',
  `addtime` DATETIME COMMENT '记录时间，格式为年月日时分秒（yyyy-MM-dd HH:mm:ss）',
  `cust_id` BIGINT COMMENT '客户ID，唯一标识客户',
  `cust_name` STRING COMMENT '客户名称',
  `city_id` BIGINT COMMENT '城市ID，唯一标识城市',
  `city_name` STRING COMMENT '城市名称',
  `cust_team` STRING COMMENT '客户团队类型，取值范围：平台客户、大客户',
  `recharge_type` STRING COMMENT '余额变动类型，如：消费、充值、退款等',
  `recharge_amount` DECIMAL(38,18) COMMENT '变动金额，正数表示增加，负数表示减少',
  `after_amount` DECIMAL(38,18) COMMENT '变动后剩余金额',
  `record_no` STRING COMMENT '关联业务编号：订单号、售后单号、充值单号等',
  `account_id` BIGINT COMMENT '操作子账号ID，执行操作的用户ID',
  `account_name` STRING COMMENT '操作人名称',
  `date_flag` STRING COMMENT '日期标识，格式为yyyyMMdd，用于业务日期标识'
) 
COMMENT '财务口径鲜沐卡变动明细表，记录客户余额变动明细信息，包括消费、充值等操作记录'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '财务口径鲜沐卡变动明细表，用于财务对账和余额变动分析',
  'lifecycle' = '30'
);
```