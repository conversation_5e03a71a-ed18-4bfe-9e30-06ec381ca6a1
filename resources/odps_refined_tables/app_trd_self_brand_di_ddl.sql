CREATE TABLE IF NOT EXISTS app_trd_self_brand_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计日期',
	`brand_name` STRING COMMENT '品牌名称，如：Protag蛋白标签、C味等',
	`large_area_name` STRING COMMENT '运营大区名称，如：上海大区、福州大区、广州大区等',
	`m3_name` STRING COMMENT 'M3管理者姓名，销售总监级别',
	`m2_name` STRING COMMENT 'M2管理者姓名，销售经理级别',
	`m1_name` STRING COMMENT 'M1管理者姓名，销售主管级别',
	`cust_type` STRING COMMENT '客户业态，枚举值：甜品冰淇淋、茶饮、咖啡、面包蛋糕等',
	`cust_group` STRING COMMENT '客户类型，枚举值：平台客户',
	`cust_id` BIGINT COMMENT '客户ID，唯一标识客户',
	`first_buy_date` STRING COMMENT '商品维度首次购买时间，格式为yyyyMMdd，表示年月日',
	`category1` STRING COMMENT '一级类目，如：其他',
	`category2` STRING COMMENT '二级类目，如：饮料、成品原料等',
	`category3` STRING COMMENT '三级类目，如：植物蛋白饮料、果冻类配料等',
	`category4` STRING COMMENT '四级类目，如：其他植物蛋白饮料、波波丨晶球等',
	`spu_name` STRING COMMENT '商品名称，如：Protag常温生椰乳、C味原味波波晶球等',
	`sku_id` STRING COMMENT 'SKU ID，商品库存单位唯一标识',
	`sku_disc` STRING COMMENT '商品规格描述，如：1L*12盒、1L*1盒、1KG*12包等',
	`orgin_total_amt` DECIMAL(38,18) COMMENT '交易应付GMV，订单原始总金额',
	`real_total_amt` DECIMAL(38,18) COMMENT '交易实付GMV，订单实际支付金额',
	`trade_sku_cnt` BIGINT COMMENT '交易SKU数量，取值范围：0-70',
	`dlv_orgin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV，履约原始总金额',
	`dlv_real_total_amt` DECIMAL(38,18) COMMENT '履约实付GMV，履约实际支付金额',
	`dlv_sku_cnt` BIGINT COMMENT '履约SKU数量，取值范围：0-100',
	`cost_amt` DECIMAL(38,18) COMMENT '商品成本金额'
) 
COMMENT '自营品牌数据表，包含自营品牌的销售交易和履约数据，按品牌、区域、客户等多维度统计'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期分区') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='自营品牌数据表，用于分析自营品牌的销售表现和客户行为') 
LIFECYCLE 30;