CREATE TABLE IF NOT EXISTS app_area_nohonour_cost_di(
	service_area STRING COMMENT '区域名称，枚举值：华中、华东、华南、福建、昆明等',
	allocate_amt DECIMAL(38,18) COMMENT '调拨费用，单位为元',
	purchase_car_amt DECIMAL(38,18) COMMENT '采购用车费用，单位为元',
	big_cust_amt DECIMAL(38,18) COMMENT '大客户费用，单位为元',
	purchase_store_amt DECIMAL(38,18) COMMENT '采购仓储费用，单位为元',
	total_nohonour_amt DECIMAL(38,18) COMMENT '非履约费用总额，单位为元',
	deliver_total_amt DECIMAL(38,18) COMMENT '配送费用总额，单位为元'
) 
COMMENT '区域维度非履约成本表，统计各区域在非履约环节的各项成本费用'
PARTITIONED BY (ds STRING COMMENT '分区字段，yyyyMMdd格式日期，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='区域维度非履约成本明细表，包含调拨、采购用车、大客户、采购仓储、配送等各项费用') 
LIFECYCLE 30;