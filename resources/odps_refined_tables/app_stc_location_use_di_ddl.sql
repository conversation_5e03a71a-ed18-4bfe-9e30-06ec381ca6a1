CREATE TABLE IF NOT EXISTS app_stc_location_use_di(
	warehouse_no STRING COMMENT '库存仓编号，如：10、69、117等',
	warehouse_name STRING COMMENT '库存仓名称，如：嘉兴总仓、东莞总仓、东莞冷冻总仓等',
	zone_type STRING COMMENT '库区类型，枚举值：冷藏、常温',
	cabinet_type STRING COMMENT '库位类型，枚举值：地堆、高位货架、地位货架',
	use_volume DECIMAL(38,18) COMMENT '使用体积，单位：立方米',
	use_code BIGINT COMMENT '使用库位数，取值范围：0-1410',
	logic_volume DECIMAL(38,18) COMMENT '理论体积，单位：立方米',
	total_volume DECIMAL(38,18) COMMENT '实际体积，单位：立方米',
	total_code BIGINT COMMENT '总库位数，取值范围：1-3180'
) 
COMMENT '仓配库位使用记录表，记录各仓库不同库区和库位类型的体积使用情况和库位数量统计'
PARTITIONED BY (ds STRING COMMENT '分区字段，yyyyMMdd格式日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='仓配库位使用记录表，用于分析仓库库位利用率和库存管理') 
LIFECYCLE 30;