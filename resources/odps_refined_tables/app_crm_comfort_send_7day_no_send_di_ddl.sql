CREATE TABLE IF NOT EXISTS app_crm_comfort_send_7day_no_send_di(
    m_id BIGINT COMMENT '商户ID，取值范围：29-571921',
    order_no STRING COMMENT '订单编号，唯一标识订单',
    order_time DATETIME COMMENT '订单生成时间，格式：年月日时分秒',
    sku_id STRING COMMENT '商品SKU编码，唯一标识商品',
    pd_name STRING COMMENT '商品名称',
    pd_weight STRING COMMENT '商品规格描述',
    pd_amount BIGINT COMMENT '订单内商品数量，取值范围：1-700',
    pay_amount DECIMAL(38,18) COMMENT '实付总额，单位：元',
    area_no BIGINT COMMENT '商户所在运营区域编号，取值范围：1001-44264',
    bd_id BIGINT COMMENT '归属BD ID，公海为0，取值范围：0-1189140',
    day_tag STRING COMMENT '数据所在日标记，格式：yyyyMMdd',
    m_name STRING COMMENT '商户名称',
    province STRING COMMENT '省份名称',
    city STRING COMMENT '城市名称',
    area STRING COMMENT '区县名称'
)
COMMENT 'CRM近7天未配送省心送订单数据表，记录商户近7天内未完成配送的省心送订单信息'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd')
STORED AS ALIORC
TBLPROPERTIES ('comment'='CRM省心送订单未配送监控表，用于追踪近7天内未完成配送的省心送订单情况')
LIFECYCLE 30;