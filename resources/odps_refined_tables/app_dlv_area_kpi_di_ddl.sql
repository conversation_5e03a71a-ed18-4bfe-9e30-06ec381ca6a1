CREATE TABLE IF NOT EXISTS app_dlv_area_kpi_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
	`area_name` STRING COMMENT '城配仓名称，如：长沙仓、南宁仓、深圳仓等',
	`total_point_cnt` BIGINT COMMENT '总点位数，包含所有配送点的数量',
	`point_cnt` BIGINT COMMENT '点位数（不含喜茶），排除喜茶品牌后的配送点数量',
	`no_in_time_point_cnt` BIGINT COMMENT '不及时点位数（不含喜茶），未按时配送的点位数量',
	`delay_time_point_cnt_2` BIGINT COMMENT '前置2小时不及时点位数（不含喜茶），提前2小时未按时配送的点位数量',
	`out_time` DECIMAL(38,18) COMMENT '配送超时时间（不含喜茶），单位为小时',
	`delay_time_2` DECIMAL(38,18) COMMENT '前置2小时配送超时时间（不含喜茶），单位为小时',
	`path_cnt` BIGINT COMMENT '线路数（不含喜茶），配送线路数量',
	`delay_path_cnt` BIGINT COMMENT '出库不及时线路数（不含喜茶），未按时出库的线路数量',
	`out_distance_point_cnt` BIGINT COMMENT '超距离点位数（含喜茶），超出规定距离的配送点数量',
	`after_sale_amt` DECIMAL(38,18) COMMENT '售后总金额，单位为元',
	`after_sale_amt_wah` DECIMAL(38,18) COMMENT '售后金额__仓配责，仓配责任导致的售后金额',
	`after_sale_amt_pur` DECIMAL(38,18) COMMENT '售后金额__采购责，采购责任导致的售后金额',
	`after_sale_amt_che` DECIMAL(38,18) COMMENT '售后金额__品控责，品控责任导致的售后金额',
	`after_sale_amt_pur_che` DECIMAL(38,18) COMMENT '售后金额__采购品控责，采购和品控共同责任导致的售后金额',
	`after_sale_amt_oth` DECIMAL(38,18) COMMENT '售后金额__其他责，其他责任导致的售后金额',
	`delivery_total_amt` DECIMAL(38,18) COMMENT '配送销售金额，单位为元',
	`coupon_amt` DECIMAL(38,18) COMMENT '优惠券金额，单位为元',
	`sku_cnt` BIGINT COMMENT '配送件数，配送的商品总件数',
	`error_sku_cnt` BIGINT COMMENT '错误件数，配送错误的商品件数，取值范围：0-9',
	`cust_cnt` BIGINT COMMENT '活跃客户数，活跃的客户数量',
	`error_cust_cnt` BIGINT COMMENT '错误客户数，配送错误的客户数量，取值范围：0-2'
) 
COMMENT '仓配KPI配送数据汇总表，包含各城配仓的配送效率、售后责任、销售金额等关键绩效指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据所属日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='仓配KPI配送数据汇总表，用于监控和分析各城配仓的配送绩效指标') 
LIFECYCLE 30;