```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_kpi_area_honour_wi` (
  `year` STRING COMMENT '年份，格式：YYYY',
  `week_of_year` BIGINT COMMENT '周数，取值范围：1-53',
  `monday` STRING COMMENT '周一日期，格式：YYYYMMDD',
  `sunday` STRING COMMENT '周日日期，格式：YYYYMMDD',
  `area_no` BIGINT COMMENT '城配仓编号，取值范围：1-155',
  `area_name` STRING COMMENT '城配仓名称',
  `sku_type` STRING COMMENT '商品类型，枚举值：自营、代仓、代售',
  `origin_total_amt` DECIMAL(38,18) COMMENT '原始总金额（元）',
  `real_total_amt` DECIMAL(38,18) COMMENT '实际总金额（元）',
  `cost_amt` DECIMAL(38,18) COMMENT '商品成本价（元）',
  `preferential_amt` DECIMAL(38,18) COMMENT '营销金额（元）',
  `origin_pay_rate` DECIMAL(38,18) COMMENT '应付毛利率',
  `real_pay_rate` DECIMAL(38,18) COMMENT '实付毛利率',
  `refund_amt` DECIMAL(38,18) COMMENT '售后退款总金额（元）',
  `cust_cnt` BIGINT COMMENT '客户数，取值范围：1-880',
  `cust_unit_amt` DECIMAL(38,18) COMMENT '客户单价（元/客户）',
  `order_cnt` BIGINT COMMENT '订单数，取值范围：1-1315',
  `point_cnt` BIGINT COMMENT '点位数，取值范围：1-929',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储成本（元）',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本（元）',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送成本（元）',
  `total_deliver_amt` DECIMAL(38,18) COMMENT '履约总费用（元）'
) 
COMMENT '履约KPI城配仓维度周度统计表，按城配仓维度统计每周的履约相关KPI指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：YYYYMMDD，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '履约KPI城配仓维度周度统计表，包含各城配仓每周的销售、成本、客户等相关指标统计',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```