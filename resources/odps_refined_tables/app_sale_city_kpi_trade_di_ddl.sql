```sql
CREATE TABLE IF NOT EXISTS app_sale_city_kpi_trade_di(
    `date` STRING COMMENT '日期，格式为yyyyMMdd',
    `administrative_city` STRING COMMENT '注册行政城市，枚举值包括：None、来宾市、张家界市、中山市、临沧市等',
    `zone_name` STRING COMMENT '销售区域名称，枚举值包括：无、长沙、大粤西、昆明等',
    `m1` STRING COMMENT '城市负责人（M1），枚举值包括：无、陈锐石、陈俊生、蒋柳选等',
    `m2` STRING COMMENT '区域负责人（M2），枚举值包括：无、彭琨、陈欲豪、孙日达等',
    `m3` STRING COMMENT '部门负责人（M3），枚举值包括：无、吕建杰、孙日达等',
    `order_origin_total_amt` DECIMAL(38,18) COMMENT '交易应付总金额（元）',
    `order_real_total_amt` DECIMAL(38,18) COMMENT '交易实付总金额（元）',
    `order_cust_cnt` BIGINT COMMENT '交易客户数，取值范围：0-554',
    `order_cust_arpu` DECIMAL(38,18) COMMENT 'ARPU(应付总金额/客户数)，单位：元/客户',
    `order_cnt` BIGINT COMMENT '交易订单数，取值范围：0-691',
    `lose_cust_cnt` BIGINT COMMENT '交易流失客户数（90天内活跃用户近60天未下单客户数），取值范围：0-413',
    `lose_cust_ratio` DECIMAL(38,18) COMMENT '交易流失率',
    `delivery_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额（元）',
    `delivery_real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额（元）',
    `delivery_cust_cnt` BIGINT COMMENT '履约活跃客户数，取值范围：0-615',
    `delivery_origin_profit` DECIMAL(38,18) COMMENT '履约应付毛利润（元）',
    `delivery_real_profit` DECIMAL(38,18) COMMENT '履约实付毛利润（元）',
    `delivery_after_profit` DECIMAL(38,18) COMMENT '履约后毛利润（元）',
    `delivery_days_avg` DECIMAL(38,18) COMMENT '履约频次（平均履约天数）',
    `delivery_point_cnt` BIGINT COMMENT '履约点位数，取值范围：0-673',
    `delivery_amt` DECIMAL(38,18) COMMENT '自营履约费用（元）',
    `new_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '新客履约应付总金额（元）',
    `new_delivery_real_total_amt` DECIMAL(38,18) COMMENT '新客履约实付总金额（元）',
    `new_delivery_cust_cnt` BIGINT COMMENT '新客履约活跃客户数，取值范围：0-23',
    `new_delivery_real_profit` DECIMAL(38,18) COMMENT '新客履约实付毛利润（元）',
    `old_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '老客履约应付总金额（元）',
    `old_delivery_real_total_amt` DECIMAL(38,18) COMMENT '老客履约实付总金额（元）',
    `old_delivery_cust_cnt` BIGINT COMMENT '老客履约活跃客户数，取值范围：0-592',
    `old_delivery_real_profit` DECIMAL(38,18) COMMENT '老客履约实付毛利润（元）',
    `at_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '(乳品)安佳铁塔履约应付总金额（元）',
    `at_delivery_real_total_amt` DECIMAL(38,18) COMMENT '(乳品)安佳铁塔履约实付总金额（元）',
    `at_delivery_cust_cnt` BIGINT COMMENT '(乳品)安佳铁塔履约活跃客户数，取值范围：0-85',
    `at_delivery_real_profit` DECIMAL(38,18) COMMENT '(乳品)安佳铁塔履约实付毛利润（元）',
    `noat_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '(乳品)非安佳铁塔履约应付总金额（元）',
    `noat_delivery_real_total_amt` DECIMAL(38,18) COMMENT '(乳品)非安佳铁塔履约实付总金额（元）',
    `noat_delivery_cust_cnt` BIGINT COMMENT '(乳品)非安佳铁塔履约活跃客户数，取值范围：0-158',
    `noat_delivery_real_profit` DECIMAL(38,18) COMMENT '(乳品)非安佳铁塔履约实付毛利润（元）',
    `timing_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '省心送履约应付总金额（元）',
    `timing_delivery_real_total_amt` DECIMAL(38,18) COMMENT '省心送履约实付总金额（元）',
    `timing_delivery_cust_cnt` BIGINT COMMENT '省心送履约活跃客户数，取值范围：0-36',
    `timing_delivery_real_profit` DECIMAL(38,18) COMMENT '省心送履约实付毛利润（元）',
    `order_sku_cnt` BIGINT COMMENT '交易SKU款数，取值范围：0-370',
    `order_sku_weight` DECIMAL(38,18) COMMENT '交易SKU重量（KG）',
    `delivery_sku_cnt` BIGINT COMMENT '履约SKU款数，取值范围：0-390',
    `delivery_sku_weight` DECIMAL(38,18) COMMENT '履约SKU重量（KG）'
) 
COMMENT '销售KPI指标汇总表，按城市和销售区域统计的交易和履约相关指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd')
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
    'comment'='销售KPI指标汇总表，包含城市维度的交易金额、客户数、订单数、流失率、履约金额、毛利润等核心业务指标') 
LIFECYCLE 30;
```