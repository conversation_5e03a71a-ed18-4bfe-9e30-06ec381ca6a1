CREATE TABLE IF NOT EXISTS app_stc_warehouse_category_kpi_wi(
	year STRING COMMENT '年份，格式：YYYY',
	week_of_year BIGINT COMMENT '周数，取值范围：1-53',
	monday STRING COMMENT '周一日期，格式：YYYYMMDD，表示年月日',
	sunday STRING COMMENT '周日日期，格式：YYYYMMDD，表示年月日',
	warehouse_no BIGINT COMMENT '库存仓编号，取值范围：2-155',
	warehouse_name STRING COMMENT '库存仓名称',
	category STRING COMMENT '类目：鲜果-新鲜水果类，标品-标准化产品',
	origin_total_amt DECIMAL(38,18) COMMENT '履约应付GMV，单位：元',
	real_total_amt DECIMAL(38,18) COMMENT '履约实付GMV，单位：元',
	after_sale_amt DECIMAL(38,18) COMMENT '售后金额，单位：元',
	after_sale_amt_check DECIMAL(38,18) COMMENT '售后金额品控责任部分，单位：元',
	damage_cnt BIGINT COMMENT '货损总数量，单位：件',
	damage_amt DECIMAL(38,18) COMMENT '货损总金额，单位：元',
	damage_cnt_wah BIGINT COMMENT '货损数量_仓配责任，单位：件',
	damage_amt_wah DECIMAL(38,18) COMMENT '货损金额_仓配责任，单位：元',
	damage_cnt_pur BIGINT COMMENT '货损数量_采购责任，单位：件',
	damage_amt_pur DECIMAL(38,18) COMMENT '货损金额_采购责任，单位：元',
	damage_cnt_opr BIGINT COMMENT '货损数量_运营责任，单位：件',
	damage_amt_opr DECIMAL(38,18) COMMENT '货损金额_运营责任，单位：元',
	damage_cnt_oth BIGINT COMMENT '货损数量_其他责任，单位：件',
	damage_amt_oth DECIMAL(38,18) COMMENT '货损金额_其他责任，单位：元',
	sale_cnt BIGINT COMMENT '销售数量，单位：件',
	sale_amt DECIMAL(38,18) COMMENT '销售金额，单位：元',
	test_cnt BIGINT COMMENT '抽检数量，单位：件',
	qualified_cnt BIGINT COMMENT '合格数量，单位：件',
	check_cnt BIGINT COMMENT '货检数量，单位：件',
	inbound_cnt BIGINT COMMENT '入库数量，单位：件'
) 
COMMENT '品控KPI统计表，按仓库和类目维度统计每周的品控相关指标，包括销售、货损、售后等数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='品控KPI周度统计表，用于监控各仓库不同类目商品的品控表现') 
LIFECYCLE 30;