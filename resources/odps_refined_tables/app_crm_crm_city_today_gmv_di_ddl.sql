CREATE TABLE IF NOT EXISTS app_crm_crm_city_today_gmv_di(
	city STRING COMMENT '城市名称，如：上海市、深圳市',
	area STRING COMMENT '区域名称，如：嘉定区、松江区、闵行区、静安区、光明区',
	order_gmv DECIMAL(38,18) COMMENT '下单总GMV（总交易额），单位：元',
	order_merchant BIGINT COMMENT '下单客户数，取值范围：0-439，平均约51个客户',
	pull_new_amount BIGINT COMMENT '拉新客户数量，取值范围：0-8，平均约0.88个',
	visit_num BIGINT COMMENT '拜访次数，取值范围：0-250，平均约22次',
	fruit_gmv DECIMAL(38,18) COMMENT '鲜果品类GMV，单位：元',
	dairy_gmv DECIMAL(38,18) COMMENT '乳制品品类GMV，单位：元',
	non_dairy_gmv DECIMAL(38,18) COMMENT '非乳制品品类GMV，单位：元',
	brand_gmv DECIMAL(38,18) COMMENT '自营品牌GMV，单位：元',
	agent_gmv DECIMAL(38,18) COMMENT '代售商品GMV，单位：元',
	reward_gmv DECIMAL(38,18) COMMENT '固定奖励SKU的GMV，单位：元',
	delivery_gmv DECIMAL(38,18) COMMENT '配送服务GMV，单位：元'
) 
COMMENT '城市当日GMV统计表，按城市和区域维度统计各类商品和服务的交易额、客户数、拜访次数等业务指标'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期（年月日）') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='城市维度当日GMV明细数据表，用于城市业务分析和业绩监控') 
LIFECYCLE 30;