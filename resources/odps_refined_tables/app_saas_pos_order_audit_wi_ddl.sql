CREATE TABLE IF NOT EXISTS app_saas_pos_order_audit_wi(
	channel_type BIGINT COMMENT '渠道类型：1=美团，2=其他（根据数据样本显示当前只有2）',
	tenant_id BIGINT COMMENT '租户ID',
	report_week STRING COMMENT '稽核自然周-周一日期，格式为yyyyMMdd',
	out_store_code STRING COMMENT '外部系统门店编码',
	out_store_name STRING COMMENT '外部系统门店名称',
	merchant_store_id BIGINT COMMENT '帆台门店ID',
	merchant_store_code STRING COMMENT '帆台门店编码',
	out_item_code STRING COMMENT '外部系统物料编码',
	out_item_name STRING COMMENT '外部系统物料名称',
	market_item_id BIGINT COMMENT '帆台商品ID',
	specification STRING COMMENT '商品规格',
	specification_unit STRING COMMENT '规格单位',
	use_count DECIMAL(38,18) COMMENT '销用总量',
	need_buy_count DECIMAL(38,18) COMMENT '应进货总量',
	real_buy_count BIGINT COMMENT '实际帆台进货总量'
) 
COMMENT 'SaaS门店进销稽核表(周)，用于统计门店商品销售与进货的稽核数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='SaaS门店进销稽核周表，包含门店商品销售、进货的稽核统计信息') 
LIFECYCLE 30;