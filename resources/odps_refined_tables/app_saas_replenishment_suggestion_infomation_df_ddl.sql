```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_saas_replenishment_suggestion_infomation_df` (
  `warehouse_no` BIGINT COMMENT '仓库编号，唯一标识一个仓库',
  `warehouse_name` STRING COMMENT '仓库名称',
  `sku` STRING COMMENT '商品SKU编码，唯一标识一个商品',
  `saas_sku_id` BIGINT COMMENT 'SaaS系统中的SKU ID，对应商品在SaaS系统中的唯一标识',
  `tenant_id` BIGINT COMMENT '租户ID，标识所属的租户',
  `category_id` BIGINT COMMENT '货品类目ID，标识商品所属的类目',
  `stock_level_minimum` BIGINT COMMENT '安全库存下限，库存低于此值需要补货',
  `stock_level_maximum` BIGINT COMMENT '安全库存上限，库存高于此值可能积压',
  `transfer_order_in_quantity` BIGINT COMMENT '调拨订单在途数量，正在调拨中的商品数量',
  `on_way_order_quantity` BIGINT COMMENT '采购订单在途数量，正在采购中的商品数量',
  `enabled_quantity` BIGINT COMMENT '可用库存数量，当前可用的商品库存',
  `past_seven_day_sales_quantity` BIGINT COMMENT '近7天销量，过去7天的销售数量',
  `past_fifteen_day_sales_quantity` BIGINT COMMENT '近15天销量，过去15天的销售数量',
  `past_thirty_day_sales_quantity` BIGINT COMMENT '近30天销量，过去30天的销售数量',
  `replenishment_advice_flag` BIGINT COMMENT '补货建议标识：0-不建议补货，1-建议补货',
  `replenishment_advice_quantity` BIGINT COMMENT '建议补货数量，当建议补货时具体的补货数量'
)
COMMENT '补货建议信息表，包含商品库存信息、销售数据和补货建议'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment'='补货建议信息表，基于商品库存、销售数据和库存阈值计算补货建议',
  'lifecycle'='30'
);
```