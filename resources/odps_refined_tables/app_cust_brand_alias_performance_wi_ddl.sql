CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_cust_brand_alias_performance_wi` (
  `year` STRING COMMENT '年份，格式：YYYY',
  `week_of_year` BIGINT COMMENT '周数，取值范围：1-53',
  `monday` STRING COMMENT '周一日期，格式：YYYYMMDD',
  `sunday` STRING COMMENT '周日日期，格式：YYYYMMDD',
  `brand_alias` STRING COMMENT '品牌别名',
  `order_origin_amt` DECIMAL(38,18) COMMENT '交易应付总金额',
  `order_real_amt` DECIMAL(38,18) COMMENT '交易实付总金额',
  `delivery_origin_amt` DECIMAL(38,18) COMMENT '履约应付总金额',
  `delivery_real_amt` DECIMAL(38,18) COMMENT '履约实付总金额',
  `delivery_cash_real_amt` DECIMAL(38,18) COMMENT '履约现结实付总金额',
  `delivery_bill_real_amt` DECIMAL(38,18) COMMENT '履约账期实付总金额',
  `delivery_cost_amt` DECIMAL(38,18) COMMENT '履约商品成本金额',
  `delivery_real_gross` DECIMAL(38,18) COMMENT '履约实付毛利润',
  `delivery_real_gross_rate` DECIMAL(38,18) COMMENT '履约实付毛利率，取值范围：0-1',
  `delivery_cust_cnt` BIGINT COMMENT '履约客户数，取值范围：0-322',
  `delivery_point_cnt` BIGINT COMMENT '履约累计点位数，取值范围：0-471',
  `after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后总金额',
  `after_sale_rate` DECIMAL(38,18) COMMENT '售后比例(已到货售后金额/履约实付GMV)，取值范围：0-1'
)
COMMENT '大客户品牌粒度监控表，按品牌别名为粒度统计交易和履约相关指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：YYYYMMDD，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '大客户品牌粒度监控表，用于监控各品牌在交易和履约环节的关键业务指标',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;