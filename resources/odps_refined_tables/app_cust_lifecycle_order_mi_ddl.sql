```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_cust_lifecycle_order_mi` (
  `month` STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
  `cust_team` STRING COMMENT '客户团队类型，枚举值：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户、批发',
  `cust_type` STRING COMMENT '客户行业类型，枚举值：面包蛋糕、西餐、咖啡、甜品冰淇淋、茶饮、其他',
  `life_cycle_detail` STRING COMMENT '生命周期标签（细），枚举值：A1、A2等',
  `register_province` STRING COMMENT '注册时省份名称',
  `register_city` STRING COMMENT '注册时城市名称',
  `city_id` BIGINT COMMENT '运营服务区ID，数值范围：1001-44271',
  `city_name` STRING COMMENT '运营服务区名称',
  `large_area_id` BIGINT COMMENT '运营服务大区ID，数值范围：1-93',
  `large_area_name` STRING COMMENT '运营服务大区名称',
  `cust_cnt` BIGINT COMMENT '客户数量，数值范围：1-6013',
  `other_real_amt` DECIMAL(38,18) COMMENT '订单实付总金额',
  `fruit_other_real_amt` DECIMAL(38,18) COMMENT '鲜果类订单实付金额',
  `dairy_other_real_amt` DECIMAL(38,18) COMMENT '乳制品类订单实付金额',
  `other_other_real_amt` DECIMAL(38,18) COMMENT '其他品类订单实付金额',
  `order_days_avg` DECIMAL(38,18) COMMENT '平均下单频次（天/次）',
  `fruit_order_days_avg` DECIMAL(38,18) COMMENT '鲜果类平均下单频次（天/次）',
  `dairy_order_days_avg` DECIMAL(38,18) COMMENT '乳制品类平均下单频次（天/次）',
  `other_order_days_avg` DECIMAL(38,18) COMMENT '其他品类平均下单频次（天/次）',
  `order_sku_cnt_avg` DECIMAL(38,18) COMMENT '平均去重SKU数量',
  `order_spu_cnt_avg` DECIMAL(38,18) COMMENT '平均去重SPU数量',
  `self_order_real_amt` DECIMAL(38,18) COMMENT '自营品牌订单实付金额',
  `self_order_days_avg` DECIMAL(38,18) COMMENT '自营品牌平均下单频次（天/次）'
)
COMMENT '客户标签汇总表，包含客户生命周期、地域分布、订单行为等多维度标签数据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '客户标签汇总表，用于客户分群分析和生命周期管理',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```