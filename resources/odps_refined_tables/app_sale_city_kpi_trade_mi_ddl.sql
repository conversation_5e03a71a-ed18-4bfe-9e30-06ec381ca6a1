```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_sale_city_kpi_trade_mi` (
  `month` STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
  `administrative_city` STRING COMMENT '注册行政城市名称',
  `zone_name` STRING COMMENT '销售区域名称，取值范围：各城市销售区域名称，"无"表示无区域归属',
  `m1` STRING COMMENT '城市负责人（M1）姓名，"无"表示无负责人',
  `m2` STRING COMMENT '区域负责人（M2）姓名，"无"表示无负责人',
  `m3` STRING COMMENT '部门负责人（M3）姓名，"无"表示无负责人',
  `order_origin_total_amt` DECIMAL(38,18) COMMENT '交易应付总金额（元）',
  `order_real_total_amt` DECIMAL(38,18) COMMENT '交易实付总金额（元）',
  `order_cust_cnt` BIGINT COMMENT '交易客户数（个）',
  `order_cust_arpu` DECIMAL(38,18) COMMENT 'ARPU值（平均每客户收入），计算公式：应付总金额/客户数',
  `order_cnt` BIGINT COMMENT '交易订单数（单）',
  `delivery_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额（元）',
  `delivery_real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额（元）',
  `delivery_cust_cnt` BIGINT COMMENT '履约活跃客户数（个）',
  `delivery_origin_profit` DECIMAL(38,18) COMMENT '履约应付毛利润（元）',
  `delivery_real_profit` DECIMAL(38,18) COMMENT '履约实付毛利润（元）',
  `delivery_after_profit` DECIMAL(38,18) COMMENT '履约后毛利润（元），扣除各项成本后的利润',
  `delivery_days_avg` DECIMAL(38,18) COMMENT '履约频次（次/客户），平均每个客户的履约次数',
  `delivery_point_cnt` BIGINT COMMENT '履约点位数（个）',
  `delivery_amt` DECIMAL(38,18) COMMENT '自营履约费用（元）',
  `new_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '新客履约应付总金额（元）',
  `new_delivery_real_total_amt` DECIMAL(38,18) COMMENT '新客履约实付总金额（元）',
  `new_delivery_cust_cnt` BIGINT COMMENT '新客履约活跃客户数（个）',
  `new_delivery_real_profit` DECIMAL(38,18) COMMENT '新客履约实付毛利润（元）',
  `old_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '老客履约应付总金额（元）',
  `old_delivery_real_total_amt` DECIMAL(38,18) COMMENT '老客履约实付总金额（元）',
  `old_delivery_cust_cnt` BIGINT COMMENT '老客履约活跃客户数（个）',
  `old_delivery_real_profit` DECIMAL(38,18) COMMENT '老客履约实付毛利润（元）',
  `at_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '(乳品)安佳铁塔履约应付总金额（元）',
  `at_delivery_real_total_amt` DECIMAL(38,18) COMMENT '(乳品)安佳铁塔履约实付总金额（元）',
  `at_delivery_cust_cnt` BIGINT COMMENT '(乳品)安佳铁塔履约活跃客户数（个）',
  `at_delivery_real_profit` DECIMAL(38,18) COMMENT '(乳品)安佳铁塔履约实付毛利润（元）',
  `noat_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '(乳品)非安佳铁塔履约应付总金额（元）',
  `noat_delivery_real_total_amt` DECIMAL(38,18) COMMENT '(乳品)非安佳铁塔履约实付总金额（元）',
  `noat_delivery_cust_cnt` BIGINT COMMENT '(乳品)非安佳铁塔履约活跃客户数（个）',
  `noat_delivery_real_profit` DECIMAL(38,18) COMMENT '(乳品)非安佳铁塔履约实付毛利润（元）',
  `timing_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '省心送履约应付总金额（元）',
  `timing_delivery_real_total_amt` DECIMAL(38,18) COMMENT '省心送履约实付总金额（元）',
  `timing_delivery_cust_cnt` BIGINT COMMENT '省心送履约活跃客户数（个）',
  `timing_delivery_real_profit` DECIMAL(38,18) COMMENT '省心送履约实付毛利润（元）',
  `order_sku_cnt` BIGINT COMMENT '交易SKU款数（款）',
  `order_sku_weight` DECIMAL(38,18) COMMENT '交易SKU重量（KG）',
  `delivery_sku_cnt` BIGINT COMMENT '履约SKU款数（款）',
  `delivery_sku_weight` DECIMAL(38,18) COMMENT '履约SKU重量（KG）'
)
COMMENT '销售KPI指标汇总表，按城市和月份统计的交易和履约相关指标，包含新老客、乳品分类、省心送等多维度数据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '销售KPI指标汇总表',
  'lifecycle' = '30'
);
```