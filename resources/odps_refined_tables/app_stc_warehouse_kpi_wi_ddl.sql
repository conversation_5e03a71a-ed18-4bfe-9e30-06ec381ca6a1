CREATE TABLE IF NOT EXISTS app_stc_warehouse_kpi_wi(
	`year` STRING COMMENT '年份，格式：YYYY',
	`week_of_year` BIGINT COMMENT '周数，取值范围：1-53',
	`monday` STRING COMMENT '周一日期，格式：YYYYMMDD，表示年月日',
	`sunday` STRING COMMENT '周日日期，格式：YYYYMMDD，表示年月日',
	`warehouse_no` BIGINT COMMENT '库存仓号，取值范围：2-155',
	`warehouse_name` STRING COMMENT '库存仓名称',
	`check_sku_cnt` BIGINT COMMENT '抽检数量，取值范围：0',
	`in_bound_sku_cnt` BIGINT COMMENT '入库数量，取值范围：0',
	`back_order_cnt` BIGINT COMMENT '退货总单数，取值范围：0-84',
	`finish_order_cnt` BIGINT COMMENT '已完成单数，取值范围：0-18',
	`damage_amt` DECIMAL(38,18) COMMENT '货损金额',
	`damage_amt_wah` DECIMAL(38,18) COMMENT '货损金额——仓配责',
	`sale_amt` DECIMAL(38,18) COMMENT '销售金额',
	`after_sale_amt` DECIMAL(38,18) COMMENT '售后总金额',
	`after_sale_amt_wah` DECIMAL(38,18) COMMENT '售后金额__仓配责',
	`after_sale_amt_pur` DECIMAL(38,18) COMMENT '售后金额__采购责',
	`after_sale_amt_che` DECIMAL(38,18) COMMENT '售后金额__品控责',
	`after_sale_amt_pur_che` DECIMAL(38,18) COMMENT '售后金额__采购品控责',
	`after_sale_amt_oth` DECIMAL(38,18) COMMENT '售后金额__其他责',
	`delivery_total_amt` DECIMAL(38,18) COMMENT '配送销售金额',
	`coupon_amt` DECIMAL(38,18) COMMENT '优惠券金额',
	`origin_total_amt` DECIMAL(38,18) COMMENT '应付GMV',
	`real_total_amt` DECIMAL(38,18) COMMENT '实付GMV',
	`storage_amt` DECIMAL(38,18) COMMENT '仓储成本',
	`arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本',
	`deliver_amt` DECIMAL(38,18) COMMENT '配送成本',
	`self_picked_amt` DECIMAL(38,18) COMMENT '采购自提成本',
	`other_amt` DECIMAL(38,18) COMMENT '其他成本',
	`allocation_amt` DECIMAL(38,18) COMMENT '调拨成本'
) 
COMMENT '仓配KPI库存数据表，包含仓库运营关键绩效指标数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：YYYYMMDD，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='仓配KPI库存数据表，包含各仓库的销售、售后、成本等关键绩效指标数据') 
LIFECYCLE 30;