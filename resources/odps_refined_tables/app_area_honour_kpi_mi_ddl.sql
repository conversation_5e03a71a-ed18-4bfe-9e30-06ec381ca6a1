CREATE TABLE IF NOT EXISTS app_area_honour_kpi_mi(
	month STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
	service_area STRING COMMENT '服务区域名称，枚举值包括：贵阳、昆明、华北、福建、华西等',
	point_in_cnt BIGINT COMMENT '内区点位数，统计区域内服务点的数量',
	point_out_cnt BIGINT COMMENT '外区点位数，统计区域外服务点的数量',
	out_quality BIGINT COMMENT '出库件数，统计出库的商品数量',
	trunk_amt DECIMAL(38,18) COMMENT '干线费用，运输干线产生的费用',
	delivery_in_amt DECIMAL(38,18) COMMENT '内区配送成本，区域内配送产生的费用',
	delivery_out_amt DECIMAL(38,18) COMMENT '外区配送成本，区域外配送产生的费用',
	storage_amt DECIMAL(38,18) COMMENT '仓储成本，商品存储产生的费用',
	delivery_total_amt DECIMAL(38,18) COMMENT '总配送费，内区和外区配送费用的总和'
) 
COMMENT '区域维度履约成本KPI月表，按月份和区域统计履约相关的各项成本指标'
PARTITIONED BY (ds STRING COMMENT '分区字段，yyyyMM格式，表示数据所属月份') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='区域维度履约成本KPI月度统计表，包含点位数、出库量、各项成本费用等关键指标') 
LIFECYCLE 30;