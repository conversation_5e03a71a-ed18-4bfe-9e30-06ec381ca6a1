CREATE TABLE IF NOT EXISTS app_m1_performance_comm_mi_extra_h(
	m1_id BIGINT COMMENT 'M1管理者ID',
	m1_name STRING COMMENT 'M1管理者姓名',
	bd_work_zone STRING COMMENT '负责区域名称',
	cust_group STRING COMMENT '客户类型：大客户，平台客户，全部',
	dlv_cust_cnt BIGINT COMMENT '已履约客户数量',
	total_dlv_cust_cnt BIGINT COMMENT '预计本月履约客户总数（包含离线数据和未来数据）',
	pb_total_dlv_cust_cnt BIGINT COMMENT 'PB月累计履约客户数量',
	score_total_dlv_cust_cnt BIGINT COMMENT '利润积分月累计履约客户数量',
	pb_comm_rate DECIMAL(38,18) COMMENT 'PB标品得分率',
	pb_increase_rate DECIMAL(38,18) COMMENT 'PB标品增长率',
	pb_gmv_base DECIMAL(38,18) COMMENT 'PBGMV基数',
	pb_last_m_dlv_gmv DECIMAL(38,18) COMMENT '上月PB履约GMV',
	pb_mtd_dlv_gmv DECIMAL(38,18) COMMENT 'PB本月截止昨晚履约GMV',
	pb_today_dlv_gmv DECIMAL(38,18) COMMENT 'PB今日待履约GMV',
	pb_today_trd_gmv DECIMAL(38,18) COMMENT 'PB今日交易本月待履约GMV',
	pb_other_dlv_gmv DECIMAL(38,18) COMMENT 'PB其余待履约GMV',
	pb_total_dlv_gmv DECIMAL(38,18) COMMENT 'PB本月预计总履约GMV',
	score_comm_rate DECIMAL(38,18) COMMENT '利润积分得分率',
	score_increase_rate DECIMAL(38,18) COMMENT '利润积分增长率',
	score_base BIGINT COMMENT '利润积分基数',
	last_m_scores DECIMAL(38,18) COMMENT '上月利润积分',
	mtd_scores DECIMAL(38,18) COMMENT '本月截止昨晚履约利润积分',
	today_dlv_scores DECIMAL(38,18) COMMENT '今日待履约利润积分',
	today_trd_scores DECIMAL(38,18) COMMENT '今日交易本月待履约利润积分',
	other_dlv_scores DECIMAL(38,18) COMMENT '其余待履约利润积分',
	total_scores DECIMAL(38,18) COMMENT '本月预计总履约利润积分'
)
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期')
STORED AS ALIORC
TBLPROPERTIES (
	'columnar.nested.type'='true',
	'comment'='销售区域M1利润积分和PB增长率系数表，记录M1管理者的业绩指标和增长系数'
)
LIFECYCLE 30;