CREATE TABLE IF NOT EXISTS app_largearea_sku_adjust_price_df(
	large_area_name STRING COMMENT '大区名称，枚举值包括：上海大区、杭州大区等',
	sku_id STRING COMMENT '商品SKU ID，唯一标识一个具体商品规格',
	spu_name STRING COMMENT '商品SPU名称，即标准产品单元名称',
	adjust_price_time DATETIME COMMENT '调价时间，格式为年月日时分秒（yyyy-MM-dd HH:mm:ss）',
	original_price DECIMAL(38,18) COMMENT '调价前的商品价格，单位为元',
	price DECIMAL(38,18) COMMENT '调价后的商品价格，单位为元',
	max_sale_price DECIMAL(38,18) COMMENT '近30天内该商品的最高销售价格，单位为元',
	min_sale_price DECIMAL(38,18) COMMENT '近30天内该商品的最低销售价格，单位为元'
) 
COMMENT '商品调价记录表，记录各大区商品的调价信息，包括调价前后价格和近30天价格波动范围'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='商品价格调整明细表，用于记录和分析商品价格变动情况') 
LIFECYCLE 30;