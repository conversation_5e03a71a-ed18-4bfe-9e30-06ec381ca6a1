```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_xianmu_search_category_prediction_df` (
  `query` STRING COMMENT '搜索词，用户输入的搜索关键词',
  `category4` STRING COMMENT '四级类目名称，商品分类体系中的第四级分类名称',
  `category4_id` STRING COMMENT '四级类目ID，商品分类体系中的第四级分类唯一标识',
  `category3` STRING COMMENT '三级类目名称，商品分类体系中的第三级分类名称',
  `click_cnt` BIGINT COMMENT '点击次数，该query下对应类目商品的点击数量统计',
  `category_rank` DOUBLE COMMENT '该四级类目的商品在该query的搜索结果页的点击数排名，取值范围[0.0769231, 1.0]',
  `category_percentile` DOUBLE COMMENT '该四级类目百分位，表示该类目在搜索结果中的相对位置，取值范围[0.000212781, 1.0]',
  `create_time` STRING COMMENT '创建时间，记录生成时间，格式为yyyy-MM-dd HH:mm:ss'
)
COMMENT '基于用户点击历史记录的搜索词-类目预测结果表，用于分析用户搜索行为与商品类目的关联关系'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，数据日期，格式为yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '搜索词类目预测结果表，包含搜索词与商品类目的关联分析和点击统计',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 365
```