CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_cust_lifecycle_progress_period_mi` (
  `month` STRING COMMENT '月份，格式为yyyyMM，表示数据统计的月份',
  `cust_team` STRING COMMENT '客户团队类型，枚举：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户',
  `cust_type` STRING COMMENT '客户类型，枚举：面包蛋糕、西餐、咖啡、甜品冰淇淋、茶饮、其他',
  `register_province` STRING COMMENT '客户注册时所在省份',
  `register_city` STRING COMMENT '客户注册时所在城市',
  `life_cycle_detail` STRING COMMENT '客户生命周期标签（细分），枚举值包括A2、A3、B2、L1、L2等',
  `cust_cnt` BIGINT COMMENT '总客户数量',
  `close_cust_cnt` BIGINT COMMENT '当月闭店客户数量',
  `order_days_1_cust_cnt` BIGINT COMMENT '当月下单0天客户数量',
  `order_days_2_cust_cnt` BIGINT COMMENT '当月下单1天客户数量',
  `order_days_3_cust_cnt` BIGINT COMMENT '当月下单2-3天客户数量',
  `order_days_4_cust_cnt` BIGINT COMMENT '当月下单4天及以上客户数量',
  `order_amt_avg_1_cust_cnt` BIGINT COMMENT '当月次均价0元客户数量',
  `order_amt_avg_2_cust_cnt` BIGINT COMMENT '当月次均价0-50元客户数量',
  `order_amt_avg_3_cust_cnt` BIGINT COMMENT '当月次均价50-200元客户数量',
  `order_amt_avg_4_cust_cnt` BIGINT COMMENT '当月次均价200-400元客户数量',
  `order_amt_avg_5_cust_cnt` BIGINT COMMENT '当月次均价400-600元客户数量',
  `order_amt_avg_6_cust_cnt` BIGINT COMMENT '当月次均价600元以上客户数量',
  `fruit_amt` DECIMAL(38,18) COMMENT '当月鲜果品类实付GMV（总交易额）',
  `fruit_cust_cnt` BIGINT COMMENT '当月购买鲜果品类客户数量',
  `dairy_amt` DECIMAL(38,18) COMMENT '当月乳制品品类实付GMV（总交易额）',
  `dairy_cust_cnt` BIGINT COMMENT '当月购买乳制品品类客户数量',
  `other_amt` DECIMAL(38,18) COMMENT '当月其他品类实付GMV（总交易额）',
  `other_cust_cnt` BIGINT COMMENT '当月购买其他品类客户数量',
  `self_amt` DECIMAL(38,18) COMMENT '当月自营品牌实付GMV（总交易额）',
  `self_cust_cnt` BIGINT COMMENT '当月购买自营品牌客户数量',
  `spu_cnt_avg` DECIMAL(38,18) COMMENT '当月平均SPU（标准化产品单元）数量',
  `fruit_spu_cnt_avg` DECIMAL(38,18) COMMENT '当月平均鲜果品类SPU数量',
  `dairy_spu_cnt_avg` DECIMAL(38,18) COMMENT '当月平均乳制品品类SPU数量',
  `other_spu_cnt_avg` DECIMAL(38,18) COMMENT '当月平均标品SPU数量',
  `lastmonth_order_days_1_cust_cnt` BIGINT COMMENT '上月同期下单0天客户数量',
  `lastmonth_order_days_2_cust_cnt` BIGINT COMMENT '上月同期下单1天客户数量',
  `lastmonth_order_days_3_cust_cnt` BIGINT COMMENT '上月同期下单2-3天客户数量',
  `lastmonth_order_days_4_cust_cnt` BIGINT COMMENT '上月同期下单4天及以上客户数量',
  `lastmonth_order_amt_avg_1_cust_cnt` BIGINT COMMENT '上月同期次均价0元客户数量',
  `lastmonth_order_amt_avg_2_cust_cnt` BIGINT COMMENT '上月同期次均价0-50元客户数量',
  `lastmonth_order_amt_avg_3_cust_cnt` BIGINT COMMENT '上月同期次均价50-200元客户数量',
  `lastmonth_order_amt_avg_4_cust_cnt` BIGINT COMMENT '上月同期次均价200-400元客户数量',
  `lastmonth_order_amt_avg_5_cust_cnt` BIGINT COMMENT '上月同期次均价400-600元客户数量',
  `lastmonth_order_amt_avg_6_cust_cnt` BIGINT COMMENT '上月同期次均价600元以上客户数量',
  `lastmonth_fruit_amt` DECIMAL(38,18) COMMENT '上月同期鲜果品类实付GMV',
  `lastmonth_fruit_cust_cnt` BIGINT COMMENT '上月同期购买鲜果品类客户数量',
  `lastmonth_dairy_amt` DECIMAL(38,18) COMMENT '上月同期乳制品品类实付GMV',
  `lastmonth_dairy_cust_cnt` BIGINT COMMENT '上月同期购买乳制品品类客户数量',
  `lastmonth_other_amt` DECIMAL(38,18) COMMENT '上月同期其他品类实付GMV',
  `lastmonth_other_cust_cnt` BIGINT COMMENT '上月同期购买其他品类客户数量',
  `lastmonth_self_amt` DECIMAL(38,18) COMMENT '上月同期自营品牌实付GMV',
  `lastmonth_self_cust_cnt` BIGINT COMMENT '上月同期购买自营品牌客户数量',
  `lastmonth_spu_cnt_avg` DECIMAL(38,18) COMMENT '上月同期平均SPU数量',
  `lastmonth_fruit_spu_cnt_avg` DECIMAL(38,18) COMMENT '上月同期平均鲜果品类SPU数量',
  `lastmonth_dairy_spu_cnt_avg` DECIMAL(38,18) COMMENT '上月同期平均乳制品品类SPU数量',
  `lastmonth_other_spu_cnt_avg` DECIMAL(38,18) COMMENT '上月同期平均标品SPU数量'
)
COMMENT '客户生命周期进度月汇总表（同期对比），按月统计客户生命周期各阶段的关键指标，包含当月数据和上月同期数据对比'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据加载日期')
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '客户生命周期进度月汇总表，用于分析客户在不同生命周期阶段的业务表现和趋势变化',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;