CREATE TABLE IF NOT EXISTS app_cust_brand_alias_category_performance_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计日期',
	`brand_alias` STRING COMMENT '品牌别名，用于标识不同品牌',
	`category` STRING COMMENT '品类：鲜果、乳制品、其他，表示商品所属品类',
	`order_origin_amt` DECIMAL(38,18) COMMENT '交易应付总金额，订单原始应付金额',
	`order_real_amt` DECIMAL(38,18) COMMENT '交易实付总金额，订单实际支付金额',
	`delivery_origin_amt` DECIMAL(38,18) COMMENT '履约应付总金额，履约环节原始应付金额',
	`delivery_real_amt` DECIMAL(38,18) COMMENT '履约实付总金额，履约环节实际支付金额',
	`delivery_cash_real_amt` DECIMAL(38,18) COMMENT '履约现结实付总金额，履约环节现金结算实际支付金额',
	`delivery_bill_real_amt` DECIMAL(38,18) COMMENT '履约账期实付总金额，履约环节账期结算实际支付金额',
	`delivery_cost_amt` DECIMAL(38,18) COMMENT '履约商品成本金额，履约环节商品成本金额',
	`delivery_real_gross` DECIMAL(38,18) COMMENT '履约实付毛利润，履约环节实际支付金额减去成本后的毛利润',
	`delivery_real_gross_rate` DECIMAL(38,18) COMMENT '履约实付毛利率，履约实付毛利润占履约实付总金额的比例',
	`delivery_cust_cnt` BIGINT COMMENT '履约客户数，参与履约的客户数量，取值范围0-148',
	`delivery_point_cnt` BIGINT COMMENT '履约累计点位数，履约涉及的点位数量，取值范围0-159',
	`after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后总金额，已到货商品的售后金额',
	`after_sale_rate` DECIMAL(38,18) COMMENT '售后比例，已到货售后金额占履约实付GMV的比例'
) 
COMMENT '大客户品牌+品类粒度监控表，按品牌别名和品类维度统计交易和履约相关指标'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='大客户品牌+品类粒度监控表，用于监控大客户业务中不同品牌和品类的交易、履约、售后等关键指标表现') 
LIFECYCLE 30;