CREATE TABLE IF NOT EXISTS app_kpi_category_trade_wi(
	year STRING COMMENT '年份，格式：yyyy',
	week_of_year BIGINT COMMENT '周数，取值范围：1-53',
	monday STRING COMMENT '周一日期，格式：yyyyMMdd，表示年月日',
	sunday STRING COMMENT '周日日期，格式：yyyyMMdd，表示年月日',
	category STRING COMMENT '品类，枚举类型取值范围：鲜果,乳制品,其他',
	origin_total_amt DECIMAL(38,18) COMMENT '应付总金额，单位：元',
	real_total_amt DECIMAL(38,18) COMMENT '实付总金额，单位：元',
	cust_cnt BIGINT COMMENT '客户数',
	cust_arpu DECIMAL(38,18) COMMENT 'ARPU(应付总金额/客户数)，单位：元/客户',
	order_cnt BIGINT COMMENT '订单数',
	order_avg DECIMAL(38,18) COMMENT '订单均价(应付总金额/订单数)，单位：元/订单',
	after_sale_noreceived_amt DECIMAL(38,18) COMMENT '未到货售后总金额，单位：元',
	after_sale_rate DECIMAL(38,18) COMMENT '退货率(未到货售后总金额/应付总金额)，小数形式',
	dire_origin_total_amt DECIMAL(38,18) COMMENT '直发采购应付总金额，单位：元',
	delivery_amt DECIMAL(38,18) COMMENT '运费（运费+超时加单费），单位：元',
	timing_origin_total_amt DECIMAL(38,18) COMMENT '省心送应付总金额，单位：元'
) 
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='交易口径KPI指标周汇总表，按品类统计每周的交易相关指标数据') 
LIFECYCLE 30;