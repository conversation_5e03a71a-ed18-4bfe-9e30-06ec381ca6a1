CREATE TABLE IF NOT EXISTS app_kpi_category_wholesale_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务发生日期',
	`sku_type` STRING COMMENT '商品类型：自营-平台自营商品，代仓-第三方仓库代发商品',
	`category` STRING COMMENT '商品品类：鲜果-新鲜水果类，乳制品-奶制品类，其他-其他商品类别',
	`order_gmv_amt` DECIMAL(38,18) COMMENT '交易应付总金额，订单层面的应付金额汇总',
	`deliver_gmv_amt` DECIMAL(38,18) COMMENT '履约应付总金额，实际履约交付的应付金额汇总',
	`deliver_cust_cnt` BIGINT COMMENT '履约客户数，实际发生履约交易的客户数量',
	`deliver_cust_arpu` DECIMAL(38,18) COMMENT '履约ARPU值，计算公式：履约应付总金额/履约客户数，表示每个客户的平均收入',
	`deliver_order_cnt` BIGINT COMMENT '履约订单数，实际完成履约的订单数量',
	`deliver_order_avg` DECIMAL(38,18) COMMENT '履约订单均价，计算公式：履约应付总金额/履约订单数，表示每个订单的平均金额',
	`deliver_cost_amt` DECIMAL(38,18) COMMENT '履约总成本，履约过程中产生的总成本金额',
	`deliver_gross_profit` DECIMAL(38,18) COMMENT '履约毛利润，计算公式：履约应付总金额-履约总成本',
	`deliver_gross_profit_rate` DECIMAL(38,18) COMMENT '履约毛利率，计算公式：履约毛利润/履约应付总金额，表示利润占比'
) 
COMMENT '交易口径KPI指标日汇总表，按商品类型和品类维度统计每日的交易和履约相关指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='交易口径KPI指标日汇总表，包含商品类型和品类维度的交易GMV、履约GMV、客户数、订单数、成本、利润等核心指标') 
LIFECYCLE 30;