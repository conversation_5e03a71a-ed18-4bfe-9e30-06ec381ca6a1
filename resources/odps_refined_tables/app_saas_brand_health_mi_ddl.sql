```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_saas_brand_health_mi` (
  `month` STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
  `brand_alias` STRING COMMENT '品牌名称',
  `order_store_cnt` BIGINT COMMENT '交易门店数',
  `all_store_cnt` BIGINT COMMENT '注册门店数',
  `total_gmv` DECIMAL(38,18) COMMENT '总交易GMV',
  `store_gmv` DECIMAL(38,18) COMMENT '店均交易GMV',
  `order_days` BIGINT COMMENT '下单天数',
  `home_login_days` BIGINT COMMENT '后台登录天数',
  `is_supplier` STRING COMMENT '是否完善供应商创建，枚举值：是/否',
  `is_supplier_sku` STRING COMMENT '是否关联供应商，枚举值：是/否',
  `is_purchases` STRING COMMENT '当月是否下采购单，枚举值：是/否',
  `is_after_sale` STRING COMMENT '当月是否有售后，枚举值：是/否',
  `is_store_log` STRING COMMENT '当月是否使用仓库模块，枚举值：是/否',
  `is_finance_log` STRING COMMENT '当月是否使用财务模块，枚举值：是/否',
  `tenant_id` BIGINT COMMENT '租户id'
) 
COMMENT 'SaaS品牌健康度表，用于分析品牌在SaaS平台上的经营健康度指标，包括交易数据、活跃度、功能使用情况等'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期，如20250917表示2025年9月17日'
)
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
  'comment'='SaaS品牌健康度分析表，包含品牌交易、活跃度、功能使用等多维度指标') 
LIFECYCLE 30;
```