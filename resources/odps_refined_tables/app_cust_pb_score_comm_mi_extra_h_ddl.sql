CREATE TABLE IF NOT EXISTS app_cust_pb_score_comm_mi_extra_h(
	order_source STRING COMMENT '订单来源：SaaS-来自SaaS系统的订单，鲜沐-来自鲜沐平台的订单',
	cust_id BIGINT COMMENT '客户ID',
	cust_name STRING COMMENT '客户名称',
	cust_type STRING COMMENT '客户类型：企业客户、个人客户等',
	bd_id BIGINT COMMENT 'BD ID',
	bd_name STRING COMMENT '履约当天负责的BD姓名',
	bd_work_zone STRING COMMENT 'BD工作区域',
	bd_region STRING COMMENT 'BD所属大区',
	dlv_real_amt DECIMAL(38,18) COMMENT '履约实付GMV金额',
	dlv_real_amt_at DECIMAL(38,18) COMMENT 'AT渠道履约实付金额',
	dlv_real_amt_pb DECIMAL(38,18) COMMENT 'PB渠道履约实付金额',
	last_dlv_real_amt_pb DECIMAL(38,18) COMMENT '上月PB渠道履约实付金额',
	dlv_real_amt_expo DECIMAL(38,18) COMMENT '流量品履约实付金额',
	dlv_real_amt_profit DECIMAL(38,18) COMMENT '利润品履约实付金额',
	dlv_real_amt_normal DECIMAL(38,18) COMMENT '常规品履约实付金额',
	dlv_real_amt_fruit DECIMAL(38,18) COMMENT '鲜果类履约实付金额',
	cate_group_score DECIMAL(38,18) COMMENT '利润积分',
	last_cate_group_score DECIMAL(38,18) COMMENT '上月利润积分',
	cate_group_score_today DECIMAL(38,18) COMMENT '今日待履约利润积分',
	order_group_score_today DECIMAL(38,18) COMMENT '今日交易待履约利润积分',
	other_group_score_today DECIMAL(38,18) COMMENT '其余待履约利润积分',
	dlv_real_amt_today_pb DECIMAL(38,18) COMMENT 'PB渠道今日待履约实付金额',
	dlv_order_amt_today_pb DECIMAL(38,18) COMMENT 'PB渠道今日交易待履约实付金额',
	dlv_other_amt_today_pb DECIMAL(38,18) COMMENT 'PB渠道其余待履约实付金额',
	total_cate_group_score DECIMAL(38,18) COMMENT '本月预计利润积分（已履约+待履约）',
	totla_cate_group_amt_pb DECIMAL(38,18) COMMENT '本月预计PB渠道总GMV（已履约+待履约）',
	total_dlv_real_amt DECIMAL(38,18) COMMENT '本月预计履约总GMV（已履约+待履约）'
) 
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='客户分BD月累计利润积分表，记录客户按BD维度的利润积分和履约金额数据') 
LIFECYCLE 30;