```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_dlv_area_city_cust_sku_deliver_trade_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务日期',
  `large_area_id` BIGINT COMMENT '运营服务大区ID，唯一标识一个大区',
  `large_area_name` STRING COMMENT '运营服务大区名称，如：杭州大区、上海大区等',
  `city_id` BIGINT COMMENT '运营服务区ID，唯一标识一个城市服务区',
  `city_name` STRING COMMENT '运营服务区名称，如：宁波、上海、南京等',
  `cust_id` BIGINT COMMENT '客户ID，唯一标识一个客户',
  `cust_name` STRING COMMENT '客户名称，如：沪上阿姨9、熹熹嚷嚷等',
  `cust_type` STRING COMMENT '客户类型，枚举值：面包蛋糕,西餐,咖啡,甜品冰淇淋,茶饮,其他',
  `cust_team` STRING COMMENT '客户团队类型，枚举值：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户、批发',
  `sku_id` STRING COMMENT 'SKU编号，商品最小库存单位标识',
  `spu_id` BIGINT COMMENT 'SPU ID，标准产品单位标识',
  `spu_name` STRING COMMENT '商品名称，如：Protag常温生椰乳',
  `sku_disc` STRING COMMENT '商品描述，如：1L*12盒，表示商品规格',
  `timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送交易应付GMV，原始订单金额',
  `timing_real_total_amt` DECIMAL(38,18) COMMENT '省心送交易实付GMV，实际支付金额',
  `origin_total_amt` DECIMAL(38,18) COMMENT '交易应付GMV，总原始订单金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '交易实付GMV，总实际支付金额',
  `timing_dlv_origin_total_amt` DECIMAL(38,18) COMMENT '省心送履约应付GMV，省心送履约原始金额',
  `timing_dlv_real_total_amt` DECIMAL(38,18) COMMENT '省心送履约实付GMV，省心送履约实际金额',
  `timing_dlv_cost_amt` DECIMAL(38,18) COMMENT '省心送成本，省心送服务成本金额',
  `dlv_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV，总履约原始金额',
  `dlv_real_total_amt` DECIMAL(38,18) COMMENT '履约实付GMV，总履约实际金额',
  `timing_no_dlv_origin_total_amt` DECIMAL(38,18) COMMENT '省心送未履约应付GMV，省心送未履约原始金额',
  `timing_no_dlv_real_total_amt` DECIMAL(38,18) COMMENT '省心送未履约实付GMV，省心送未履约实际金额',
  `timing_no_dlv_sku_cnt` BIGINT COMMENT '省心送未履约数量，取值范围：0-40',
  `timing_dlv_7_day_sku_cnt` BIGINT COMMENT '省心送未来7天预约配送数量，取值范围：0-30',
  `timing_dlv_14_day_sku_cnt` BIGINT COMMENT '省心送未来14天预约配送数量，取值范围：0-30'
) 
COMMENT '运营服务区+客户+库存仓 SKU 省心送交易明细表，记录各省心送业务的交易和履约明细数据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据分区日期'
)
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
               'comment'='省心送业务交易明细数据表，包含大区、城市、客户、商品的交易和履约信息') 
LIFECYCLE 30;
```