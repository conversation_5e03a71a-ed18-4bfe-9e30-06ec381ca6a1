CREATE TABLE IF NOT EXISTS app_bd_cust_effective_mi(
	month STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
	zone_name STRING COMMENT '运营区域名称',
	bd_id BIGINT COMMENT 'BD ID，唯一标识一个业务发展人员，取值范围：-1表示未知或无效BD，其他为正整数',
	bd_name STRING COMMENT 'BD姓名',
	m1_name STRING COMMENT 'M1管理者（销售主管）姓名，即BD的直接上级',
	m2_name STRING COMMENT 'M2管理者（销售经理）姓名，即M1的直接上级',
	m3_name STRING COMMENT 'M3管理者（销售总监）姓名，即M2的直接上级',
	cust_m1 STRING COMMENT '客户区域M1管理者姓名',
	is_same_city STRING COMMENT '是否同城：是-同城，否-非同城',
	dlv_cust_cnt BIGINT COMMENT '履约客户数，取值范围：1-656，表示当月履约的客户数量',
	effect_cust_cnt BIGINT COMMENT '月活客户数，取值范围：0-654，表示当月活跃的客户数量',
	time_scheduling DECIMAL(38,18) COMMENT '时间进度，取值范围：0.0-1.0，表示当月时间进度百分比'
) 
PARTITIONED BY (ds STRING COMMENT '分区字段，yyyyMMdd格式，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='BD履约有效月活客户数统计表，记录每个BD在不同区域的履约客户数和月活客户数，包含层级关系和同城标识') 
LIFECYCLE 30;