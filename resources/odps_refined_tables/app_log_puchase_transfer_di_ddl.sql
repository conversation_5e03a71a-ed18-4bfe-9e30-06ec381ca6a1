CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_log_puchase_transfer_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
  `type` STRING COMMENT '实验分组类型，取值范围：V3、V4、对照组',
  `category` STRING COMMENT '页面模块分类，取值范围：搜索页面、分类页面、采购助手',
  `cust_cnt` BIGINT COMMENT '曝光UV，表示在对应模块曝光的独立用户数',
  `click_uv` BIGINT COMMENT '商品点击UV，表示在对应模块点击商品的独立用户数',
  `order_cnt` BIGINT COMMENT '订单数量，宽口径统计：如下单前15分钟内在对应模块有点击行为，则纳入统计'
)
COMMENT '采购助手转化漏斗分析表，用于追踪用户从曝光到点击再到下单的转化路径'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，用于数据管理和查询优化'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '采购助手转化漏斗分析表，用于分析各实验分组在不同页面模块的用户行为转化效果',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;