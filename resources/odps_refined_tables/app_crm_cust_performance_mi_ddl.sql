```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_crm_cust_performance_mi` (
  `month` STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
  `cust_type` STRING COMMENT '客户类型：全部、茶饮、咖啡等',
  `administrative_city` STRING COMMENT '行政城市名称',
  `m1` STRING COMMENT '城市负责人（M1管理者）姓名',
  `origin_amt` DECIMAL(38,18) COMMENT '应付GMV（商品交易总额），单位：元',
  `real_amt` DECIMAL(38,18) COMMENT '实付GMV（实际支付金额），单位：元',
  `cost_amt` DECIMAL(38,18) COMMENT '成本金额，单位：元',
  `origin_profit_ratio` DECIMAL(38,18) COMMENT '应付毛利率（应付GMV减去成本后除以应付GMV）',
  `real_profit_ratio` DECIMAL(38,18) COMMENT '实付毛利率（实付GMV减去成本后除以实付GMV）',
  `real_profit_avg` DECIMAL(38,18) COMMENT '人均实付毛利值（实付毛利总额除以客户数），单位：元/人',
  `cust_cnt` BIGINT COMMENT '下单客户数',
  `spu_cnt_avg` DECIMAL(38,18) COMMENT '人均SPU数（标准化产品单位数量平均值）',
  `origin_arpu` DECIMAL(38,18) COMMENT 'ARPU值（应付）（每用户平均收入，基于应付GMV计算），单位：元/人',
  `fruit_amt_ratio` DECIMAL(38,18) COMMENT '鲜果GMV占比（鲜果类商品GMV占总GMV的比例）',
  `dairy_amt_ratio` DECIMAL(38,18) COMMENT '乳制品GMV占比（乳制品类商品GMV占总GMV的比例）',
  `other_amt_ratio` DECIMAL(38,18) COMMENT '其他GMV占比（其他类商品GMV占总GMV的比例）',
  `fruit_profit_amt_ratio` DECIMAL(38,18) COMMENT '鲜果实付毛利值占比（鲜果类实付毛利占总实付毛利的比例）',
  `dairy_profit_amt_ratio` DECIMAL(38,18) COMMENT '乳制品实付毛利值占比（乳制品类实付毛利占总实付毛利的比例）',
  `other_profit_amt_ratio` DECIMAL(38,18) COMMENT '其他实付毛利值占比（其他类实付毛利占总实付毛利的比例）',
  `fruit_cust_cnt_ratio` DECIMAL(38,18) COMMENT '鲜果覆盖率（购买鲜果类商品的客户数占总客户数的比例）',
  `dairy_cust_cnt_ratio` DECIMAL(38,18) COMMENT '乳制品覆盖率（购买乳制品类商品的客户数占总客户数的比例）',
  `other_cust_cnt_ratio` DECIMAL(38,18) COMMENT '其他覆盖率（购买其他类商品的客户数占总客户数的比例）',
  `fruit_category4_cnt_avg` DECIMAL(38,18) COMMENT '鲜果人均四级类目数（鲜果类四级类目数量的平均值）',
  `dairy_category4_cnt_avg` DECIMAL(38,18) COMMENT '乳制品人均四级类目数（乳制品类四级类目数量的平均值）',
  `other_category4_cnt_avg` DECIMAL(38,18) COMMENT '其他人均四级类目数（其他类四级类目数量的平均值）'
)
COMMENT '客户类型粒度平台销售业绩报表月汇总表，按客户类型、行政城市、M1管理者等多维度统计销售业绩指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '客户类型粒度平台销售业绩报表月汇总表，包含GMV、毛利率、覆盖率等关键业务指标',
  'lifecycle' = '30'
)
```