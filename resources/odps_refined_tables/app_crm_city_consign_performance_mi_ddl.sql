CREATE TABLE IF NOT EXISTS app_crm_city_consign_performance_mi(
	month STRING COMMENT '月份，格式：yyyyMM',
	administrative_city STRING COMMENT '销售所属行政城市',
	zone_name STRING COMMENT '区域名称',
	m1 STRING COMMENT '城市负责人（M1）',
	m2 STRING COMMENT '区域负责人（M2）',
	m3 STRING COMMENT '部门负责人（M3）',
	cust_type STRING COMMENT '客户类型；枚举值：面包蛋糕,西餐,咖啡,甜品冰淇淋,茶饮,其他',
	order_cust_cnt BIGINT COMMENT '下单客户数',
	order_sku_cnt BIGINT COMMENT '销量',
	order_cnt BIGINT COMMENT '订单数',
	real_total_amt DECIMAL(38,18) COMMENT '订单实付金额',
	drop_in_visit_cust_cnt BIGINT COMMENT '上门拜访客户数（上门/有效）',
	visit_cust_cnt BIGINT COMMENT '总拜访客户数'
) 
COMMENT '行政城市粒度代售业绩报表月汇总表，按行政城市维度统计代售业务的月度业绩表现'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='行政城市粒度代售业绩报表月汇总表，包含各行政城市的销售业绩指标和拜访数据') 
LIFECYCLE 30;