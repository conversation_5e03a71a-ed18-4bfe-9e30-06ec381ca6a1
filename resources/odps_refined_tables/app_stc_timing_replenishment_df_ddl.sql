CREATE TABLE IF NOT EXISTS app_stc_timing_replenishment_df(
	`warehouse_no` BIGINT COMMENT '库存仓号，数值型标识',
	`warehouse_name` STRING COMMENT '库存仓名称',
	`sku_id` STRING COMMENT 'SKU ID，商品唯一标识',
	`sku_disc` STRING COMMENT '商品描述，包含规格等信息',
	`spu_name` STRING COMMENT '商品名称',
	`supplier` STRING COMMENT '供货商名称',
	`store_method` STRING COMMENT '存储方式；枚举值：未分类,冷冻,冷藏,常温,顶汇大流通',
	`timing_dlv_14_day_sku_cnt` BIGINT COMMENT '未来14天设置配送量',
	`timing_dlv_14_30_day_sku_cnt` BIGINT COMMENT '未来14天至30天设置配送量',
	`timing_dlv_30_90_day_sku_cnt` BIGINT COMMENT '未来30天至90天设置配送量',
	`timing_no_plan_sku_cnt` BIGINT COMMENT '未设置配送量',
	`init_quantity` BIGINT COMMENT '期末库存数量',
	`purchase_on_way_quality` BIGINT COMMENT '采购在途数量',
	`no_dlv_sku_cnt` BIGINT COMMENT '未履约数量',
	`supplement_14day` BIGINT COMMENT '未来14天需求量',
	`supplement_30day` BIGINT COMMENT '未来30天需求量',
	`supplement_out_30day` BIGINT COMMENT '30天以上需求量',
	`supplement_cnt` DECIMAL(38,18) COMMENT '建议补货量，高精度小数'
) 
COMMENT '省心送仓储预警数据表，包含库存预警和补货建议相关数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='省心送仓储预警数据表，用于库存管理和补货预警分析') 
LIFECYCLE 30;