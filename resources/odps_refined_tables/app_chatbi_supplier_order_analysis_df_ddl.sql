```sql
CREATE TABLE IF NOT EXISTS app_chatbi_supplier_order_analysis_df(
    order_no STRING COMMENT '出库订单号，来源于WMS库存任务处理明细表，唯一标识出库订单',
    tenant_id BIGINT COMMENT '租户ID，用于多租户数据隔离，标识数据所属的租户。枚举值：1-鲜沐或顺鹿达数据',
    warehouse_no BIGINT COMMENT '仓库编号，标识商品所在的具体仓库，用于仓库维度分析。取值范围：2-155',
    warehouse_name STRING COMMENT '仓库名称，仓库的中文名称，便于业务人员理解和报表展示',
    sku_id STRING COMMENT 'SKU商品编码，商品的唯一标识码，用于商品维度分析和库存管理',
    batch STRING COMMENT '批次号，商品的批次信息，用于追溯商品来源和质量管理',
    order_time DATETIME COMMENT '订单创建时间，记录订单的下单时间，格式为年月日时分秒，用于时间维度分析。取值范围：2023-11-21 10:54:22 至 2025-09-17 19:22:36',
    sku_price DECIMAL(38,18) COMMENT 'SKU单价，商品的单位价格，单位为元，保留18位小数',
    sku_subtotal_price DECIMAL(38,18) COMMENT 'SKU小计金额，等于数量乘以单价，单位为元，用于计算订单总价值，保留18位小数',
    quantity BIGINT COMMENT 'SKU数量，该SKU在订单中的出库数量，支持大数值存储。取值范围：1-108，平均值：1.93',
    sku_sub_type BIGINT COMMENT 'SKU子类型。枚举值：1-代销不入仓(全品类)、2-代销入仓(全品类)、3-自营(经销)(我司自行采购、入库、销售)、5-顺鹿达商品(鲜果POP)。取值范围：1-5，主要值：3(75%数据)',
    pd_name STRING COMMENT '商品名称，商品的中文名称描述，便于业务识别和报表展示',
    weight STRING COMMENT 'SKU规格，商品规格描述，如：250G*4盒/一级/4*5、1L*12盒等',
    supplier_id STRING COMMENT '供应商ID，支持多供应商场景，多个供应商ID用逗号分隔，用于供应商维度分析',
    supplier_name STRING COMMENT '供应商名称，供应商的企业名称，便于供应商管理和分析',
    supplier_manager STRING COMMENT '供应商负责人，供应商的业务对接人员姓名，用于供应商关系管理',
    after_sale_amount DECIMAL(38,18) COMMENT '售后总金额，单位为元，保留18位小数',
    deliveryed_after_sale_amount DECIMAL(38,18) COMMENT '已配送售后金额，小于等于售后总金额，单位为元，保留18位小数',
    after_sales_times BIGINT COMMENT '此SKU售后次数。取值范围：1(仅403条记录有值)',
    first_after_sale_handle_date STRING COMMENT '首次售后处理日期，格式为年月日：yyyyMMdd',
    last_after_sale_handle_date STRING COMMENT '最近一次售后处理日期，格式为年月日：yyyyMMdd',
    sku_real_subtotal_price DECIMAL(38,18) COMMENT 'SKU最终金额（减去了售后金额），计算公式：sku_subtotal_price - after_sale_amount。统计GMV时优先使用该字段，单位为元，保留18位小数'
)
COMMENT '订单SKU供应商分析结果表，整合订单、商品、仓库、供应商等维度信息，支持多维度业务分析。最后修改时间：2025-09-18 02:08:03'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为年月日：yyyyMMdd')
STORED AS ALIORC
TBLPROPERTIES ('comment'='订单SKU供应商分析结果表，用于供应商绩效分析、商品销售分析、仓库运营分析等多维度业务分析')
LIFECYCLE 7;
```