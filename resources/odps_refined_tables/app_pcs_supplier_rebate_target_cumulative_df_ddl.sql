CREATE TABLE IF NOT EXISTS app_pcs_supplier_rebate_target_cumulative_df(
	`year` BIGINT COMMENT '年度，取值范围：2023-2025',
	`period` STRING COMMENT '周期，取值范围：H1（上半年）、H2（下半年）',
	`supplier_id` BIGINT COMMENT '供货商ID，取值范围：28-2912',
	`supplier_name` STRING COMMENT '供货商名称',
	`warehouse_no` BIGINT COMMENT '仓库编号，取值范围：1-155',
	`warehouse_name` STRING COMMENT '仓库名称',
	`commodity_temperature_zone` STRING COMMENT '商品温区，取值范围：冷藏、常温',
	`purchase_order_amount_in_period` DECIMAL(38,18) COMMENT '周期内采购下单金额（含税）',
	`purchase_order_amount_excluding_tax` DECIMAL(38,18) COMMENT '采购下单金额（不含税）',
	`purchase_order_quantity_in_period` BIGINT COMMENT '周期内采购下单件数，取值范围：0-76964',
	`purchase_order_weight_in_period` DECIMAL(38,18) COMMENT '周期内采购下单重量（单位：千克）',
	`purchase_order_volume_in_period` DECIMAL(38,18) COMMENT '周期内采购下单体积（单位：立方米）',
	`purchase_inbound_amount_in_period` DECIMAL(38,18) COMMENT '周期内采购入库金额（含税）',
	`purchase_inbound_amount_excluding_tax` DECIMAL(38,18) COMMENT '采购入库金额（不含税）',
	`purchase_inbound_quantity_in_period` BIGINT COMMENT '周期内采购入库件数，取值范围：0-76029',
	`purchase_inbound_weight_in_period` DECIMAL(38,18) COMMENT '周期内采购入库重量（单位：千克）',
	`purchase_inbound_volume_in_period` DECIMAL(38,18) COMMENT '周期内采购入库体积（单位：立方米）',
	`purchase_reservation_amount_in_period` DECIMAL(38,18) COMMENT '周期内采购预约金额（含税）',
	`purchase_reservation_amount_excluding_tax` DECIMAL(38,18) COMMENT '采购预约金额（不含税）',
	`purchase_reservation_quantity_in_period` BIGINT COMMENT '周期内采购预约件数，取值范围：0-76029',
	`purchase_reservation_weight_in_period` DECIMAL(38,18) COMMENT '周期内采购预约重量（单位：千克）',
	`purchase_reservation_volume_in_period` DECIMAL(38,18) COMMENT '周期内采购预约体积（单位：立方米）'
) 
COMMENT '供应商返利目标累计月维度表，记录供应商在半年周期内的采购相关数据，用于返利目标计算'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据日期')
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='供应商返利目标累计表，包含采购下单、入库、预约等关键业务指标') 
LIFECYCLE 30;