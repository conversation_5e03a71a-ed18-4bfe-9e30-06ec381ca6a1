CREATE TABLE IF NOT EXISTS app_pcs_successful_supplier_in_bound_df(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务发生日期',
	`warehouse_no` BIGINT COMMENT '库存仓号，取值范围：10-69，常见值：10、48、69',
	`warehouse_name` STRING COMMENT '库存仓名称，如：东莞总仓、长沙总仓、嘉兴总仓等',
	`sku_id` STRING COMMENT 'SKU编号，商品最小库存单位唯一标识',
	`spu_name` STRING COMMENT '商品名称，如：紫香1号百香果、国产红心火龙果等',
	`spu_disc` STRING COMMENT '商品描述，包含规格、等级、重量等详细信息',
	`supplier_id` BIGINT COMMENT '供应商编号，取值范围：147-2284，常见值在712-1853之间',
	`supplier` STRING COMMENT '供应商名称，如：石云花、淄博荣福商贸有限公司等',
	`in_bound_sku_cnt` BIGINT COMMENT '采购入库量，取值范围：0-160，大部分为0，表示入库商品数量',
	`in_bound_amt` DECIMAL(38,18) COMMENT '采购入库金额，精确到18位小数，表示入库商品总金额',
	`refence` DECIMAL(38,18) COMMENT '基准值，精确到18位小数，可能为价格基准或参考值'
) 
COMMENT '中标竞价供应商历史入库情况表，记录供应商中标后的商品入库明细数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据加载日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='记录中标竞价供应商的商品入库历史数据，包括入库数量、金额及基准信息') 
LIFECYCLE 30;