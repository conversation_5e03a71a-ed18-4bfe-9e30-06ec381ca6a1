```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_stc_warehouse_category_allocate_mi` (
  `month` STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
  `warehouse_no` BIGINT COMMENT '库存仓编号，唯一标识仓库，取值范围：10-170',
  `warehouse_name` STRING COMMENT '库存仓名称，如：嘉兴总仓、华西总仓等',
  `allocate_cnt` BIGINT COMMENT '调拨出库数量，统计周期内调拨出库的商品数量，取值范围：1-24863',
  `allocate_cost` DECIMAL(38,18) COMMENT '调拨出库成本，统计周期内调拨出库的成本金额，单位：元',
  `allocate_amt` DECIMAL(38,18) COMMENT '调拨出库金额，统计周期内调拨出库的销售金额，单位：元',
  `deliver_cnt` BIGINT COMMENT '履约数量，统计周期内实际履约的商品数量，取值范围：0-100317',
  `deliver_amt` DECIMAL(38,18) COMMENT '履约金额，统计周期内实际履约的销售金额，单位：元'
)
COMMENT '调拨出库月数据统计表，记录各仓库每月的调拨出库和履约情况，用于库存管理和绩效分析'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment'='调拨出库月数据统计表',
  'lifecycle'='30'
);
```