CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_largeg_area_sku_order_wi` (
  `year` STRING COMMENT '年份，格式为yyyy',
  `week_of_year` BIGINT COMMENT '周数，取值范围1-53',
  `monday` STRING COMMENT '周一日期，格式为yyyyMMdd（年月日）',
  `sunday` STRING COMMENT '周日日期，格式为yyyyMMdd（年月日）',
  `sku_id` STRING COMMENT 'SKU编号，商品唯一标识',
  `spu_name` STRING COMMENT '商品名称',
  `sku_disc` STRING COMMENT '商品描述，包含规格信息',
  `large_area_id` BIGINT COMMENT '运营大区ID，取值范围1-93',
  `large_area_name` STRING COMMENT '运营大区名称',
  `cust_cnt` BIGINT COMMENT '交易客户数，取值范围1-539',
  `large_area_cust_cnt` BIGINT COMMENT '运营大区总客户数，取值范围1-10712'
)
COMMENT '区域渗透数据表，统计各运营大区SKU级别的客户交易情况，用于分析商品在各区域的渗透率'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd（年月日）'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '区域渗透数据分析表，包含SKU级别的交易客户数和区域总客户数统计',
  'lifecycle' = '30'
);