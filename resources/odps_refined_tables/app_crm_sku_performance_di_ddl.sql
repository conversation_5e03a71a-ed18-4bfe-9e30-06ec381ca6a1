CREATE TABLE IF NOT EXISTS app_crm_sku_performance_di(
    `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
    `sku_id` STRING COMMENT '商品SKU，商品的唯一标识编码',
    `spu_name` STRING COMMENT '商品名称，商品的标准产品单元名称',
    `sku_spec` STRING COMMENT '规格，商品的具体规格参数',
    `sku_category` STRING COMMENT '商品类目：枚举值包括自营品牌、鲜果、乳制品、非乳制品',
    `category4` STRING COMMENT '四级分类，商品的最细粒度分类',
    `administrative_city` STRING COMMENT '商家对应的行政城市，商家所在的城市名称',
    `is_simple` STRING COMMENT '是否单店：枚举值包括是、否，标识是否为单店经营',
    `cust_type` STRING COMMENT '客户类型；枚举值：面包蛋糕、西餐、咖啡、甜品冰淇淋、茶饮、其他',
    `sku_cnt` BIGINT COMMENT '商品数量，销售的商品数量统计',
    `real_gmv_amt` DECIMAL(38,18) COMMENT '订单实付金额，客户实际支付的订单金额',
    `origin_gmv_amt` DECIMAL(38,18) COMMENT '订单应付金额，订单原始应付金额',
    `deliver_gmv_amt` DECIMAL(38,18) COMMENT '履约实付金额，实际履约支付的金额',
    `deliver_cost` DECIMAL(38,18) COMMENT '履约成本，履约过程中产生的成本费用',
    `deliver_cust_cnt` BIGINT COMMENT '履约客户数，完成履约的客户数量统计',
    `order_cnt` BIGINT COMMENT '订单数，产生的订单数量统计',
    `cust_cnt` BIGINT COMMENT '订单客户数，产生订单的客户数量统计',
    `m1` STRING COMMENT '城市负责人（M1），城市级别的管理负责人',
    `m2` STRING COMMENT '区域负责人（M2），区域级别的管理负责人',
    `m3` STRING COMMENT '部门负责人（M3），部门级别的管理负责人'
)
COMMENT 'SKU粒度平台销售业绩报表周汇总表，按SKU维度统计的平台销售业绩数据，按周进行汇总'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期分区')
STORED AS ALIORC
TBLPROPERTIES ('comment'='SKU销售业绩统计表，包含商品销售数量、金额、客户数等核心业务指标')
LIFECYCLE 30;