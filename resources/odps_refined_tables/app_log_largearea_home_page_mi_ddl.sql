```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_log_largearea_home_page_mi` (
  `month_of_year` STRING COMMENT '统计月份，格式为yyyyMM，如202509表示2025年9月',
  `largearea_no` STRING COMMENT '运营大区ID，唯一标识符',
  `largearea_name` STRING COMMENT '运营大区名称，如广州大区、上海大区等',
  `cust_type` STRING COMMENT '客户业态，枚举类型：烘焙、茶饮、其他等',
  `home_page_uv` BIGINT COMMENT '首页独立访客数',
  `home_page_pv` BIGINT COMMENT '首页页面浏览量',
  `search_uv` BIGINT COMMENT '搜索功能使用的独立用户数',
  `search_pv` BIGINT COMMENT '搜索功能使用的页面浏览量',
  `classification_uv` BIGINT COMMENT '分类功能使用的独立用户数',
  `classification_pv` BIGINT COMMENT '分类功能使用的页面浏览量',
  `banner_uv` BIGINT COMMENT 'Banner广告的独立曝光用户数',
  `banner_pv` BIGINT COMMENT 'Banner广告的页面曝光次数',
  `waist_banner_uv` BIGINT COMMENT '腰部Banner广告的独立曝光用户数',
  `waist_banner_pv` BIGINT COMMENT '腰部Banner广告的页面曝光次数',
  `carousel_banner_uv` BIGINT COMMENT '轮播Banner广告的独立曝光用户数',
  `carousel_banner_pv` BIGINT COMMENT '轮播Banner广告的页面曝光次数',
  `activity_uv` BIGINT COMMENT '活动模块的独立访问用户数',
  `activity_pv` BIGINT COMMENT '活动模块的页面访问次数',
  `fresh_artners_uv` BIGINT COMMENT '鲜拍档模块的独立访问用户数',
  `fresh_artners_pv` BIGINT COMMENT '鲜拍档模块的页面访问次数',
  `special_uv` BIGINT COMMENT '特价活动模块的独立访问用户数',
  `special_pv` BIGINT COMMENT '特价活动模块的页面访问次数',
  `temporary_uv` BIGINT COMMENT '临保商品模块的独立访问用户数',
  `temporary_pv` BIGINT COMMENT '临保商品模块的页面访问次数',
  `boutique_uv` BIGINT COMMENT '精品优选模块的独立访问用户数',
  `boutique_pv` BIGINT COMMENT '精品优选模块的页面访问次数',
  `common_recommend_uv` BIGINT COMMENT '常用商品推荐模块的独立访问用户数',
  `common_recommend_pv` BIGINT COMMENT '常用商品推荐模块的页面访问次数',
  `recommend_uv` BIGINT COMMENT '商品推荐模块的独立访问用户数',
  `recommend_pv` BIGINT COMMENT '商品推荐模块的页面访问次数',
  `cust_flag` STRING COMMENT '是否老客标识，枚举类型：是、否',
  `bottom_nav_pv` BIGINT COMMENT '底部导航栏总页面浏览量',
  `bottom_nav_uv` BIGINT COMMENT '底部导航栏总独立用户数',
  `category_pv` BIGINT COMMENT '底部导航栏-分类页面的浏览量',
  `procurement_assistant_pv` BIGINT COMMENT '底部导航栏-采购助手页面的浏览量',
  `shopping_cart_pv` BIGINT COMMENT '底部导航栏-购物车页面的浏览量',
  `personal_center_pv` BIGINT COMMENT '底部导航栏-个人中心页面的浏览量',
  `category_uv` BIGINT COMMENT '底部导航栏-分类页面的独立用户数',
  `procurement_assistant_uv` BIGINT COMMENT '底部导航栏-采购助手页面的独立用户数',
  `shopping_cart_uv` BIGINT COMMENT '底部导航栏-购物车页面的独立用户数',
  `personal_center_uv` BIGINT COMMENT '底部导航栏-个人中心页面的独立用户数',
  `category_tankeng_pv` BIGINT COMMENT '分类弹坑功能的总页面浏览量',
  `category_tankeng_uv` BIGINT COMMENT '分类弹坑功能的总独立用户数',
  `fresh_fruit_pv` BIGINT COMMENT '分类弹坑-鲜果品类的页面浏览量',
  `wangyou_tavern_pv` BIGINT COMMENT '分类弹坑-忘忧酒馆品类的页面浏览量',
  `dairy_products_pv` BIGINT COMMENT '分类弹坑-乳制品品类的页面浏览量',
  `coffee_pv` BIGINT COMMENT '分类弹坑-咖啡品类的页面浏览量',
  `baking_supplies_pv` BIGINT COMMENT '分类弹坑-烘培辅料品类的页面浏览量',
  `frozen_cakes_pv` BIGINT COMMENT '分类弹坑-冷冻蛋糕品类的页面浏览量',
  `bar_supplies_pv` BIGINT COMMENT '分类弹坑-水吧辅料品类的页面浏览量',
  `sugar_syrup_pv` BIGINT COMMENT '分类弹坑-糖|糖浆品类的页面浏览量',
  `western_ingredients_pv` BIGINT COMMENT '分类弹坑-西餐辅料品类的页面浏览量',
  `packaging_materials_pv` BIGINT COMMENT '分类弹坑-包材品类的页面浏览量',
  `fresh_fruit_uv` BIGINT COMMENT '分类弹坑-鲜果品类的独立用户数',
  `wangyou_tavern_uv` BIGINT COMMENT '分类弹坑-忘忧酒馆品类的独立用户数',
  `dairy_products_uv` BIGINT COMMENT '分类弹坑-乳制品品类的独立用户数',
  `coffee_uv` BIGINT COMMENT '分类弹坑-咖啡品类的独立用户数',
  `baking_supplies_uv` BIGINT COMMENT '分类弹坑-烘培辅料品类的独立用户数',
  `frozen_cakes_uv` BIGINT COMMENT '分类弹坑-冷冻蛋糕品类的独立用户数',
  `bar_supplies_uv` BIGINT COMMENT '分类弹坑-水吧辅料品类的独立用户数',
  `sugar_syrup_uv` BIGINT COMMENT '分类弹坑-糖|糖浆品类的独立用户数',
  `western_ingredients_uv` BIGINT COMMENT '分类弹坑-西餐辅料品类的独立用户数',
  `packaging_materials_uv` BIGINT COMMENT '分类弹坑-包材品类的独立用户数',
  `recipe_market_pv` BIGINT COMMENT '配方集市模块的页面浏览量',
  `recipe_market_uv` BIGINT COMMENT '配方集市模块的独立用户数',
  `home_page_ai_assistant_pv` BIGINT COMMENT '首页AI助手功能的页面浏览量',
  `home_page_ai_assistant_uv` BIGINT COMMENT '首页AI助手功能的独立用户数'
) 
COMMENT '首页各模块流量统计表，按运营大区、客户业态等维度统计各功能模块的UV/PV数据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment'='首页各模块流量统计表，包含首页各功能模块的用户访问行为数据',
  'columnar.nested.type'='true'
) 
LIFECYCLE 30;
```