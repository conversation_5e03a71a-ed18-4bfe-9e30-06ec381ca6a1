```sql
CREATE TABLE IF NOT EXISTS app_pcs_batch_sku_in_bound_di(
    `purchase_date` DATETIME COMMENT '采购时间，格式为年月日时分秒',
    `batch_no` STRING COMMENT '采购批次号',
    `sku_id` STRING COMMENT '采购SKU编号',
    `spu_name` STRING COMMENT '商品名称',
    `sku_disc` STRING COMMENT 'SKU规格描述',
    `sku_type` STRING COMMENT 'SKU类型，取值范围：自营、代仓、代售',
    `category1` STRING COMMENT '一级类目',
    `category2` STRING COMMENT '二级类目',
    `category3` STRING COMMENT '三级类目',
    `category4` STRING COMMENT '四级类目',
    `purchaser` STRING COMMENT '采购人姓名',
    `in_store_warehouse_no` BIGINT COMMENT '入库仓库编号',
    `in_store_warehouse_name` STRING COMMENT '入库仓库名称',
    `in_store_state` STRING COMMENT '入库状态，取值范围：待入库、已入库、部分入库',
    `supplier` STRING COMMENT '供应商名称',
    `purchase_sku_cnt` BIGINT COMMENT '采购数量',
    `purchase_total_amt` DECIMAL(38,18) COMMENT '采购总金额',
    `purchase_unit_cost` DECIMAL(38,18) COMMENT '采购单价',
    `in_store_sku_cnt` BIGINT COMMENT '已入库数量',
    `no_store_sku_cnt` BIGINT COMMENT '未入库数量',
    `back_sku_cnt` BIGINT COMMENT '退货数量',
    `back_order_sku_cnt` BIGINT COMMENT '退订数量',
    `in_store_sku_cnt_t_1` DECIMAL(38,18) COMMENT 'T-1日入库成本',
    `in_store_sku_cnt_t_2` DECIMAL(38,18) COMMENT 'T-2日入库成本',
    `in_store_sku_cnt_t_3` DECIMAL(38,18) COMMENT 'T-3日入库成本',
    `in_store_sku_cnt_t_4` DECIMAL(38,18) COMMENT 'T-4日入库成本',
    `in_store_sku_cnt_t_5` DECIMAL(38,18) COMMENT 'T-5日入库成本',
    `in_store_sku_cnt_t_6` DECIMAL(38,18) COMMENT 'T-6日入库成本',
    `in_store_sku_cnt_t_7` DECIMAL(38,18) COMMENT 'T-7日入库成本'
)
COMMENT '采购数据汇总表，记录采购批次、SKU信息、入库状态、成本等采购相关数据'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = '采购数据汇总表，包含采购批次信息、商品信息、入库状态、采购成本和历史入库成本数据',
    'lifecycle' = '30'
);
```