```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_log_module_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
  `module` STRING COMMENT '模块名称，枚举值包括：banner、活动、购物车、首页推荐、其他等',
  `cust_uv` BIGINT COMMENT '进入客户UV（Unique Visitor），统计去重后的客户数量',
  `cust_pv` BIGINT COMMENT '进入客户PV（Page View），统计客户进入模块的总次数',
  `sku_cnt` BIGINT COMMENT '商品曝光数，统计模块中展示的商品数量',
  `click_uv` BIGINT COMMENT '点击商品UV，统计去重后的点击商品客户数',
  `click_pv` BIGINT COMMENT '点击商品PV，统计商品点击总次数',
  `addbuy_uv` BIGINT COMMENT '加买客户UV，统计去重后的加购客户数',
  `addbuy_pv` BIGINT COMMENT '加买客户PV，统计加购操作总次数',
  `order_cust_cnt` BIGINT COMMENT '下单客户数，统计去重后的下单客户数量',
  `sku_pv` BIGINT COMMENT '商品总曝光次数，统计所有商品被展示的总次数',
  `sku_click_pv` BIGINT COMMENT '商品总点击次数，统计所有商品被点击的总次数',
  `cust_type` STRING COMMENT '业态类型，枚举值包括：咖啡、茶饮、其他等',
  `cust_flag` STRING COMMENT '是否老客标识，枚举值：是-老客，否-新客',
  `customer_click_pv` BIGINT COMMENT '点击客户PV，统计客户点击操作总次数',
  `customer_click_uv` BIGINT COMMENT '点击客户UV，统计去重后的点击客户数',
  `order_cnt` BIGINT COMMENT '下单次数，统计所有下单订单数量',
  `sku_exposure_pv` BIGINT COMMENT '商品曝光PV，统计商品被展示的总次数',
  `sku_exposure_uv` BIGINT COMMENT '商品曝光UV，统计去重后的看到商品的客户数',
  `sku_add_to_cart_pv` BIGINT COMMENT '商品加购PV，统计商品被加入购物车的总次数',
  `sku_add_to_cart_uv` BIGINT COMMENT '商品加购UV，统计去重后的加购客户数'
)
COMMENT '首页各模块数据统计表，记录首页各个模块的用户行为数据，包括曝光、点击、加购、下单等关键指标'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，用于数据管理和查询优化'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '首页模块数据明细表，用于分析用户在各模块的行为特征和转化效果',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```