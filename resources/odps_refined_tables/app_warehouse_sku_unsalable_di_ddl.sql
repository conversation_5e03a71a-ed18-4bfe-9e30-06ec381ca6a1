```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_warehouse_sku_unsalable_di` (
  `warehouse_no` BIGINT COMMENT '库存仓编号',
  `warehouse_name` STRING COMMENT '库存仓名称',
  `sku_id` STRING COMMENT 'SKU编号，商品唯一标识',
  `category1` STRING COMMENT '一级类目名称',
  `category4_id` STRING COMMENT '四级类目ID',
  `category4` STRING COMMENT '四级类目名称',
  `sku_brand` STRING COMMENT '品牌名称',
  `spu_name` STRING COMMENT 'SPU名称，标准产品单元名称',
  `disc` STRING COMMENT 'SKU描述，包含规格包装信息',
  `sku_origin` STRING COMMENT '商品产地：进口，国产',
  `sku_property` STRING COMMENT 'SKU性质：常规，活动，临保，拆包，不卖，破袋',
  `store_method` STRING COMMENT '存储方式：冷冻,冷藏,常温',
  `warn_days` BIGINT COMMENT '滞销预警天数阈值',
  `purchase_in_quality` BIGINT COMMENT '历史两周采购入库数量',
  `sale_out_quality` BIGINT COMMENT '历史两周销售出库数量',
  `allocate_in_quality` BIGINT COMMENT '历史两周调拨入库数量',
  `allocate_out_quality` BIGINT COMMENT '历史两周调拨出库数量',
  `purchase_on_way_quality` BIGINT COMMENT '采购在途数量',
  `allocate_on_way_quality` BIGINT COMMENT '调拨在途数量',
  `lately_arrived_time` STRING COMMENT '八周内最近入库时间，格式yyyyMMdd',
  `enable_quality` BIGINT COMMENT '在库可用库存数量',
  `safe_quality` BIGINT COMMENT '安全库存数量',
  `avg_quality` BIGINT COMMENT '过去2周日均出库量',
  `doc_quality` BIGINT COMMENT 'DOC（现货）库存数量',
  `doc_on_way_quality` BIGINT COMMENT 'DOC（现货+在途）库存数量',
  `use_days` BIGINT COMMENT '库存可用天数',
  `duration_on_shelf` DECIMAL(38,18) COMMENT '上架时长（天）',
  `duration_sale_out` DECIMAL(38,18) COMMENT '售罄时长（天）',
  `sale_out_rask` DECIMAL(38,18) COMMENT '售罄率（百分比）',
  `is_sale_out_rask` STRING COMMENT '售罄风险判定：是，否',
  `unsalable_decided` STRING COMMENT '动销判定：两周低动销，两周无动销，-（正常动销）',
  `enable_amt` DECIMAL(38,18) COMMENT '库存成本金额'
)
COMMENT '每日SKU、库存仓维度滞销情况表，用于监控和分析商品滞销状况'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = 'SKU滞销分析表，包含库存、销售、调拨等维度数据，用于滞销预警和库存优化决策',
  'lifecycle' = '30'
);
```