CREATE TABLE IF NOT EXISTS app_sku_area_cust_category_gross_margin_wi(
	`monday` STRING COMMENT '周一，格式：yyyyMMdd，表示周一开始日期',
	`sunday` STRING COMMENT '周日，格式：yyyyMMdd，表示周日结束日期',
	`large_area_name` STRING COMMENT '服务大区名称',
	`warehouse_name` STRING COMMENT '库存仓名称',
	`cust_class` STRING COMMENT '客户大类：大客户（茶百道）、大客户（非茶百道）、批发、普通（非品牌）、普通（品牌）',
	`sku_id` STRING COMMENT 'SKU编号，商品唯一标识',
	`sku_type` STRING COMMENT 'SKU类型：自营、代仓',
	`spu_name` STRING COMMENT '商品名称',
	`category_1` STRING COMMENT '一级类目',
	`category_2` STRING COMMENT '二级类目',
	`category_3` STRING COMMENT '三级类目',
	`category_4` STRING COMMENT '四级类目',
	`origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额',
	`real_total_amt` DECIMAL(38,18) COMMENT '实付总金额',
	`cost_amt` DECIMAL(38,18) COMMENT '总成本',
	`gross_margin` DECIMAL(38,18) COMMENT '毛利润',
	`sku_cnt` BIGINT COMMENT '配送数量，统计周期内配送的商品数量',
	`origin_amt_beforew` DECIMAL(38,18) COMMENT '上周应付总金额',
	`real_amt_beforew` DECIMAL(38,18) COMMENT '上周实付总金额',
	`cost_amt_beforew` DECIMAL(38,18) COMMENT '上周总成本',
	`gross_margin_beforew` DECIMAL(38,18) COMMENT '上周毛利润',
	`sku_cnt_beforew` DECIMAL(38,18) COMMENT '上周配送数量'
) 
COMMENT '毛利数据SKU维度周表，按SKU、区域、客户类别统计的周度毛利数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：yyyyMMdd，数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='SKU维度毛利周报表，包含商品销售、成本、毛利等关键指标，支持周度同比分析') 
LIFECYCLE 30;