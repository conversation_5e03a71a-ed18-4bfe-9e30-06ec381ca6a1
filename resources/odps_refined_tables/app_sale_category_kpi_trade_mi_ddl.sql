```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_sale_category_kpi_trade_mi` (
  `month` STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
  `category` STRING COMMENT '品类:取值范围为鲜果、乳制品、其他',
  `order_origin_total_amt` DECIMAL(38,18) COMMENT '交易应付总金额（元）',
  `order_real_total_amt` DECIMAL(38,18) COMMENT '交易实付总金额（元）',
  `order_cust_cnt` BIGINT COMMENT '交易客户数（个）',
  `order_cust_arpu` DECIMAL(38,18) COMMENT 'ARPU值（应付总金额/客户数），单位：元/客户',
  `order_cnt` BIGINT COMMENT '交易订单数（单）',
  `delivery_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额（元）',
  `delivery_real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额（元）',
  `delivery_cust_cnt` BIGINT COMMENT '履约活跃客户数（个）',
  `delivery_origin_profit` DECIMAL(38,18) COMMENT '履约应付毛利润（元）',
  `delivery_real_profit` DECIMAL(38,18) COMMENT '履约实付毛利润（元）',
  `delivery_after_profit` DECIMAL(38,18) COMMENT '履约后毛利润（元）',
  `delivery_days_avg` DECIMAL(38,18) COMMENT '履约频次（次/天）',
  `delivery_point_cnt` BIGINT COMMENT '履约点位数（个）',
  `delivery_amt` DECIMAL(38,18) COMMENT '自营履约费用（元）',
  `new_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '新客履约应付总金额（元）',
  `new_delivery_real_total_amt` DECIMAL(38,18) COMMENT '新客履约实付总金额（元）',
  `new_delivery_cust_cnt` BIGINT COMMENT '新客履约活跃客户数（个）',
  `new_delivery_real_profit` DECIMAL(38,18) COMMENT '新客履约实付毛利润（元）',
  `old_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '老客履约应付总金额（元）',
  `old_delivery_real_total_amt` DECIMAL(38,18) COMMENT '老客履约实付总金额（元）',
  `old_delivery_cust_cnt` BIGINT COMMENT '老客履约活跃客户数（个）',
  `old_delivery_real_profit` DECIMAL(38,18) COMMENT '老客履约实付毛利润（元）',
  `order_sku_cnt` BIGINT COMMENT '交易SKU款数（款）',
  `order_sku_weight` DECIMAL(38,18) COMMENT '交易SKU重量（KG）',
  `delivery_sku_cnt` BIGINT COMMENT '履约SKU款数（款）',
  `delivery_sku_weight` DECIMAL(38,18) COMMENT '履约SKU重量（KG）'
) 
COMMENT '销售KPI指标汇总表，包含交易和履约相关的关键绩效指标数据，按月份和品类维度统计'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC  
TBLPROPERTIES (
  'comment' = '销售KPI指标汇总表',
  'last_data_modified_time' = '2025-09-18 03:47:38'
) 
LIFECYCLE 30;
```