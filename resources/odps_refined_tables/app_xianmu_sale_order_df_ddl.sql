CREATE TABLE IF NOT EXISTS app_xianmu_sale_order_df(
	order_no STRING COMMENT '订单号',
	order_time DATETIME COMMENT '下单时间，格式：年月日时分秒',
	account_model STRING COMMENT '订单类型，枚举值：现结',
	contact STRING COMMENT '联系人',
	delivery_time DATETIME COMMENT '配送时间，格式：年月日时分秒',
	address STRING COMMENT '配送地址',
	order_account STRING COMMENT '下单账号',
	bill_status STRING COMMENT '开票状态，枚举值：未开票',
	order_source STRING COMMENT '订单来源，枚举值：销转采',
	customer_name STRING COMMENT '客户名称',
	customer_type STRING COMMENT '客户类型，枚举值：普通客户',
	payment_time DATETIME COMMENT '支付时间，格式：年月日时分秒',
	contact_mode STRING COMMENT '联系方式',
	sale_manager STRING COMMENT '销售经理',
	order_status STRING COMMENT '订单出库状态，枚举值：已出库',
	store_no BIGINT COMMENT '城配仓',
	task_created STRING COMMENT '是否生成出库任务，枚举值：是',
	delivery_status STRING COMMENT '订单配送状态，枚举值：已完成',
	sale_entity STRING COMMENT '销售主体',
	stock_task_type BIGINT COMMENT '出库任务类型：51-销售出库，52-样品出库，57-补发出库，58-销售自提出库，62-样品字体出库，63-越库出库',
	stock_task_id BIGINT COMMENT '出库任务id',
	warehouse_no BIGINT COMMENT '仓库编号',
	order_total_price DECIMAL(38,18) COMMENT '订单商品总价',
	delivery_fee DECIMAL(38,18) COMMENT '配送费',
	delivery_exact DECIMAL(38,18) COMMENT '精准送',
	order_overtime DECIMAL(38,18) COMMENT '超时加单',
	special_price DECIMAL(38,18) COMMENT '特价优惠',
	ladder_price DECIMAL(38,18) COMMENT '阶梯价优惠',
	change_price DECIMAL(38,18) COMMENT '换购优惠',
	extend_price DECIMAL(38,18) COMMENT '扩展购买优惠',
	full_reduce DECIMAL(38,18) COMMENT '满减',
	equity_card DECIMAL(38,18) COMMENT '权益卡',
	coupon DECIMAL(38,18) COMMENT '优惠券',
	red DECIMAL(38,18) COMMENT '红包',
	delivery_fee_coupon DECIMAL(38,18) COMMENT '运费券',
	delivery_exact_coupon DECIMAL(38,18) COMMENT '精准送优惠券',
	advance DECIMAL(38,18) COMMENT '预付商品'
) 
COMMENT '鲜沐销售主体销售订单表，包含销售订单的完整信息，包括订单基本信息、客户信息、支付信息、配送信息、优惠信息等'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='鲜沐销售主体销售订单表，用于存储销售订单的完整数据') 
LIFECYCLE 30;