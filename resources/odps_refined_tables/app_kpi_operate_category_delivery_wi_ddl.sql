CREATE TABLE IF NOT EXISTS app_kpi_operate_category_delivery_wi(
	`year` STRING COMMENT '年份，格式：YYYY',
	`week_of_year` STRING COMMENT '周数，格式：1-53',
	`monday` STRING COMMENT '周一日期，格式：YYYYMMDD',
	`sunday` STRING COMMENT '周日日期，格式：YYYYMMDD',
	`category` STRING COMMENT '商品品类，取值范围：鲜果、乳制品、其他',
	`origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV（应付款项总金额）',
	`real_total_amt` DECIMAL(38,18) COMMENT '履约实付GMV（实际付款总金额）',
	`marketing_amt` DECIMAL(38,18) COMMENT '营销费用',
	`cost_amt` DECIMAL(38,18) COMMENT '成本金额',
	`origin_gross` DECIMAL(38,18) COMMENT '应付毛利润',
	`real_gross` DECIMAL(38,18) COMMENT '实付毛利润',
	`origin_gross_margin` DECIMAL(38,18) COMMENT '应付毛利率',
	`real_gross_margin` DECIMAL(38,18) COMMENT '实付毛利率',
	`cust_cnt` BIGINT COMMENT '履约客户数',
	`point_cnt` BIGINT COMMENT '点位数',
	`origin_pre_cust_price` DECIMAL(38,18) COMMENT '应付客单价',
	`real_pre_cust_price` DECIMAL(38,18) COMMENT '实付客单价',
	`timing_origin_amt` DECIMAL(38,18) COMMENT '省心送应付GMV',
	`timing_real_amt` DECIMAL(38,18) COMMENT '省心送实付GMV',
	`consign_origin_amt` DECIMAL(38,18) COMMENT '代售应付GMV',
	`consign_real_amt` DECIMAL(38,18) COMMENT '代售实付GMV',
	`consign_marketing_amt` DECIMAL(38,18) COMMENT '代售营销费用',
	`consign_origin_gross` DECIMAL(38,18) COMMENT '代售应付毛利润',
	`consign_real_gross` DECIMAL(38,18) COMMENT '代售实付毛利润',
	`consign_cust_cnt` BIGINT COMMENT '代售履约客户数',
	`turnover_day_cnt` DECIMAL(38,18) COMMENT '周转天数',
	`damage_amt` DECIMAL(38,18) COMMENT '货损金额',
	`storage_amt` DECIMAL(38,18) COMMENT '仓储费',
	`arterial_roads_amt` DECIMAL(38,18) COMMENT '单点干线费',
	`self_picked_amt` DECIMAL(38,18) COMMENT '采购自提费',
	`allocation_amt` DECIMAL(38,18) COMMENT '调拨费',
	`other_amt` DECIMAL(38,18) COMMENT '其他费用',
	`deliver_amt` DECIMAL(38,18) COMMENT '配送费'
) 
COMMENT '运营履约KPI表（平台客户），包含商品品类维度的财务和运营指标数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='运营履约KPI表（平台客户），按周和商品品类统计的财务运营指标') 
LIFECYCLE 30;