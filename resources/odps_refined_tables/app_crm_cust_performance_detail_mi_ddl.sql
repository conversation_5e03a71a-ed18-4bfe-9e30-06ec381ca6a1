```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_crm_cust_performance_detail_mi` (
  `month` STRING COMMENT '月份，格式：yyyyMM',
  `cust_id` BIGINT COMMENT '商家编号，唯一标识一个商家',
  `cust_name` STRING COMMENT '商家名称',
  `cust_type` STRING COMMENT '商家类型：枚举值-拉新，复活，其他',
  `is_single` STRING COMMENT '是否单店：枚举值-是，否',
  `administrative_city` STRING COMMENT '商家对应的行政城市名称',
  `bd_id` BIGINT COMMENT '商家对应BD编号，-1表示无BD',
  `bd_name` STRING COMMENT '商家对应BD姓名',
  `zone_name` STRING COMMENT '区域名称',
  `m1` STRING COMMENT '城市负责人（M1）姓名',
  `m2` STRING COMMENT '区域负责人（M2）姓名',
  `m3` STRING COMMENT '部门负责人（M3）姓名',
  `first_order_real_amt` DECIMAL(38,18) COMMENT '首日下单实付金额（元）',
  `first_order_fruit_real_amt` DECIMAL(38,18) COMMENT '首日下单鲜果实付金额（元）',
  `total_real_amt` DECIMAL(38,18) COMMENT '月累计实付总金额（元）',
  `fruit_real_amt` DECIMAL(38,18) COMMENT '月累计鲜果实付金额（元）',
  `dairy_real_amt` DECIMAL(38,18) COMMENT '月累计乳制品实付金额（元），剔除安佳铁塔',
  `reward_real_amt` DECIMAL(38,18) COMMENT '月累计安佳铁塔实付金额（元）',
  `non_dairy_real_amt` DECIMAL(38,18) COMMENT '月累计非乳制品实付金额（元）',
  `brand_real_amt` DECIMAL(38,18) COMMENT '月累计自营品牌实付金额（元）',
  `consign_real_amt` DECIMAL(38,18) COMMENT '月累计代售实付金额（元）',
  `first_order_date` STRING COMMENT '首日下单日期，格式：yyyyMMdd',
  `first_order_dairy_real_amt` DECIMAL(38,18) COMMENT '首日下单乳制品实付金额（元），剔除安佳铁塔',
  `first_order_non_dairy_real_amt` DECIMAL(38,18) COMMENT '首日下单非乳制品实付金额（元）',
  `first_order_brand_real_amt` DECIMAL(38,18) COMMENT '首日下单自营品牌实付金额（元）',
  `first_order_reward_real_amt` DECIMAL(38,18) COMMENT '首日下单安佳铁塔实付金额（元）',
  `first_order_consign_real_amt` DECIMAL(38,18) COMMENT '首日下单代售实付金额（元）',
  `order_cnt` BIGINT COMMENT '月累计下单次数，范围：1-125',
  `visit_in_cnt` BIGINT COMMENT '月累计上门拜访次数（普通上门拜访+有效拜访），范围：0-3',
  `cust_type_2` STRING COMMENT '客户类型；枚举值：面包蛋糕,西餐,咖啡,甜品冰淇淋,茶饮,其他',
  `last_follow_online_time` DATETIME COMMENT '当月最近一次线上拜访时间（电话和微信），格式：年月日时分秒',
  `last_follow_offline_time` DATETIME COMMENT '当月最近一次线下拜访时间，格式：年月日时分秒',
  `last_login_time` DATETIME COMMENT '当月最近一次登陆商城时间，格式：年月日时分秒',
  `login_days` BIGINT COMMENT '月累计登陆商城天数，范围：0-17',
  `spu_cnt` BIGINT COMMENT '月累计下单SPU数，范围：1-92',
  `order_date_cnt` BIGINT COMMENT '月累计下单天数，范围：1-17',
  `fruit_order_date_cnt` BIGINT COMMENT '鲜果月累计下单天数，范围：0-17',
  `last_month_date` STRING COMMENT '上一次下单日期（与本月首单日期比），格式：yyyyMMdd',
  `order_date_diff` BIGINT COMMENT '距离上一次下单天数，范围：1-2672',
  `fruit_last_month_date` STRING COMMENT '鲜果上一次下单日期（与本月首单日期比），格式：yyyyMMdd',
  `fruit_order_date_diff` BIGINT COMMENT '鲜果距离上一次鲜果下单天数，范围：1-2672'
)
COMMENT '客户粒度平台销售业绩报表月汇总表，按商家维度统计月度销售业绩和拜访行为数据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '客户粒度平台销售业绩报表月汇总表',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```