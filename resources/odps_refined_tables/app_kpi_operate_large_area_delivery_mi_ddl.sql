CREATE TABLE IF NOT EXISTS app_kpi_operate_large_area_delivery_mi(
    `month` STRING COMMENT '月份，格式：yyyyMM',
    `large_area_name` STRING COMMENT '运营服务大区名称',
    `origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV（商品总价值）',
    `real_total_amt` DECIMAL(38,18) COMMENT '履约实付GMV（实际支付金额）',
    `marketing_amt` DECIMAL(38,18) COMMENT '营销费用',
    `cost_amt` DECIMAL(38,18) COMMENT '成本金额',
    `origin_gross` DECIMAL(38,18) COMMENT '应付毛利润',
    `real_gross` DECIMAL(38,18) COMMENT '实付毛利润',
    `origin_gross_margin` DECIMAL(38,18) COMMENT '应付毛利率（应付毛利润/应付GMV）',
    `real_gross_margin` DECIMAL(38,18) COMMENT '实付毛利率（实付毛利润/实付GMV）',
    `cust_cnt` BIGINT COMMENT '履约客户数，取值范围：2-9598',
    `point_cnt` BIGINT COMMENT '点位数，取值范围：2-28131',
    `origin_pre_cust_price` DECIMAL(38,18) COMMENT '应付客单价（应付GMV/客户数）',
    `real_pre_cust_price` DECIMAL(38,18) COMMENT '实付客单价（实付GMV/客户数）',
    `timing_origin_amt` DECIMAL(38,18) COMMENT '省心送应付GMV',
    `timing_real_amt` DECIMAL(38,18) COMMENT '省心送实付GMV',
    `consign_origin_amt` DECIMAL(38,18) COMMENT '代售应付GMV',
    `consign_real_amt` DECIMAL(38,18) COMMENT '代售实付GMV',
    `consign_marketing_amt` DECIMAL(38,18) COMMENT '代售营销费用',
    `consign_origin_gross` DECIMAL(38,18) COMMENT '代售应付毛利润',
    `consign_real_gross` DECIMAL(38,18) COMMENT '代售实付毛利润',
    `consign_cust_cnt` BIGINT COMMENT '代售履约客户数，取值范围：0',
    `storage_amt` DECIMAL(38,18) COMMENT '仓储费',
    `arterial_roads_amt` DECIMAL(38,18) COMMENT '单点干线费',
    `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提费',
    `allocation_amt` DECIMAL(38,18) COMMENT '调拨费',
    `other_amt` DECIMAL(38,18) COMMENT '其他费用',
    `deliver_amt` DECIMAL(38,18) COMMENT '配送费'
)
COMMENT '运营履约KPI表（平台客户），包含各大区的运营履约关键绩效指标数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd')
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
    'comment'='运营履约KPI统计表，按大区维度统计GMV、毛利率、客户数、点位数等核心业务指标')
LIFECYCLE 30;