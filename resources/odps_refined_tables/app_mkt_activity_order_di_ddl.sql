CREATE TABLE IF NOT EXISTS app_mkt_activity_order_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
	`activity_id` BIGINT COMMENT '活动ID，唯一标识一个营销活动',
	`activity_name` STRING COMMENT '活动名称，描述营销活动的具体内容',
	`start_time` DATETIME COMMENT '活动开始时间，格式为年月日时分秒（yyyy-MM-dd HH:mm:ss）',
	`end_time` DATETIME COMMENT '活动结束时间，格式为年月日时分秒（yyyy-MM-dd HH:mm:ss）',
	`activity_type` STRING COMMENT '活动类型，枚举值：特价活动、满减活动、折扣活动等',
	`activity_tag` STRING COMMENT '活动目的标签，枚举值：滞销促销、临保清仓、新品推广、常规促销等',
	`city_id` BIGINT COMMENT '运营服务区ID，标识城市级别的运营区域',
	`city_name` STRING COMMENT '运营服务区名称，对应城市ID的具体城市名称',
	`large_area_id` BIGINT COMMENT '运营服务大区ID，标识大区级别的运营区域',
	`large_area_name` STRING COMMENT '运营服务大区名称，对应大区ID的具体大区名称',
	`sku_id` STRING COMMENT '商品SKU ID，唯一标识具体商品规格',
	`spu_id` BIGINT COMMENT '商品SPU ID，标识商品品类',
	`spu_name` STRING COMMENT '商品名称，描述商品的基本信息',
	`sku_spec` STRING COMMENT '商品规格，描述商品的具体规格参数',
	`order_cnt` BIGINT COMMENT '订单数量，统计活动期间产生的订单总数',
	`cust_cnt` BIGINT COMMENT '客户数量，统计参与活动的独立客户数',
	`sku_cnt` BIGINT COMMENT 'SKU数量，统计活动涉及的商品SKU种类数',
	`activity_amt` DECIMAL(38,18) COMMENT '营销费用，活动投入的营销成本金额',
	`real_total_amt` DECIMAL(38,18) COMMENT '实付金额，客户实际支付的订单金额',
	`origin_total_amt` DECIMAL(38,18) COMMENT '应付金额，订单原始总金额（优惠前）'
) 
COMMENT '活动订单统计表，记录营销活动的订单相关数据统计，包括活动信息、商品信息、订单指标和费用信息'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期分区') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='活动订单统计表，用于分析营销活动效果和订单表现') 
LIFECYCLE 30;