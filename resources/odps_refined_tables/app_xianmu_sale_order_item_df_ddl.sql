```sql
CREATE TABLE IF NOT EXISTS app_xianmu_sale_order_item_df(
    order_no STRING COMMENT '订单编号，唯一标识一个销售订单',
    sku_id STRING COMMENT 'SKU编码，商品最小库存单位的唯一标识',
    spu_name STRING COMMENT '商品名称，标准产品单元的名称',
    weight DECIMAL(38,18) COMMENT '商品重量规格，单位：千克，精确到小数点后18位',
    sub_type BIGINT COMMENT '商品性质分类：3-普通商品（根据数据样本推断，目前仅观察到值为3）',
    need_pay_price DECIMAL(38,18) COMMENT '应付单价，商品的标准售价，单位：元，精确到小数点后18位',
    actual_pay_price DECIMAL(38,18) COMMENT '实付单价，商品的实际成交价格，单位：元，精确到小数点后18位',
    sku_cnt BIGINT COMMENT '商品购买数量，整数类型',
    order_total_price DECIMAL(38,18) COMMENT '商品实付总价，计算公式：实付单价 × 购买数量，单位：元，精确到小数点后18位',
    refund_status STRING COMMENT '退款状态：None-无退款（根据数据样本推断，目前仅观察到值为None）',
    after_sale_count STRING COMMENT '售后申请次数：None-无售后记录（根据数据样本推断，目前仅观察到值为None）',
    delivery_batch STRING COMMENT '配送批次编号：None-无配送批次信息（根据数据样本推断，目前仅观察到值为None）',
    stock_task_type BIGINT COMMENT '出库任务类型：51-销售出库，52-样品出库，57-补发出库，58-销售自提出库，62-样品自提出库，63-越库出库'
)
COMMENT '销转采销售单明细表，记录销售转采购业务中的订单商品明细信息，包含商品信息、价格、数量、出库类型等关键业务数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据所属的业务日期')
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
               'comment'='销转采销售单明细表，用于销售转采购业务的订单商品级别数据分析和报表生成')
LIFECYCLE 30;
```