```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_city_trade_selfowened_wi` (
  `year` STRING COMMENT '年份，格式：YYYY',
  `week_of_year` BIGINT COMMENT '周数，取值范围：1-53',
  `monday` STRING COMMENT '周一日期，格式：YYYYMMDD，表示年月日',
  `sunday` STRING COMMENT '周日日期，格式：YYYYMMDD，表示年月日',
  `cause_type` STRING COMMENT '业务类型，枚举：鲜沐,SAAS',
  `register_province` STRING COMMENT '注册省份名称',
  `register_city` STRING COMMENT '注册城市名称',
  `register_area` STRING COMMENT '注册区域名称',
  `cust_type` STRING COMMENT '客户类型，枚举：面包蛋糕,西餐,咖啡,甜品冰淇淋,茶饮,其他',
  `brand_type` STRING COMMENT '大客户类型，枚举：大客户,普通,批发客户',
  `brand_name` STRING COMMENT '品牌名称',
  `category1` STRING COMMENT '一级类目名称',
  `category2_id` STRING COMMENT '二级类目ID',
  `category2` STRING COMMENT '二级类目名称',
  `category3_id` STRING COMMENT '三级类目ID',
  `category3` STRING COMMENT '三级类目名称',
  `category4_id` STRING COMMENT '四级类目ID',
  `category4` STRING COMMENT '四级类目名称',
  `origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额，单位：元',
  `real_total_amt` DECIMAL(38,18) COMMENT '实付总金额，单位：元',
  `cust_cnt` BIGINT COMMENT '客户数，取值范围：1-12',
  `new_cust_cnt` BIGINT COMMENT '（历史截止今天）本周新客户数，取值范围：0-3',
  `order_time_cnt` DECIMAL(38,18) COMMENT '下单时间间隔之和，单位：分钟',
  `order_time_avg` DECIMAL(38,18) COMMENT '平均下单时间间隔，单位：分钟'
) 
COMMENT '自营品牌城市整体交易数据周表，按周统计各城市自营品牌的交易数据，包括客户类型、品牌类型、类目层级等维度'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：YYYYMMDD，表示年月日'
)
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
               'comment'='自营品牌城市交易周度统计表，用于分析各城市自营品牌的交易趋势和客户行为') 
LIFECYCLE 30;
```