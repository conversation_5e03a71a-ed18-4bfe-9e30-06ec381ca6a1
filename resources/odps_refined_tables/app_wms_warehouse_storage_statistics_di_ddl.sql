```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_wms_warehouse_storage_statistics_di` (
  `settle_date` DATETIME COMMENT '结算日，格式为年月日时分秒',
  `warehouse_no` BIGINT COMMENT '仓编号（仅鲜沐仓），取值范围：1-155',
  `warehouse_name` STRING COMMENT '仓库名称',
  `refrigerated_pallet` DECIMAL(38,18) COMMENT '冷藏托盘数量（单位：托盘）',
  `frozen_pallet` DECIMAL(38,18) COMMENT '冷冻托盘数量（单位：托盘）',
  `ambient_pallet` DECIMAL(38,18) COMMENT '常温托盘数量（单位：托盘）',
  `outbound_quantity` BIGINT COMMENT '出库件数，取值范围：0-20962',
  `outbound_weight` DECIMAL(38,18) COMMENT '出库重量（单位：吨）',
  `inbound_weight` DECIMAL(38,18) COMMENT '入库重量（单位：吨）',
  `standard_product_temporary_quantity` BIGINT COMMENT '标品临保数量，取值范围：0-35',
  `fruit_handling_quantity` BIGINT COMMENT '水果搬运数量，取值范围：0-5493',
  `fresh_fruit_sorting_quantity` BIGINT COMMENT '鲜果分包数量，取值范围：0-10764',
  `standard_product_unpacking_quantity` BIGINT COMMENT '标品拆包件数，取值范围：0-1149'
)
COMMENT 'BMS仓储数据统计表，包含仓库存储、出入库、加工等运营数据'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment'='BMS仓储运营数据统计表，用于分析仓库存储、出入库、加工等运营指标',
  'columnar.nested.type'='true'
)
LIFECYCLE 30;
```