CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_cust_lifecycle_distribution_df` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
  `life_cycle_detail` STRING COMMENT '生命周期标签（细），取值范围：A1、A2、A3、B1、B2等客户生命周期阶段',
  `cust_cnt` BIGINT COMMENT '总客户数',
  `disbaled_private_cust_cnt` BIGINT COMMENT '锁定客户数',
  `private_cust_cnt` BIGINT COMMENT '分配客户数',
  `bd_cnt` BIGINT COMMENT '跟进bd数',
  `order_real_amt_30d` DECIMAL(38,18) COMMENT '最近30天下单实付金额',
  `order_real_amt_60d` DECIMAL(38,18) COMMENT '最近60天下单实付金额',
  `order_real_amt_365d` DECIMAL(38,18) COMMENT '最近365天下单实付金额',
  `bd_order_real_amt_30d` DECIMAL(38,18) COMMENT '最近30天分配bd客户下单实付金额',
  `bd_order_real_amt_60d` DECIMAL(38,18) COMMENT '最近60天分配bd客户下单实付金额',
  `bd_order_real_amt_365d` DECIMAL(38,18) COMMENT '最近365天分配bd客户下单实付金额',
  `login_cust_cnt` BIGINT COMMENT '近30日登录总客户数',
  `login_disbaled_private_cust_cnt` BIGINT COMMENT '近30日登录锁定客户数',
  `login_private_cust_cnt` BIGINT COMMENT '近30日登录分配客户数',
  `login_bd_cnt` BIGINT COMMENT '近30日登录跟进bd数',
  `login_order_real_amt_30d` DECIMAL(38,18) COMMENT '近30日登录最近30天下单实付金额',
  `login_order_real_amt_60d` DECIMAL(38,18) COMMENT '近30日登录最近60天下单实付金额',
  `login_order_real_amt_365d` DECIMAL(38,18) COMMENT '近30日登录最近365天下单实付金额',
  `login_bd_order_real_amt_30d` DECIMAL(38,18) COMMENT '近30日登录最近30天分配bd客户下单实付金额',
  `login_bd_order_real_amt_60d` DECIMAL(38,18) COMMENT '近30日登录最近60天分配bd客户下单实付金额',
  `login_bd_order_real_amt_365d` DECIMAL(38,18) COMMENT '近30日登录最近365天分配bd客户下单实付金额',
  `login_value_cust_cnt` BIGINT COMMENT '近30日登录价值高总客户数',
  `login_value_disbaled_private_cust_cnt` BIGINT COMMENT '近30日登录价值高锁定客户数',
  `login_value_private_cust_cnt` BIGINT COMMENT '近30日登录价值高分配客户数',
  `login_value_bd_cnt` BIGINT COMMENT '近30日登录价值高跟进bd数',
  `login_value_order_real_amt_30d` DECIMAL(38,18) COMMENT '近30日登录价值高最近30天下单实付金额',
  `login_value_order_real_amt_60d` DECIMAL(38,18) COMMENT '近30日登录价值高最近60天下单实付金额',
  `login_value_order_real_amt_365d` DECIMAL(38,18) COMMENT '近30日登录价值高最近365天下单实付金额',
  `login_value_bd_order_real_amt_30d` DECIMAL(38,18) COMMENT '近30日登录价值高最近30天分配bd客户下单实付金额',
  `login_value_bd_order_real_amt_60d` DECIMAL(38,18) COMMENT '近30日登录价值高最近60天分配bd客户下单实付金额',
  `login_value_bd_order_real_amt_365d` DECIMAL(38,18) COMMENT '近30日登录价值高最近365天分配bd客户下单实付金额'
)
COMMENT '客户分配指标汇总表，按日期和客户生命周期维度统计客户分配、登录、下单金额等核心指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据加载日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '客户分配指标汇总表，包含客户生命周期各阶段的分配、登录、下单金额等核心业务指标',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;