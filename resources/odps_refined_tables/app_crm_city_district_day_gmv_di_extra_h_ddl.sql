CREATE TABLE IF NOT EXISTS app_crm_city_district_day_gmv_di_extra_h(
    city STRING COMMENT '行政城市名称，如：上海、北京等',
    district STRING COMMENT '行政区名称，如：徐汇区、长宁区等',
    day_tag STRING COMMENT '数据所在日标记，格式为yyyyMMdd，表示年月日',
    pull_new_amount BIGINT COMMENT '拉新客户数量，取值范围：0-19',
    ordinary_pull_new_amount BIGINT COMMENT '普通拉新客户数量，当前数据均为0',
    core_merchant_amount BIGINT COMMENT '核心客户数量，当前数据均为0',
    month_live_amount BIGINT COMMENT '月活跃客户总数，取值范围：0-544',
    open_merchant_month_live BIGINT COMMENT '公海客户月活跃数量，取值范围：0-7',
    private_merchant_month_live BIGINT COMMENT '私海客户月活跃数量，取值范围：0-539',
    open_merchant_effective_month_live BIGINT COMMENT '公海有效月活跃客户数量，取值范围：0-7',
    private_merchant_effective_month_live BIGINT COMMENT '私海有效月活跃客户数量，取值范围：0-503',
    open_merchant_amount BIGINT COMMENT '公海客户总数，取值范围：0-5182',
    private_merchant_amount BIGINT COMMENT '私海客户总数，取值范围：0-985',
    operate_merchant_num BIGINT COMMENT '倒闭客户数量，取值范围：0-164',
    visit_num BIGINT COMMENT '拜访次数，取值范围：0-355',
    escort_num BIGINT COMMENT '陪访次数，取值范围：0-31',
    spu_average DECIMAL(38,18) COMMENT 'SPU均值，商品标准化单元平均值',
    total_gmv DECIMAL(38,18) COMMENT '总GMV（商品交易总额）',
    saas_total_gmv_amt DECIMAL(38,18) COMMENT 'SaaS商家GMV总额',
    saas_total_cust_cnt BIGINT COMMENT 'SaaS月活跃客户数量，取值范围：0-36',
    brand_gmv DECIMAL(38,18) COMMENT '自营品牌GMV总额',
    brand_cust_cnt BIGINT COMMENT '自营品牌下单客户数量，取值范围：0-162',
    fruit_gmv DECIMAL(38,18) COMMENT '鲜果品类GMV总额',
    fruit_cust_cnt BIGINT COMMENT '鲜果品类下单客户数量，取值范围：0-371',
    dairy_gmv DECIMAL(38,18) COMMENT '乳制品品类GMV总额',
    dairy_cust_cnt BIGINT COMMENT '乳制品品类下单客户数量，取值范围：0-190',
    non_dairy_gmv DECIMAL(38,18) COMMENT '非乳制品品类GMV总额',
    non_dairy_cust_cnt BIGINT COMMENT '非乳制品品类下单客户数量，取值范围：0-247',
    agent_goods_gmv DECIMAL(38,18) COMMENT '代售品GMV总额'
)
COMMENT '行政城市+区本月GMV统计表，包含各区域客户活跃度、交易额等核心业务指标'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日')
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
    'comment'='按行政城市和区域统计的日度GMV数据表，包含拉新、活跃客户、交易额等多维度业务指标')
LIFECYCLE 30;