```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_cust_label_df`(
    `cust_id` BIGINT COMMENT '客户ID，唯一标识客户',
    `cust_name` STRING COMMENT '客户名称',
    `register_date` BIGINT COMMENT '注册日期，格式：yyyyMMdd，如20160909表示2016年9月9日',
    `review_status` BIGINT COMMENT '审核状态：0-审核通过，1-审核中，2-审核未通过，3-账号被拉黑，4-注销',
    `cust_group` STRING COMMENT '客户分组类型：大客户、平台客户、批发客户',
    `cust_type` STRING COMMENT '客户类型：面包蛋糕、西餐、咖啡、甜品冰淇淋、茶饮、其他',
    `register_province` STRING COMMENT '注册时省份',
    `register_city` STRING COMMENT '注册时城市',
    `register_area` STRING COMMENT '注册时区县',
    `large_area_name` STRING COMMENT '运营大区名称',
    `load_day_cnt` BIGINT COMMENT '近30天登录天数，范围：0-30',
    `nearly_order_time` DATETIME COMMENT '最近一次下单日期，格式：年月日时分秒',
    `order_day_cnt` BIGINT COMMENT '近30天下单天数，范围：0-17',
    `origin_total_amt` DECIMAL(38,18) COMMENT '近30天交易应付金额',
    `real_total_amt` DECIMAL(38,18) COMMENT '近30天交易实付金额',
    `discount_order_cnt` BIGINT COMMENT '近30天优惠订单数，范围：0-43',
    `after_sale_order_cnt` BIGINT COMMENT '近30天售后订单数，范围：0-12',
    `after_sale_amt` DECIMAL(38,18) COMMENT '近30天已到货售后金额',
    `fruit_origin_total_amt` DECIMAL(38,18) COMMENT '鲜果近30天交易应付金额',
    `dairy_origin_total_amt` DECIMAL(38,18) COMMENT '乳制品近30天交易应付金额',
    `other_origin_total_amt` DECIMAL(38,18) COMMENT '其他品近30天交易应付金额',
    `fruit_order_day_cnt` BIGINT COMMENT '鲜果近30天交易频次（天数），范围：0-16',
    `dairy_order_day_cnt` BIGINT COMMENT '乳制品近30天交易频次（天数），范围：0-12',
    `other_order_day_cnt` BIGINT COMMENT '其他品近30天交易频次（天数），范围：0-16',
    `nearly_delivary_time` STRING COMMENT '最近一次履约日期，格式：yyyy-MM-dd HH:mm:ss',
    `delivery_day_cnt` BIGINT COMMENT '近30天履约天数，范围：0-29',
    `delivery_order_cnt` BIGINT COMMENT '近30天履约订单数，范围：0-80',
    `dlv_origin_total_amt` DECIMAL(38,18) COMMENT '近30天履约应付金额',
    `dlv_real_total_amt` DECIMAL(38,18) COMMENT '近30天履约实付金额',
    `fruit_dlv_origin_total_amt` DECIMAL(38,18) COMMENT '鲜果近30天履约应付金额',
    `dairy_dlv_origin_total_amt` DECIMAL(38,18) COMMENT '乳制品近30天履约应付金额',
    `other_dlv_origin_total_amt` DECIMAL(38,18) COMMENT '其他品近30天履约应付金额',
    `fruit_dlv_day_cnt` BIGINT COMMENT '鲜果近30天履约频次（天数），范围：0-28',
    `dairy_dlv_day_cnt` BIGINT COMMENT '乳制品近30天履约频次（天数），范围：0-24',
    `other_dlv_day_cnt` BIGINT COMMENT '其他品近30天履约频次（天数），范围：0-29',
    `is_last_cust` DECIMAL(38,18) COMMENT '是否复购客户：null-未知，其他值表示复购概率',
    `cust_label` STRING COMMENT '新/老客标识：新客、老客',
    `lost_label` STRING COMMENT '是否流失客户：是、否',
    `spu_name_list` STRING COMMENT '近30天履约的商品GMVTOP3，商品名称列表'
)
COMMENT '客户标签表，包含客户基本信息、交易行为、履约行为等标签数据，用于客户分析和画像'
PARTITIONED BY (
    `ds` STRING NOT NULL COMMENT '分区字段，格式：yyyyMMdd，如20250917表示2025年9月17日'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment'='客户标签分析表',
    'columnar.nested.type'='true'
)
LIFECYCLE 30;
```