```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_area_deliver_1di` (
  `area_id` BIGINT COMMENT '仓库编号，唯一标识一个配送仓库，取值范围：1-144',
  `area_name` STRING COMMENT '仓库名称，如：杭州仓、上海仓、宁波仓、苏州仓等',
  `car_cnt` BIGINT COMMENT '车次数，配送车辆出车次数，取值范围：0-25',
  `short_car_cnt` BIGINT COMMENT '缺货车次数，因缺货导致无法完成配送的车次数量，取值范围：0-2',
  `point_cnt` BIGINT COMMENT '点位数，需要配送的总点位数量，取值范围：20-485',
  `point_cnt_b14h` BIGINT COMMENT '14点前完成点位数，在下午2点前完成的配送点位数量，取值范围：0-485',
  `short_point_cnt` BIGINT COMMENT '缺货点位数，因缺货无法完成配送的点位数量，取值范围：0-9',
  `order_cnt` BIGINT COMMENT '订单数，总订单数量，取值范围：20-603',
  `short_order_cnt` BIGINT COMMENT '缺货订单数，因缺货无法完成的订单数量，取值范围：0-9',
  `cust_cnt` BIGINT COMMENT '客户数，服务客户总数，取值范围：20-451',
  `short_cust_cnt` BIGINT COMMENT '缺货客户数，因缺货影响到的客户数量，取值范围：0-9',
  `sku_cnt` BIGINT COMMENT 'SKU数，商品品类总数，取值范围：90-3583',
  `short_sku_cnt` BIGINT COMMENT '缺货SKU数，缺货的商品品类数量，取值范围：0-409',
  `real_total_amt` DECIMAL(38,18) COMMENT '实际总价，实际成交金额（元），保留18位小数精度',
  `short_real_total_amt` DECIMAL(38,18) COMMENT '缺货实际总价，因缺货导致的实际金额损失（元），保留18位小数精度',
  `origin_total_amt` DECIMAL(38,18) COMMENT '原始总价，订单原始金额（元），保留18位小数精度',
  `short_origin_total_amt` DECIMAL(38,18) COMMENT '缺货原始总价，因缺货导致的原始金额损失（元），保留18位小数精度',
  `point_in_time` BIGINT COMMENT '及时完成点位数，按时完成的配送点位数量，取值范围：20-485'
) 
COMMENT '配送仓库维度按天汇总表，统计各配送仓库每日的配送指标数据，包括车次、点位、订单、客户、SKU等维度的完成情况和缺货情况'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，数据日期，格式：yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment'='配送仓库维度按天汇总表，用于分析各仓库配送效率和缺货情况',
  'lifecycle'='30'
);
```