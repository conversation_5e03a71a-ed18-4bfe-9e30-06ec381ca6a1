CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_kpi_category_wholesale_mi` (
  `month` STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
  `sku_type` STRING COMMENT '商品类型; 取值范围：自营/代仓',
  `category` STRING COMMENT '品类; 取值范围：鲜果/乳制品/其他',
  `order_gmv_amt` DECIMAL(38,18) COMMENT '交易应付总金额，单位：元',
  `deliver_gmv_amt` DECIMAL(38,18) COMMENT '履约应付总金额，单位：元',
  `deliver_cust_cnt` BIGINT COMMENT '履约客户数，去重后的客户数量',
  `deliver_cust_arpu` DECIMAL(38,18) COMMENT '履约ARPU(应付总金额/客户数)，单位：元/客户',
  `deliver_order_cnt` BIGINT COMMENT '履约订单数，去重后的订单数量',
  `deliver_order_avg` DECIMAL(38,18) COMMENT '履约订单均价(应付总金额/订单数)，单位：元/订单',
  `deliver_cost_amt` DECIMAL(38,18) COMMENT '履约总成本，单位：元',
  `deliver_gross_profit` DECIMAL(38,18) COMMENT '履约毛利润(应付总金额-总成本)，单位：元',
  `deliver_gross_profit_rate` DECIMAL(38,18) COMMENT '履约毛利率(毛利润/应付总金额)，小数形式表示百分比'
)
COMMENT '交易口径KPI指标日汇总表，按商品类型和品类维度统计的每日交易和履约相关指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期，如20250917表示2025年9月17日'
)
STORED AS ALIORC  
TBLPROPERTIES ('comment'='交易口径KPI指标日汇总表，包含按商品类型和品类维度统计的交易金额、履约金额、客户数、订单数、成本、利润等核心业务指标')
LIFECYCLE 30;