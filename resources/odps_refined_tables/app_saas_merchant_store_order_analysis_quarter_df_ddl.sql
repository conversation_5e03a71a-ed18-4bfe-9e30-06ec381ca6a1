CREATE TABLE IF NOT EXISTS app_saas_merchant_store_order_analysis_quarter_df(
	tenant_id BIGINT COMMENT '租户ID，取值范围：2-64',
	time_tag STRING COMMENT '时间标签，格式：yyyyMMdd，表示季度统计的时间标识',
	store_id BIGINT COMMENT '门店ID，取值范围：1-396432',
	average_order_period DECIMAL(38,18) COMMENT '平均订货周期（天）',
	average_order_period_last_period DECIMAL(38,18) COMMENT '上周期平均订货周期（天）',
	average_order_period_upper_period DECIMAL(38,18) COMMENT '平均订货周期环比变化率',
	order_amount BIGINT COMMENT '订货数量，取值范围：1-86643',
	order_amount_last_period BIGINT COMMENT '上周期订货数量，取值范围：0-86643',
	order_amount_upper_period DECIMAL(38,18) COMMENT '订货数量环比变化率',
	order_price DECIMAL(38,18) COMMENT '订货金额（元）',
	order_price_last_period DECIMAL(38,18) COMMENT '上周期订货金额（元）',
	order_price_upper_period DECIMAL(38,18) COMMENT '订货金额环比变化率',
	last_order_time STRING COMMENT '最后订货日期，格式：yyyy-MM-dd，表示最近一次订货的具体日期',
	last_order_amount BIGINT COMMENT '最后订货数量，取值范围：1-12902',
	last_order_price DECIMAL(38,18) COMMENT '最后订货金额（元）'
)
COMMENT 'SaaS门店季度订货分析表，包含门店的订货周期、数量、金额等关键指标及其环比变化情况'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据统计日期')
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='SaaS门店季度订货分析结果表，用于分析门店订货行为和趋势变化')
LIFECYCLE 30;