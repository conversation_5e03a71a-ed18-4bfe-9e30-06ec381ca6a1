CREATE TABLE IF NOT EXISTS app_finance_saas_revenue_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务发生日期',
	`service_area` STRING COMMENT '大区：华东、华南、华北等',
	`warehouse_no` BIGINT COMMENT '库存仓ID，取值范围：2-145',
	`warehouse_name` STRING COMMENT '库存仓名称',
	`cust_team` STRING COMMENT '客户团队类型：平台客户/大客户',
	`category1` STRING COMMENT '商品一级类目：鲜果/乳制品/其他',
	`cash_revenue_amt` DECIMAL(38,18) COMMENT '现结含税收入金额',
	`bill_revenue_amt` DECIMAL(38,18) COMMENT '账期含税收入金额',
	`cash_revenue_amt_notax` DECIMAL(38,18) COMMENT '现结不含税收入金额',
	`bill_revenue_amt_notax` DECIMAL(38,18) COMMENT '账期不含税收入金额',
	`revenue_amt` DECIMAL(38,18) COMMENT '含税总收入金额（现结+账期）',
	`revenue_profit_amt` DECIMAL(38,18) COMMENT '含税毛利润金额',
	`revenue_amt_notax` DECIMAL(38,18) COMMENT '不含税总收入金额（现结+账期）',
	`revenue_profit_amt_notax` DECIMAL(38,18) COMMENT '不含税毛利润金额'
) 
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据加载日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='SaaS财务口径收入数据表，包含按大区、仓库、客户团队、商品类目维度的收入和利润数据') 
LIFECYCLE 30;