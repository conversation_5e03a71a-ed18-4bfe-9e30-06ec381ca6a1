```sql
CREATE TABLE IF NOT EXISTS app_stc_warehouse_sku_turnover_di(
    warehouse_no BIGINT COMMENT '库存仓编号',
    warehouse_name STRING COMMENT '库存仓名称',
    sku_id STRING COMMENT 'SKU编号，商品最小库存单位标识',
    sku_type STRING COMMENT 'SKU类型：自营-平台自营商品，代仓-第三方仓库代发商品',
    spu_id BIGINT COMMENT 'SPU ID，商品标准单位标识',
    spu_no STRING COMMENT 'SPU编号，商品标准单位编码',
    spu_name STRING COMMENT '商品名称',
    sku_disc STRING COMMENT '商品描述，包含规格包装信息',
    category1 STRING COMMENT '一级类目，商品大类分类',
    category2 STRING COMMENT '二级类目，商品小类分类',
    sku_property STRING COMMENT 'SKU属性：常规-普通商品，活动-促销商品，临保-临近保质期商品，拆包-拆包商品，破袋-包装破损商品',
    sku_life STRING COMMENT 'SKU生命周期状态：上新处理中-新商品上架中，使用中-正常销售中，已删除-已下架商品',
    sku_core_type STRING COMMENT 'SKU核心类型：核心-重点商品，非核心-普通商品',
    sale_quantity BIGINT COMMENT '当日销量数量',
    sale_cost DECIMAL(38,18) COMMENT '当日销量成本',
    inventory_quantity BIGINT COMMENT '当日库存数量',
    inventory_cost DECIMAL(38,18) COMMENT '当日库存成本',
    nearest_7_days_sale_quantity BIGINT COMMENT '近7天销量数量',
    nearest_7_days_total_sale_cost DECIMAL(38,18) COMMENT '近7天总销量成本',
    nearest_7_days_inventory_quantity BIGINT COMMENT '近7天库存数量',
    nearest_7_days_total_inventory_cost DECIMAL(38,18) COMMENT '近7天总库存成本',
    nearest_30_days_sale_quantity BIGINT COMMENT '近30天销量数量',
    nearest_30_days_total_sale_cost DECIMAL(38,18) COMMENT '近30天总销量成本',
    nearest_30_days_inventory_quantity BIGINT COMMENT '近30天库存数量',
    nearest_30_days_total_inventory_cost DECIMAL(38,18) COMMENT '近30天总库存成本',
    month_sale_quality BIGINT COMMENT '当月销量数量',
    month_total_sale_cost DECIMAL(38,18) COMMENT '当月总销量成本',
    month_inventory_quantity BIGINT COMMENT '当月库存数量',
    month_total_inventory_cost DECIMAL(38,18) COMMENT '当月总库存成本',
    nearest_14_days_inventory_quantity BIGINT COMMENT '近14天库存数量',
    nearest_14_days_total_inventory_cost DECIMAL(38,18) COMMENT '近14天总库存成本',
    nearest_14_days_sale_quantity BIGINT COMMENT '近14天销量数量',
    nearest_14_days_total_sale_cost DECIMAL(38,18) COMMENT '近14天总销量成本'
)
COMMENT '库存仓+SKU近30天库存周转分析表，包含各时间维度的销量和库存成本数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期')
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = '库存周转分析表，用于监控和分析各仓库SKU的库存周转情况',
    'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```