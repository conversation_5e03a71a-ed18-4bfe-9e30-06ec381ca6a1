```sql
CREATE TABLE IF NOT EXISTS app_saas_purchases_inbound_detail_aggregate_offline_df(
    `inbound_time` DATETIME COMMENT '入库时间，格式：年月日时分秒',
    `batch_no` STRING COMMENT '采购批次编号',
    `inbound_stock` BIGINT COMMENT '实际入库数量',
    `inbound_price` DECIMAL(38,18) COMMENT '入库总金额，保留18位小数',
    `purchases_stock` BIGINT COMMENT '计划采购数量',
    `purchases_price` DECIMAL(38,18) COMMENT '计划采购金额，保留18位小数',
    `sku_no` STRING COMMENT '内部SKU编号',
    `sku_name` STRING COMMENT '内部SKU名称',
    `specification` STRING COMMENT '商品规格描述',
    `packaging` STRING COMMENT '包装单位（如：包、箱等）',
    `saas_sku_no` STRING COMMENT 'SaaS平台SKU编号',
    `saas_sku_name` STRING COMMENT 'SaaS平台SKU名称',
    `saas_specification` STRING COMMENT 'SaaS平台商品规格描述',
    `saas_packaging` STRING COMMENT 'SaaS平台包装单位',
    `inbound_create_user_id` BIGINT COMMENT '采购创建人ID',
    `inbound_create_user_name` STRING COMMENT '采购创建人姓名',
    `inbound_create_user_phone` STRING COMMENT '采购创建人电话',
    `warehouse_id` BIGINT COMMENT '仓库ID，取值范围：1-62',
    `warehouse_name` STRING COMMENT '仓库名称',
    `tenant_id` BIGINT COMMENT '租户ID，取值范围：2-12',
    `tenant_name` STRING COMMENT '租户名称',
    `supplier_id` BIGINT COMMENT '供应商ID，取值范围：1823-1927',
    `supplier_name` STRING COMMENT '供应商名称',
    `supplier_type` BIGINT COMMENT '供应商类型：0-普通供应商，1-其他类型，2-重要供应商'
)
COMMENT '采购入库单离线聚合表，包含采购入库的详细信息、SKU信息、仓库信息、租户信息和供应商信息'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：yyyyMMdd')
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = '采购入库单离线聚合表，用于存储采购入库相关的详细数据',
    'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```