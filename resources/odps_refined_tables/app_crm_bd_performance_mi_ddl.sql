CREATE TABLE IF NOT EXISTS app_crm_bd_performance_mi(
	month STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
	bd_id BIGINT COMMENT '销售ID，唯一标识一个销售人员',
	bd_name STRING COMMENT '销售姓名',
	administrative_city STRING COMMENT '销售所属行政城市',
	zone_name STRING COMMENT '区域名称',
	m1 STRING COMMENT '城市负责人（M1）姓名',
	m2 STRING COMMENT '区域负责人（M2）姓名',
	m3 STRING COMMENT '部门负责人（M3）姓名',
	total_gmv_amt DECIMAL(38,18) COMMENT '总GMV金额（含SAAS），单位：元',
	brand_gmv_amt DECIMAL(38,18) COMMENT '自营品牌GMV金额，单位：元',
	brand_cust_cnt BIGINT COMMENT '自营品牌下单客户数',
	fruit_gmv_amt DECIMAL(38,18) COMMENT '鲜果GMV金额，单位：元',
	fruit_cust_cnt BIGINT COMMENT '鲜果下单客户数',
	dairy_gmv_amt DECIMAL(38,18) COMMENT '乳制品GMV金额，单位：元',
	dairy_cust_cnt BIGINT COMMENT '乳制品下单客户数',
	non_dairy_gmv_amt DECIMAL(38,18) COMMENT '非乳制品GMV金额，单位：元',
	non_dairy_cust_cnt BIGINT COMMENT '非乳制品下单客户数',
	reward_gmv_amt DECIMAL(38,18) COMMENT '安佳铁塔GMV金额，单位：元',
	reward_cust_cnt BIGINT COMMENT '安佳铁塔下单客户数',
	timing_gmv_amt DECIMAL(38,18) COMMENT '省心送订单GMV金额，单位：元',
	timing_cust_cnt BIGINT COMMENT '省心送下单客户数',
	timing_reward_gmv_amt DECIMAL(38,18) COMMENT '安佳铁塔省心送订单GMV金额，单位：元',
	timing_reward_cust_cnt BIGINT COMMENT '安佳铁塔省心送下单客户数',
	core_cust_total_gmv_amt DECIMAL(38,18) COMMENT '核心客户GMV金额，单位：元',
	core_cust_cnt BIGINT COMMENT '核心客户数',
	total_cust_cnt BIGINT COMMENT '活跃客户数（含SAAS）',
	private_cust_cnt BIGINT COMMENT '私海活跃客户数',
	open_cust_cnt BIGINT COMMENT '公海活跃客户数',
	new_noactive_cust_cnt BIGINT COMMENT '拉新客户数（仅注册未下单）',
	new_active_cust_cnt BIGINT COMMENT '拉新客户数（注册且下单）',
	new_active_gmv_amt DECIMAL(38,18) COMMENT '拉新客户GMV金额，单位：元',
	ordinary_cnt BIGINT COMMENT '普通拜访次数',
	ordinary_cust_cnt BIGINT COMMENT '普通拜访客户数',
	drop_in_visit_cnt BIGINT COMMENT '普通上门拜访次数',
	drop_in_visit_cust_cnt BIGINT COMMENT '普通上门拜访客户数',
	efficient_cnt BIGINT COMMENT '有效拜访次数',
	efficient_cust_cnt BIGINT COMMENT '有效拜访客户数',
	worth_cnt BIGINT COMMENT '价值拜访次数',
	worth_cust_cnt BIGINT COMMENT '价值拜访客户数',
	deliver_gmv_amt DECIMAL(38,18) COMMENT '配送GMV金额，单位：元',
	deliver_point_cnt BIGINT COMMENT '配送点位数',
	deliver_gmv_avg DECIMAL(38,18) COMMENT '点位平均单价，单位：元',
	deliver_spu_cnt BIGINT COMMENT '配送SPU数（按点位累加）',
	deliver_spu_avg DECIMAL(38,18) COMMENT '平均点位购买SPU数',
	saas_total_gmv_amt DECIMAL(38,18) COMMENT 'SAAS总GMV金额，单位：元',
	saas_total_cust_cnt BIGINT COMMENT 'SAAS活跃客户数',
	normal_new_active_cust_cnt BIGINT COMMENT '普通拉新客户数（注册且首单<15元）',
	normal_new_active_gmv_amt DECIMAL(38,18) COMMENT '普通拉新客户GMV金额，单位：元',
	private_effect_cust_cnt BIGINT COMMENT '私海有效活跃客户数（实付金额>=20元）',
	private_normal_cust_cnt BIGINT COMMENT '私海普通活跃客户数（实付金额<20元）',
	open_effect_cust_cnt BIGINT COMMENT '公海有效活跃客户数（实付金额>=20元）',
	open_normal_cust_cnt BIGINT COMMENT '公海普通活跃客户数（实付金额<20元）',
	saas_brand_gmv_amt DECIMAL(38,18) COMMENT 'SAAS自营品牌订单实付金额，单位：元',
	saas_fruit_gmv_amt DECIMAL(38,18) COMMENT 'SAAS鲜果订单实付金额，单位：元',
	saas_dairy_gmv_amt DECIMAL(38,18) COMMENT 'SAAS乳制品订单实付金额（剔除N001S01R005、N001S01R002），单位：元',
	saas_non_dairy_gmv_amt DECIMAL(38,18) COMMENT 'SAAS非乳制品订单实付金额，单位：元',
	saas_reward_gmv_amt DECIMAL(38,18) COMMENT 'SAAS安佳铁塔订单实付金额（N001S01R005、N001S01R002），单位：元',
	saas_effect_cust_cnt BIGINT COMMENT 'SAAS有效活跃客户数（实付金额>=20元）'
) 
COMMENT 'BD粒度业绩报表月汇总表，按月份统计销售人员的各项业绩指标，包括GMV、客户数、拜访情况等'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，如20250917表示2025年9月17日') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='BD销售业绩月度汇总表，用于销售业绩分析和绩效考核') 
LIFECYCLE 30;