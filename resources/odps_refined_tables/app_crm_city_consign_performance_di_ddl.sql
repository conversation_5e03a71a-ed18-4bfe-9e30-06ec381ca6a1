CREATE TABLE IF NOT EXISTS app_crm_city_consign_performance_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务发生日期',
	`administrative_city` STRING COMMENT '销售所属行政城市名称',
	`zone_name` STRING COMMENT '区域名称，销售团队所属的业务区域',
	`m1` STRING COMMENT '城市负责人（M1），即销售主管',
	`m2` STRING COMMENT '区域负责人（M2），即销售经理',
	`m3` STRING COMMENT '部门负责人（M3），即销售总监',
	`cust_type` STRING COMMENT '客户类型；枚举值：面包蛋糕,西餐,咖啡,甜品冰淇淋,茶饮,其他',
	`order_cust_cnt` BIGINT COMMENT '下单客户数，统计周期内下单的独立客户数量',
	`order_sku_cnt` BIGINT COMMENT '销量，统计周期内销售的商品SKU总数',
	`order_cnt` BIGINT COMMENT '订单数，统计周期内产生的订单总数',
	`real_total_amt` DECIMAL(38,18) COMMENT '订单实付金额，统计周期内订单实际支付的总金额',
	`drop_in_visit_cust_cnt` BIGINT COMMENT '上门拜访客户数（上门/有效），统计周期内实际到访的客户数量',
	`visit_cust_cnt` BIGINT COMMENT '总拜访客户数，统计周期内所有拜访记录的客户数量'
) 
COMMENT '行政城市粒度代售业绩报表日汇总表，按行政城市维度统计每日销售业绩数据，包含客户拜访和订单相关指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据加载日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true') 
LIFECYCLE 30;