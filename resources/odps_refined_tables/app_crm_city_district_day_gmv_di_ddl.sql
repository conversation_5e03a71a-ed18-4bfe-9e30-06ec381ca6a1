```sql
CREATE TABLE IF NOT EXISTS app_crm_city_district_day_gmv_di(
    city STRING COMMENT '行政城市名称',
    district STRING COMMENT '行政区名称',
    day_tag STRING COMMENT '数据日期标记，格式为yyyyMMdd，表示数据所属的业务日期',
    pull_new_amount BIGINT COMMENT '拉新客户数量',
    ordinary_pull_new_amount BIGINT COMMENT '普通拉新客户数量',
    core_merchant_amount BIGINT COMMENT '核心客户数量',
    month_live_amount BIGINT COMMENT '月活跃客户总数',
    open_merchant_month_live BIGINT COMMENT '公海客户月活跃数量',
    private_merchant_month_live BIGINT COMMENT '私海客户月活跃数量',
    open_merchant_effective_month_live BIGINT COMMENT '公海有效月活跃客户数量',
    private_merchant_effective_month_live BIGINT COMMENT '私海有效月活跃客户数量',
    open_merchant_amount BIGINT COMMENT '公海客户总数',
    private_merchant_amount BIGINT COMMENT '私海客户总数',
    operate_merchant_num BIGINT COMMENT '倒闭客户数量',
    visit_num BIGINT COMMENT '拜访次数',
    escort_num BIGINT COMMENT '陪访次数',
    spu_average DECIMAL(38,18) COMMENT '平均SPU（标准化产品单位）值',
    total_gmv DECIMAL(38,18) COMMENT '总GMV（商品交易总额）',
    saas_total_gmv_amt DECIMAL(38,18) COMMENT 'SaaS商家GMV总额',
    saas_total_cust_cnt BIGINT COMMENT 'SaaS月活跃客户数量',
    brand_gmv DECIMAL(38,18) COMMENT '自营品牌GMV',
    brand_cust_cnt BIGINT COMMENT '自营品牌下单客户数量',
    fruit_gmv DECIMAL(38,18) COMMENT '鲜果品类GMV',
    fruit_cust_cnt BIGINT COMMENT '鲜果品类下单客户数量',
    dairy_gmv DECIMAL(38,18) COMMENT '乳制品品类GMV',
    dairy_cust_cnt BIGINT COMMENT '乳制品品类下单客户数量',
    non_dairy_gmv DECIMAL(38,18) COMMENT '非乳制品品类GMV',
    non_dairy_cust_cnt BIGINT COMMENT '非乳制品品类下单客户数量',
    agent_goods_gmv DECIMAL(38,18) COMMENT '代售品GMV'
)
COMMENT '行政城市和行政区维度的日度GMV统计表，包含客户活跃度、拜访情况及各品类销售数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据采集日期')
STORED AS ALIORC
TBLPROPERTIES (
    'comment'='城市区县日度GMV分析表，用于监控各地区销售表现和客户行为',
    'columnar.nested.type'='true'
)
LIFECYCLE 30;
```