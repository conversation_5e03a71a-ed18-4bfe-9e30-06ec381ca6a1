```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_kpi_cust_trade_wi` (
  `year` STRING COMMENT '年份，格式：yyyy',
  `week_of_year` BIGINT COMMENT '周数，取值范围：1-53',
  `monday` STRING COMMENT '周一日期，格式：yyyyMMdd（年月日）',
  `sunday` STRING COMMENT '周日日期，格式：yyyyMMdd（年月日）',
  `cust_class` STRING COMMENT '客户类型，枚举值：Mars大客户、平台客户、集团大客户（茶百道）、大客户（茶百道）、大客户（非茶百道）、批发、普通（非品牌）、普通（品牌）',
  `origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额，单位：元',
  `real_total_amt` DECIMAL(38,18) COMMENT '实付总金额，单位：元',
  `cust_cnt` BIGINT COMMENT '客户数，统计周期内下单的客户数量',
  `cust_arpu` DECIMAL(38,18) COMMENT 'ARPU值，计算公式：应付总金额/客户数，单位：元/客户',
  `order_cnt` BIGINT COMMENT '订单数，统计周期内产生的订单数量',
  `order_avg` DECIMAL(38,18) COMMENT '订单均价，计算公式：应付总金额/订单数，单位：元/订单',
  `after_sale_noreceived_amt` DECIMAL(38,18) COMMENT '未到货售后总金额，单位：元',
  `after_sale_rate` DECIMAL(38,18) COMMENT '退货率，计算公式：未到货售后总金额/应付总金额，取值范围：0-1',
  `dire_origin_total_amt` DECIMAL(38,18) COMMENT '直发采购应付总金额，单位：元',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费，包含运费和超时加单费，单位：元',
  `timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送应付总金额，单位：元'
) 
COMMENT '交易口径KPI指标周汇总表，按周统计各类客户的交易相关指标，包括金额、客户数、订单数、ARPU、退货率等核心业务指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：yyyyMMdd（年月日），表示数据统计日期'
)
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
               'comment'='交易口径KPI指标周度汇总表，用于业务分析和监控') 
LIFECYCLE 30;
```