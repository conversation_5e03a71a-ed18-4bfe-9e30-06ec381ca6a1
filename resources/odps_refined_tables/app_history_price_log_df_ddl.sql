CREATE TABLE IF NOT EXISTS app_history_price_log_df(
	sku STRING COMMENT '商品SKU编码，唯一标识一个商品',
	area_no BIGINT COMMENT '城市编号，取值范围：1001-44270',
	price DECIMAL(38,18) COMMENT '商品售价，单位：元',
	activity_price DECIMAL(38,18) COMMENT '商品活动价格，单位：元（None表示无活动价格）',
	date_key STRING COMMENT '业务日期，格式：yyyyMMdd，表示价格记录对应的业务日期'
)
COMMENT '进货单-商品历史价格表，记录商品在不同城市的历史价格信息，包括正常售价和活动价格'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据加载日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='进货单-商品历史价格表，用于记录和分析商品价格变化趋势') 
LIFECYCLE 30;