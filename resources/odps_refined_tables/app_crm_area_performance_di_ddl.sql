```sql
CREATE TABLE IF NOT EXISTS app_crm_area_performance_di(
    `date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务日期',
    `administrative_city` STRING COMMENT '销售所属行政城市，枚举值：上海市、深圳市等',
    `area` STRING COMMENT '区名称，如：金山区、奉贤区、松江区、长宁区、罗湖区等',
    `total_gmv_amt` DECIMAL(38,18) COMMENT '总GMV金额(含SAAS)，单位：元',
    `brand_gmv_amt` DECIMAL(38,18) COMMENT '自营品牌GMV金额，单位：元',
    `brand_cust_cnt` BIGINT COMMENT '自营品牌下单客户数',
    `core_cust_total_gmv_amt` DECIMAL(38,18) COMMENT '核心客户GMV金额，单位：元',
    `core_cust_cnt` BIGINT COMMENT '核心客户数',
    `total_cust_cnt` BIGINT COMMENT '活跃客户数(含SAAS)',
    `private_cust_cnt` BIGINT COMMENT '私海活跃客户数',
    `open_cust_cnt` BIGINT COMMENT '公海活跃客户数',
    `new_noactive_cust_cnt` BIGINT COMMENT '拉新客户数（仅注册未下单）',
    `new_active_cust_cnt` BIGINT COMMENT '拉新客户数（注册且下单）',
    `new_active_gmv_amt` DECIMAL(38,18) COMMENT '拉新客户GMV金额，单位：元',
    `saas_total_gmv_amt` DECIMAL(38,18) COMMENT 'SAAS总GMV金额，单位：元',
    `saas_total_cust_cnt` BIGINT COMMENT 'SAAS活跃客户数',
    `fruit_gmv_amt` DECIMAL(38,18) COMMENT '鲜果订单实付金额，单位：元',
    `fruit_cust_cnt` BIGINT COMMENT '鲜果下单客户数',
    `dairy_gmv_amt` DECIMAL(38,18) COMMENT '乳制品订单实付金额(剔除N001S01R005、N001S01R002)，单位：元',
    `dairy_cust_cnt` BIGINT COMMENT '乳制品下单客户数',
    `non_dairy_gmv_amt` DECIMAL(38,18) COMMENT '非乳制品订单实付金额，单位：元',
    `non_dairy_cust_cnt` BIGINT COMMENT '非乳制品下单客户数',
    `reward_gmv_amt` DECIMAL(38,18) COMMENT '安佳铁塔订单实付金额(N001S01R005、N001S01R002)，单位：元',
    `reward_cust_cnt` BIGINT COMMENT '安佳铁塔下单客户数',
    `timing_gmv_amt` DECIMAL(38,18) COMMENT '省心送订单实付金额，单位：元',
    `timing_cust_cnt` BIGINT COMMENT '省心送下单客户数',
    `timing_reward_gmv_amt` DECIMAL(38,18) COMMENT '安佳铁塔省心送订单实付金额，单位：元',
    `timing_reward_cust_cnt` BIGINT COMMENT '安佳铁塔省心送下单客户数',
    `normal_new_active_cust_cnt` BIGINT COMMENT '普通拉新客户数（注册且首单<15元）',
    `normal_new_active_gmv_amt` DECIMAL(38,18) COMMENT '普通拉新客户GMV金额，单位：元',
    `private_effect_cust_cnt` BIGINT COMMENT '私海有效活跃客户数（实付金额>=20元）',
    `private_normal_cust_cnt` BIGINT COMMENT '私海普通活跃客户数（实付金额<20元）',
    `open_effect_cust_cnt` BIGINT COMMENT '公海有效活跃客户数（实付金额>=20元）',
    `open_normal_cust_cnt` BIGINT COMMENT '公海普通活跃客户数（实付金额<20元）',
    `saas_brand_gmv_amt` DECIMAL(38,18) COMMENT 'SAAS自营品牌订单实付金额，单位：元',
    `saas_fruit_gmv_amt` DECIMAL(38,18) COMMENT 'SAAS鲜果订单实付金额，单位：元',
    `saas_dairy_gmv_amt` DECIMAL(38,18) COMMENT 'SAAS乳制品订单实付金额(剔除N001S01R005、N001S01R002)，单位：元',
    `saas_non_dairy_gmv_amt` DECIMAL(38,18) COMMENT 'SAAS非乳制品订单实付金额，单位：元',
    `saas_reward_gmv_amt` DECIMAL(38,18) COMMENT 'SAAS安佳铁塔订单实付金额(N001S01R005、N001S01R002)，单位：元',
    `saas_effect_cust_cnt` BIGINT COMMENT 'SAAS有效活跃客户数（实付金额>=20元）',
    `agent_gmv` DECIMAL(38,18) COMMENT '代售GMV金额，单位：元',
    `zone_name` STRING COMMENT '区域名称，枚举值：浦东、浦西、深圳等',
    `m1_name` STRING COMMENT '城市负责人名称（M1管理者）',
    `total_agent_goods_gmv` DECIMAL(38,18) COMMENT '代售品GMV金额(含SAAS)，单位：元',
    `total_agent_cust_cnt` BIGINT COMMENT '代售品客户数(含SAAS)'
)
COMMENT '区粒度平台销售业绩报表日汇总表（目前包含上海和深圳等城市），按行政区和日期统计各类销售指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据分区日期')
STORED AS ALIORC
TBLPROPERTIES (
    'comment'='区粒度销售业绩日汇总表，包含GMV、客户数、商品品类等多维度销售数据统计',
    'columnar.nested.type'='true'
)
LIFECYCLE 30;
```