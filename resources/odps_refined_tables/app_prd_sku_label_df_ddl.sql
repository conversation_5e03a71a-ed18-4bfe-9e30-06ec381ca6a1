CREATE TABLE IF NOT EXISTS app_prd_sku_label_df(
	sku_id STRING COMMENT 'SKU ID，商品最小库存单位标识',
	spu_name STRING COMMENT '商品名称，标准产品单元名称',
	category1 STRING COMMENT '一级分类，商品最高层级分类',
	category2 STRING COMMENT '二级分类，商品次高层级分类',
	category3 STRING COMMENT '三级分类，商品中层级分类',
	category4 STRING COMMENT '四级分类，商品最细粒度分类',
	sku_spec STRING COMMENT '规格，商品规格描述',
	sku_type STRING COMMENT '商品类型，取值范围：0-自营商品',
	create_time DATETIME COMMENT '创建时间，年月日时分秒格式，SKU创建时间',
	is_new STRING COMMENT '是否新品，取值范围：是/否，SKU创建时间60天内算新品',
	is_180d_order STRING COMMENT '近半年是否有动销，取值范围：是/否，近180天是否有销售',
	abc_label STRING COMMENT '货品ABC分类，取值范围：A类/B类/C类/无，基于销售表现的货品分级',
	season_sign STRING COMMENT '季节性标识，取值范围：春季/夏季/秋季/冬季/无，商品季节性特征',
	life_cycle STRING COMMENT '生命周期，取值范围：导入期/成长期/成熟期/衰退期/长尾/无，商品生命周期阶段',
	grow_coe DECIMAL(38,18) COMMENT '成长系数，商品成长性量化指标',
	grow_type STRING COMMENT '成长曲线，取值范围：波动型/稳定型/增长型/下降型，商品销售趋势类型',
	cust_preference STRING COMMENT '业态偏好，取值范围：茶饮客户偏好/面包蛋糕客户偏好/西餐客户偏好等，目标客户群体偏好',
	exposure_type STRING COMMENT '曝光类别，取值范围：高曝光/中曝光/低曝光，商品曝光程度分类',
	click_type STRING COMMENT '点击类别，取值范围：高点击/中点击/低点击，商品点击率分类',
	elastic_coefficient DECIMAL(38,18) COMMENT '弹性系数，价格弹性量化指标',
	elastic_coefficient_type STRING COMMENT '弹性类别，取值范围：弹性商品/不弹性商品，基于价格弹性的分类',
	gross_type STRING COMMENT '毛利类别，取值范围：高毛利/中毛利/低毛利，商品毛利率分类',
	order_sku_cnt_type STRING COMMENT '销量类别，取值范围：高销量/中销量/低销量，商品销售量分类',
	order_amt_type STRING COMMENT 'GMV类别，取值范围：高GMV/中GMV/低GMV，商品交易额分类',
	turnover_type STRING COMMENT '周转类别，取值范围：高周转/中周转/低周转，商品周转率分类',
	store_cost_type STRING COMMENT '库存金额类别，取值范围：高库存/中库存/低库存，库存金额分类',
	store_order_type STRING COMMENT '库存应用类标签，取值范围：高库存高销量/高库存低销量/低库存高销量/低库存低销量，库存与销量组合标签',
	order_gross_type STRING COMMENT '销售应用类标签，取值范围：高销量高毛利/高销量低毛利/低销量高毛利/低销量低毛利，销量与毛利组合标签',
	click_order_type STRING COMMENT '流量应用类标签，取值范围：高点击高销量/高点击低销量/低点击高销量/低点击低销量，点击与销量组合标签',
	purchase_amt_1d DECIMAL(38,18) COMMENT '最近1天采购金额',
	purchase_amt_15d DECIMAL(38,18) COMMENT '最近15天采购金额',
	purchase_amt_30d DECIMAL(38,18) COMMENT '最近30天采购金额',
	purchase_amt_90d DECIMAL(38,18) COMMENT '最近90天采购金额',
	purchase_amt_180d DECIMAL(38,18) COMMENT '最近180天采购金额',
	purchase_sku_cnt_1d BIGINT COMMENT '最近1天采购数量',
	purchase_sku_cnt_15d BIGINT COMMENT '最近15天采购数量',
	purchase_sku_cnt_30d BIGINT COMMENT '最近30天采购数量',
	purchase_sku_cnt_90d BIGINT COMMENT '最近90天采购数量',
	purchase_sku_cnt_180d BIGINT COMMENT '最近180天采购数量',
	first_order_time DATETIME COMMENT '最早一次下单时间，年月日时分秒格式',
	last_order_time DATETIME COMMENT '最近一次下单时间，年月日时分秒格式',
	order_origin_amt_1d DECIMAL(38,18) COMMENT '最近1天下单应付金额',
	order_origin_amt_15d DECIMAL(38,18) COMMENT '最近15天下单应付金额',
	order_origin_amt_30d DECIMAL(38,18) COMMENT '最近30天下单应付金额',
	order_origin_amt_90d DECIMAL(38,18) COMMENT '最近90天下单应付金额',
	order_origin_amt_180d DECIMAL(38,18) COMMENT '最近180天下单应付金额',
	order_real_amt_1d DECIMAL(38,18) COMMENT '最近1天下单实付金额',
	order_real_amt_15d DECIMAL(38,18) COMMENT '最近15天下单实付金额',
	order_real_amt_30d DECIMAL(38,18) COMMENT '最近30天下单实付金额',
	order_real_amt_90d DECIMAL(38,18) COMMENT '最近90天下单实付金额',
	order_real_amt_180d DECIMAL(38,18) COMMENT '最近180天下单实付金额',
	order_preferential_amt_1d DECIMAL(38,18) COMMENT '最近1天下单营销金额',
	order_preferential_amt_15d DECIMAL(38,18) COMMENT '最近15天下单营销金额',
	order_preferential_amt_30d DECIMAL(38,18) COMMENT '最近30天下单营销金额',
	order_preferential_amt_90d DECIMAL(38,18) COMMENT '最近90天下单营销金额',
	order_preferential_amt_180d DECIMAL(38,18) COMMENT '最近180天下单营销金额',
	order_cust_cnt_1d BIGINT COMMENT '最近1天下单客户数',
	order_cust_cnt_15d BIGINT COMMENT '最近15天下单客户数',
	order_cust_cnt_30d BIGINT COMMENT '最近30天下单客户数',
	order_cust_cnt_90d BIGINT COMMENT '最近90天下单客户数',
	order_cust_cnt_180d BIGINT COMMENT '最近180天下单客户数',
	order_sku_cnt_1d BIGINT COMMENT '最近1天下单商品件数',
	order_sku_cnt_15d BIGINT COMMENT '最近15天下单商品件数',
	order_sku_cnt_30d BIGINT COMMENT '最近30天下单商品件数',
	order_sku_cnt_90d BIGINT COMMENT '最近90天下单商品件数',
	order_sku_cnt_180d BIGINT COMMENT '最近180天下单商品件数',
	order_cnt_1d BIGINT COMMENT '最近1天下单订单数',
	order_cnt_15d BIGINT COMMENT '最近15天下单订单数',
	order_cnt_30d BIGINT COMMENT '最近30天下单订单数',
	order_cnt_90d BIGINT COMMENT '最近90天下单订单数',
	order_cnt_180d BIGINT COMMENT '最近180天下单订单数',
	order_origin_unit_amt_180d_avg DECIMAL(38,18) COMMENT '最近180天平均应付单价',
	order_origin_unit_amt_180d_avg_range STRING COMMENT '价格段，取值范围：[0,50)/[50,100)/[100,200)/[200,500)/[500,∞)，根据近180天的平均价格进行划分',
	delivery_origin_amt_1d DECIMAL(38,18) COMMENT '最近1天履约应付金额',
	delivery_origin_amt_15d DECIMAL(38,18) COMMENT '最近15天履约应付金额',
	delivery_origin_amt_30d DECIMAL(38,18) COMMENT '最近30天履约应付金额',
	delivery_origin_amt_90d DECIMAL(38,18) COMMENT '最近90天履约应付金额',
	delivery_origin_amt_180d DECIMAL(38,18) COMMENT '最近180天履约应付金额',
	delivery_point_cnt_1d BIGINT COMMENT '最近1天履约点位数',
	delivery_point_cnt_15d BIGINT COMMENT '最近15天履约点位数',
	delivery_point_cnt_30d BIGINT COMMENT '最近30天履约点位数',
	delivery_point_cnt_90d BIGINT COMMENT '最近90天履约点位数',
	delivery_point_cnt_180d BIGINT COMMENT '最近180天履约点位数',
	delivery_cost_amt_180d_avg DECIMAL(38,18) COMMENT '最近180天履约平均成本单价',
	delivery_cost_gross_180d_avg DECIMAL(38,18) COMMENT '最近180天履约平均应付毛利率',
	delivery_cost_gross_180d_avg_range STRING COMMENT '毛利率段，取值范围：0-5%/5-10%/10-15%/15-20%/20-∞%，根据近180天的毛利进行划分',
	store_quantity BIGINT COMMENT '在库数量',
	store_cost DECIMAL(38,18) COMMENT '在库金额',
	temporary_store_quantity BIGINT COMMENT '临期数量',
	temporary_store_ratio DECIMAL(38,18) COMMENT '临期数量占比',
	purchase_road_quantity BIGINT COMMENT '采购在途数量',
	purchase_road_cost DECIMAL(38,18) COMMENT '采购在途金额',
	allocate_road_quantity BIGINT COMMENT '调拨在途数量',
	allocate_road_cost DECIMAL(38,18) COMMENT '调拨在途金额',
	turnover_days DECIMAL(38,18) COMMENT '周转天数，近7天平均在库金额/近7天平均销售出库金额(含自提)',
	store_days DECIMAL(38,18) COMMENT '库存可用天数，在库金额/近7天平均销售出库金额(含自提)',
	damage_sku_cnt_1d BIGINT COMMENT '最近1天货损数量',
	damage_sku_cnt_15d BIGINT COMMENT '最近15天货损数量',
	damage_sku_cnt_30d BIGINT COMMENT '最近30天货损数量',
	damage_sku_cnt_90d BIGINT COMMENT '最近90天货损数量',
	damage_sku_cnt_180d BIGINT COMMENT '最近180天货损数量',
	damage_amt_1d DECIMAL(38,18) COMMENT '最近1天货损金额',
	damage_amt_15d DECIMAL(38,18) COMMENT '最近15天货损金额',
	damage_amt_30d DECIMAL(38,18) COMMENT '最近30天货损金额',
	damage_amt_90d DECIMAL(38,18) COMMENT '最近90天货损金额',
	damage_amt_180d DECIMAL(38,18) COMMENT '最近180天货损金额',
	after_sale_deliveryed_amt_1d DECIMAL(38,18) COMMENT '最近1天已到货售后金额',
	after_sale_deliveryed_amt_15d DECIMAL(38,18) COMMENT '最近15天已到货售后金额',
	after_sale_deliveryed_amt_30d DECIMAL(38,18) COMMENT '最近30天已到货售后金额',
	after_sale_deliveryed_amt_90d DECIMAL(38,18) COMMENT '最近90天已到货售后金额',
	after_sale_deliveryed_amt_180d DECIMAL(38,18) COMMENT '最近180天已到货售后金额',
	after_sale_deliveryed_order_cnt_1d BIGINT COMMENT '最近1天已到货售后订单数',
	after_sale_deliveryed_order_cnt_15d BIGINT COMMENT '最近15天已到货售后订单数',
	after_sale_deliveryed_order_cnt_30d BIGINT COMMENT '最近30天已到货售后订单数',
	after_sale_deliveryed_order_cnt_90d BIGINT COMMENT '最近90天已到货售后订单数',
	after_sale_deliveryed_order_cnt_180d BIGINT COMMENT '最近180天已到货售后订单数',
	after_sale_notdeliveryed_amt_1d DECIMAL(38,18) COMMENT '最近1天未到货售后金额',
	after_sale_notdeliveryed_amt_15d DECIMAL(38,18) COMMENT '最近15天未到货售后金额',
	after_sale_notdeliveryed_amt_30d DECIMAL(38,18) COMMENT '最近30天未到货售后金额',
	after_sale_notdeliveryed_amt_90d DECIMAL(38,18) COMMENT '最近90天未到货售后金额',
	after_sale_notdeliveryed_amt_180d DECIMAL(38,18) COMMENT '最近180天未到货售后金额',
	after_sale_notdeliveryed_order_cnt_1d BIGINT COMMENT '最近1天未到货售后订单数',
	after_sale_notdeliveryed_order_cnt_15d BIGINT COMMENT '最近15天未到货售后订单数',
	after_sale_notdeliveryed_order_cnt_30d BIGINT COMMENT '最近30天未到货售后订单数',
	after_sale_notdeliveryed_order_cnt_90d BIGINT COMMENT '最近90天未到货售后订单数',
	after_sale_notdeliveryed_order_cnt_180d BIGINT COMMENT '最近180天未到货售后订单数',
	sku_exposure_pv_1d BIGINT COMMENT '最近1天曝光PV',
	sku_exposure_pv_15d BIGINT COMMENT '最近15天曝光PV',
	sku_exposure_pv_30d BIGINT COMMENT '最近30天曝光PV',
	sku_exposure_uv_1d BIGINT COMMENT '最近1天曝光UV',
	sku_exposure_uv_15d BIGINT COMMENT '最近15天曝光UV',
	sku_exposure_uv_30d BIGINT COMMENT '最近30天曝光UV',
	sku_click_pv_1d BIGINT COMMENT '最近1天点击PV',
	sku_click_pv_15d BIGINT COMMENT '最近15天点击PV',
	sku_click_pv_30d BIGINT COMMENT '最近30天点击PV',
	sku_click_uv_1d BIGINT COMMENT '最近1天点击UV',
	sku_click_uv_15d BIGINT COMMENT '最近15天点击UV',
	sku_click_uv_30d BIGINT COMMENT '最近30天点击UV'
) 
COMMENT '商品标签表（自营），包含商品基础信息、分类标签、销售标签、库存标签、流量标签等全方位商品标签数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，yyyyMMdd格式，数据统计日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='商品标签表（自营），用于商品分析、选品决策、库存优化等业务场景') 
LIFECYCLE 30;