CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_spu_delivery_selfowend_mi` (
  `month` STRING COMMENT '月份，格式：yyyyMM',
  `cause_type` STRING COMMENT '业务类型；枚举：鲜沐,SAAS',
  `province` STRING COMMENT '省份名称',
  `admin_city` STRING COMMENT '城市名称',
  `area` STRING COMMENT '区域名称',
  `spu_id` BIGINT COMMENT '商品ID（pd_id）',
  `spu_name` STRING COMMENT '商品名称',
  `cust_type` STRING COMMENT '客户类型；枚举：面包蛋糕,西餐,咖啡,甜品冰淇淋,茶饮,其他',
  `brand_type` STRING COMMENT '大客户类型；枚举：大客户,普通,批发客户',
  `brand_name` STRING COMMENT '品牌名称',
  `category1` STRING COMMENT '一级类目名称',
  `category2_id` STRING COMMENT '二级类目ID',
  `category2` STRING COMMENT '二级类目名称',
  `category3_id` STRING COMMENT '三级类目ID',
  `category3` STRING COMMENT '三级类目名称',
  `category4_id` STRING COMMENT '四级类目ID',
  `category4` STRING COMMENT '四级类目名称',
  `origin_total_amt` DECIMAL(38,18) COMMENT '原始配送应付总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '实际配送应付总金额',
  `cost_amt` DECIMAL(38,18) COMMENT '总成本金额',
  `origin_pay_margin` DECIMAL(38,18) COMMENT '原始应付毛利润',
  `real_pay_margin` DECIMAL(38,18) COMMENT '实际应付毛利润',
  `preferential_amt` DECIMAL(38,18) COMMENT '营销优惠金额',
  `after_sale_amt` DECIMAL(38,18) COMMENT '已到货售后总金额',
  `deliver_total_amt` DECIMAL(38,18) COMMENT '配送GMV总金额'
) 
COMMENT '城市整体配送数据日表，包含商品配送相关的业务数据和财务指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据日期'
)
STORED AS ALIORC  
TBLPROPERTIES ('comment'='城市整体配送数据日表，记录各城市商品配送的业务和财务数据') 
LIFECYCLE 30;