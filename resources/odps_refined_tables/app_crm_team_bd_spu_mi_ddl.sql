CREATE TABLE IF NOT EXISTS app_crm_team_bd_spu_mi(
	`month` STRING COMMENT '月份，格式为yyyyMM',
	`cust_team` STRING COMMENT '客户团队类型，取值范围：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户',
	`brand_alias` STRING COMMENT '品牌别称',
	`register_province` STRING COMMENT '注册时省份',
	`register_city` STRING COMMENT '注册时城市',
	`brand_id` BIGINT COMMENT '公司ID，-1表示无品牌',
	`brand_name` STRING COMMENT '公司名称',
	`bd_id` BIGINT COMMENT '销售ID，-1表示无销售',
	`bd_name` STRING COMMENT '销售名称',
	`spu_no` STRING COMMENT 'SPU编号',
	`spu_name` STRING COMMENT '商品名称',
	`sku_spec` STRING COMMENT '商品规格',
	`sku_brand` STRING COMMENT '商品品牌',
	`sku_variety` STRING COMMENT '商品品种',
	`category_1` STRING COMMENT '一级类目',
	`category_2` STRING COMMENT '二级类目',
	`category_3` STRING COMMENT '三级类目',
	`category_4` STRING COMMENT '四级类目',
	`sku_type` BIGINT COMMENT '商品类型，取值范围：0-自营，1-代仓，2-其他',
	`is_self_owned_brand` BIGINT COMMENT '是否自营品牌，取值范围：0-否，1-是',
	`origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额',
	`real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额',
	`cost_amt` DECIMAL(38,18) COMMENT '履约成本',
	`sku_cnt` BIGINT COMMENT '总配送件数',
	`cust_cnt` BIGINT COMMENT '履约门店数',
	`after_sale_amt` DECIMAL(38,18) COMMENT '售后金额'
) 
COMMENT '大客户团队商品SPU与客户名称与BD结合维度汇总表，包含大客户团队的销售数据、商品信息和履约指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='大客户团队商品SPU与客户名称与BD结合维度汇总表，用于分析大客户团队的销售业绩和商品表现') 
LIFECYCLE 30;