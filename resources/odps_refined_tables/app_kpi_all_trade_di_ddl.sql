CREATE TABLE IF NOT EXISTS app_kpi_all_trade_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示年月日',
	`manage_type` STRING COMMENT '业务线类型，枚举值：自营、代仓、代售、批发、SAAS鲜沐自营、SAAS鲜沐代仓、SAAS品牌方自营',
	`origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额，交易订单的原始应付金额总计',
	`real_total_amt` DECIMAL(38,18) COMMENT '实付总金额，交易订单的实际支付金额总计',
	`cust_cnt` BIGINT COMMENT '客户数，当日有交易行为的客户数量',
	`order_cnt` BIGINT COMMENT '订单数，当日产生的交易订单总数',
	`tenant_cnt` BIGINT COMMENT '租户数，仅SAAS业务线有效，表示当日活跃的租户数量',
	`delivery_amt` DECIMAL(38,18) COMMENT '运费金额，包含运费和超时加单费的总和',
	`cust_arpu` DECIMAL(38,18) COMMENT 'ARPU值，平均每客户收入，计算公式：应付总金额/客户数',
	`order_avg` DECIMAL(38,18) COMMENT '订单均价，平均每订单金额，计算公式：应付总金额/订单数',
	`after_sale_noreceived_order_cnt` BIGINT COMMENT '未到货售后订单数，商品未收到货的售后订单数量',
	`after_sale_noreceived_amt` DECIMAL(38,18) COMMENT '未到货售后总金额，商品未收到货的售后订单金额总计',
	`after_sale_rate` DECIMAL(38,18) COMMENT '退货率，未到货售后金额占比，计算公式：未到货售后总金额/应付总金额'
) 
COMMENT '交易口径业务线KPI指标日汇总表，按业务线维度统计每日交易相关核心指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='交易业务线KPI日汇总表，包含各业务线的交易金额、订单数、客户数、售后指标等核心业务指标') 
LIFECYCLE 30;