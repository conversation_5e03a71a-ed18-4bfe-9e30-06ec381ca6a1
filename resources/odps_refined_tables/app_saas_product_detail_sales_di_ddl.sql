CREATE TABLE IF NOT EXISTS app_saas_product_detail_sales_di(
	tenant_id BIGINT COMMENT '租户ID',
	time_tag STRING COMMENT '时间标签，格式：yyyyMMdd（年月日）',
	item_id BIGINT COMMENT '商品编码',
	sku_id STRING COMMENT '商品SKU ID',
	delivery_type BIGINT COMMENT '仓储类型：0-自营仓，1-第三方仓',
	warehouse_type BIGINT COMMENT '商品类型：0-自营，2-经销',
	title STRING COMMENT '商品名称',
	specification STRING COMMENT '规格',
	category STRING COMMENT '类目',
	brand_name STRING COMMENT '品牌名称',
	supply_price DECIMAL(38,18) COMMENT '供应价',
	price DECIMAL(38,18) COMMENT '售价',
	category_id BIGINT COMMENT '三级类目ID',
	store_type BIGINT COMMENT '门店类型：0-直营店，1-加盟店，2-托管店',
	store_id BIGINT COMMENT '门店ID',
	store_name STRING COMMENT '门店名称',
	province STRING COMMENT '省份',
	city STRING COMMENT '城市',
	address STRING COMMENT '地址信息（省+市）',
	sales_num BIGINT COMMENT '销售数量',
	sales_price DECIMAL(38,18) COMMENT '销售额',
	after_sale_num BIGINT COMMENT '售后数量',
	after_sale_price DECIMAL(38,18) COMMENT '售后金额',
	goods_type BIGINT COMMENT '商品类型：0-无货商品，1-报价货品，2-自营货品',
	supplier_name STRING COMMENT '供应商名称',
	after_sale_unit STRING COMMENT '售后单位',
	after_sale_apply_price DECIMAL(38,18) COMMENT '供应商退款申请金额',
	after_sale_total_price DECIMAL(38,18) COMMENT '供应商退款实际金额'
) 
COMMENT 'SaaS商品销售数据表，包含商品销售明细、门店信息、售后数据等'
PARTITIONED BY (ds STRING COMMENT '分区字段，yyyyMMdd（年月日）') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='SaaS商品销售明细数据表，用于商品销售分析和业务报表') 
LIFECYCLE 30;