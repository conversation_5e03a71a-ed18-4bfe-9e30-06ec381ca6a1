CREATE TABLE IF NOT EXISTS app_area_warehouse_cost_wi(
	`saturday` STRING COMMENT '周六，格式为yyyyMMdd，表示周统计的起始日期（周六）',
	`friday` STRING COMMENT '周五，格式为yyyyMMdd，表示周统计的结束日期（周五）',
	`service_area` STRING COMMENT '服务区域，取值范围：华东、广西、福建、华北等',
	`warehouse_no` BIGINT COMMENT '仓库编号，唯一标识一个仓库',
	`warehouse_name` STRING COMMENT '仓库名称',
	`in_quality` BIGINT COMMENT '入库件数，统计周期内的入库商品数量',
	`out_quality` BIGINT COMMENT '出库件数，统计周期内的出库商品数量',
	`on_quality` BIGINT COMMENT '在库件数，统计周期末的在库商品数量',
	`storage_amt` DECIMAL(38,18) COMMENT '仓储成本，统计周期内的仓储费用',
	`delivery_amt` DECIMAL(38,18) COMMENT '配送GMV，统计周期内的配送商品总价值'
)
COMMENT '库存仓维度仓储成本周表，按仓库维度统计每周的仓储成本和配送GMV数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据统计日期')
STORED AS ALIORC
TBLPROPERTIES ('comment'='库存仓维度仓储成本周统计表，包含入库、出库、在库件数以及仓储成本和配送GMV等关键指标')
LIFECYCLE 30;