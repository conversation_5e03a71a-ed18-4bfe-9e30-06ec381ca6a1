CREATE TABLE IF NOT EXISTS app_cust_brand_category_performance_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
	`category` STRING COMMENT '品类:取值范围为鲜果、乳制品、其他',
	`order_origin_amt` DECIMAL(38,18) COMMENT '交易应付总金额，订单原始应付金额',
	`order_real_amt` DECIMAL(38,18) COMMENT '交易实付总金额，订单实际支付金额',
	`delivery_origin_amt` DECIMAL(38,18) COMMENT '履约应付总金额，履约原始应付金额',
	`delivery_real_amt` DECIMAL(38,18) COMMENT '履约实付总金额，履约实际支付金额',
	`delivery_cash_real_amt` DECIMAL(38,18) COMMENT '履约现结实付总金额，履约现金支付金额',
	`delivery_bill_real_amt` DECIMAL(38,18) COMMENT '履约账期实付总金额，履约账期支付金额',
	`delivery_cost_amt` DECIMAL(38,18) COMMENT '履约商品成本金额，履约商品成本',
	`delivery_real_gross` DECIMAL(38,18) COMMENT '履约实付毛利润，履约实付金额减去成本',
	`delivery_real_gross_rate` DECIMAL(38,18) COMMENT '履约实付毛利率，履约实付毛利润占实付金额的比例',
	`delivery_cust_cnt` BIGINT COMMENT '履约客户数，完成履约的客户数量',
	`delivery_point_cnt` BIGINT COMMENT '履约累计点位数，完成履约的点位总数',
	`after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后总金额，已到货的售后金额',
	`after_sale_rate` DECIMAL(38,18) COMMENT '售后比例，已到货售后金额占履约实付GMV的比例'
) 
COMMENT '大客户品类粒度监控表，按品类维度统计大客户的交易和履约表现指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据所属日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='大客户品类粒度监控表，包含交易金额、履约金额、成本、利润、客户数、点位数等核心业务指标') 
LIFECYCLE 30;