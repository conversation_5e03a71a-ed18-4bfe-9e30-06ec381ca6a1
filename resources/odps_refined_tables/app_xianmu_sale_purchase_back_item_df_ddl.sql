```sql
CREATE TABLE IF NOT EXISTS app_xianmu_sale_purchase_back_item_df(
    return_no STRING COMMENT '退货编号，唯一标识一次退货操作',
    purchase_no STRING COMMENT '采购单号，关联采购订单表',
    order_no STRING COMMENT '销售单号，关联销售订单表',
    sku STRING COMMENT 'SKU编码，商品唯一标识',
    pd_name STRING COMMENT '商品名称',
    weight STRING COMMENT '商品规格/重量信息，None表示无规格信息',
    purchase_quantity BIGINT COMMENT '采购数量，正整数',
    actual_quantity BIGINT COMMENT '实际退货数量，正整数',
    cost DECIMAL(38,18) COMMENT '单个商品成本价格，单位：元',
    total_cost DECIMAL(38,18) COMMENT '退货总成本金额，计算公式：actual_quantity × cost，单位：元',
    no_in_quantity BIGINT COMMENT '未入库数量，0表示已全部入库，正整数'
)
COMMENT '采购退货单明细表，记录采购退货的商品明细信息，包括退货数量、成本等关键业务数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据所属日期')
STORED AS ALIORC
TBLPROPERTIES ('comment'='采购退货业务数据表，用于退货成本核算和库存管理')
LIFECYCLE 30;
```