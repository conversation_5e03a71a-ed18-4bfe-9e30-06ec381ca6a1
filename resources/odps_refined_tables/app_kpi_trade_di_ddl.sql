CREATE TABLE IF NOT EXISTS app_kpi_trade_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务日期',
	`origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额，交易中应付的总金额，单位为元',
	`real_total_amt` DECIMAL(38,18) COMMENT '实付总金额，交易中实际支付的总金额，单位为元',
	`cust_cnt` BIGINT COMMENT '客户数，当日有交易行为的独立客户数量',
	`cust_arpu` DECIMAL(38,18) COMMENT 'ARPU(平均每用户收入)，计算公式：应付总金额/客户数，单位为元/用户',
	`order_cnt` BIGINT COMMENT '订单数，当日产生的有效订单数量',
	`order_avg` DECIMAL(38,18) COMMENT '订单均价，计算公式：应付总金额/订单数，单位为元/订单',
	`after_sale_noreceived_amt` DECIMAL(38,18) COMMENT '未到货售后总金额，商品未到货状态下的售后申请金额，单位为元',
	`after_sale_rate` DECIMAL(38,18) COMMENT '退货率，计算公式：未到货售后总金额/应付总金额，取值范围0-1',
	`dire_origin_total_amt` DECIMAL(38,18) COMMENT '直发采购应付总金额，直发采购模式的应付金额，单位为元',
	`delivery_amt` DECIMAL(38,18) COMMENT '运费，包含运费和超时加单费的总和，单位为元',
	`timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送应付总金额，省心送服务模式的应付金额，单位为元'
) 
COMMENT '交易口径KPI指标日汇总表，包含交易相关的核心业务指标每日汇总数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据分区日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true') 
LIFECYCLE 30;