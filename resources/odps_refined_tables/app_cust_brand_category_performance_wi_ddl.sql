CREATE TABLE IF NOT EXISTS app_cust_brand_category_performance_wi(
	year STRING COMMENT '年份，格式：YYYY',
	week_of_year BIGINT COMMENT '周数，取值范围：1-53',
	monday STRING COMMENT '周一日期，格式：YYYYMMDD，表示年月日',
	sunday STRING COMMENT '周日日期，格式：YYYYMMDD，表示年月日',
	category STRING COMMENT '品类，枚举值：鲜果、乳制品、其他',
	order_origin_amt DECIMAL(38,18) COMMENT '交易应付总金额',
	order_real_amt DECIMAL(38,18) COMMENT '交易实付总金额',
	delivery_origin_amt DECIMAL(38,18) COMMENT '履约应付总金额',
	delivery_real_amt DECIMAL(38,18) COMMENT '履约实付总金额',
	delivery_cash_real_amt DECIMAL(38,18) COMMENT '履约现结实付总金额',
	delivery_bill_real_amt DECIMAL(38,18) COMMENT '履约账期实付总金额',
	delivery_cost_amt DECIMAL(38,18) COMMENT '履约商品成本金额',
	delivery_real_gross DECIMAL(38,18) COMMENT '履约实付毛利润',
	delivery_real_gross_rate DECIMAL(38,18) COMMENT '履约实付毛利率',
	delivery_cust_cnt BIGINT COMMENT '履约客户数',
	delivery_point_cnt BIGINT COMMENT '履约累计点位数',
	after_sale_received_amt DECIMAL(38,18) COMMENT '已到货售后总金额',
	after_sale_rate DECIMAL(38,18) COMMENT '售后比例(已到货售后金额/履约实付GMV)'
) 
COMMENT '大客户品类粒度监控表，按周和品类维度统计大客户的交易和履约相关指标'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：YYYYMMDD，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='大客户品类粒度监控表，用于监控大客户在不同品类下的交易和履约表现') 
LIFECYCLE 30;