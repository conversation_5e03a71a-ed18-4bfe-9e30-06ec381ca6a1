CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_after_sale_supplier_detail_di` (
  `date` STRING COMMENT '业务日期，格式为yyyyMMdd',
  `after_sale_order_id` STRING COMMENT '售后单号，即after_sale_order_no',
  `apply_time` DATETIME COMMENT '售后申请时间，格式为年月日时分秒',
  `reason` STRING COMMENT '售后原因，枚举值：商品品质问题等',
  `handle_type` STRING COMMENT '处理方式，枚举值：录入账单、退款、返券等',
  `review_remark` STRING COMMENT '审核备注',
  `after_sale_sku_cnt` BIGINT COMMENT '售后商品数量',
  `after_sale_amt` DECIMAL(38,18) COMMENT '售后金额',
  `sku_id` STRING COMMENT '商品SKU',
  `spu_id` BIGINT COMMENT '商品SPU；即pd_id',
  `spu_no` STRING COMMENT 'spu编号',
  `spu_name` STRING COMMENT '商品名',
  `sku_disc` STRING COMMENT 'sku描述；即weight',
  `sku_type` STRING COMMENT '商品类型；枚举值：自营、代仓、代售',
  `cust_id` BIGINT COMMENT '售后用户，即客户ID',
  `cust_name` STRING COMMENT '客户名',
  `cust_type` STRING COMMENT '客户类型，枚举值：大客户、普通等',
  `cust_team` STRING COMMENT '客户团队类型；枚举值：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户、批发等',
  `brand_id` BIGINT COMMENT '品牌ID（原大客户），-1表示无品牌',
  `brand_name` STRING COMMENT '品牌的企业名称',
  `brand_alias` STRING COMMENT '品牌的品牌名称',
  `city_id` BIGINT COMMENT '运营服务ID',
  `city_name` STRING COMMENT '运营服务名称',
  `large_area_id` BIGINT COMMENT '运营服务大区id',
  `large_area_name` STRING COMMENT '运营服务大区name',
  `province` STRING COMMENT '注册时省',
  `purchase_no` STRING COMMENT '采购批次',
  `supplier` STRING COMMENT '批次供应商',
  `store_no` BIGINT COMMENT '配送仓编号',
  `store_name` STRING COMMENT '配送仓名称',
  `warehouse_no` BIGINT COMMENT '库存仓编号',
  `warehouse_name` STRING COMMENT '库存仓名称',
  `after_sale_type` STRING COMMENT '售后分类，枚举值：仓配物流,开裂/压伤、质量,腐烂/发霉/变质/黑斑等',
  `receipt_method` STRING COMMENT '收货方式'
) 
COMMENT '已到货售后有码数据表，记录售后订单的详细信息，包括商品信息、客户信息、供应商信息等'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd'
)
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='已到货售后有码数据表，用于售后数据分析和供应商管理') 
LIFECYCLE 30;