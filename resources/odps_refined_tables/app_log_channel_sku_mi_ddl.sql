```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_log_channel_sku_mi` (
  `month` STRING COMMENT '月份，格式为yyyyMM',
  `fsku_id` STRING COMMENT 'SKU编号，商品唯一标识',
  `category1` STRING COMMENT '一级类目，商品分类最高层级',
  `category2` STRING COMMENT '二级类目，商品分类第二层级',
  `category3` STRING COMMENT '三级类目，商品分类第三层级',
  `category4` STRING COMMENT '四级类目，商品分类最细粒度层级',
  `sku_type` STRING COMMENT '商品类型：1-自营，2-代仓，3-代售',
  `spu_name` STRING COMMENT '商品名称，标准产品单元名称',
  `sku_disc` STRING COMMENT '商品描述，包含规格和包装信息',
  `model` STRING COMMENT '渠道类型：banner-横幅广告，search-搜索，recommend-推荐等',
  `uv` BIGINT COMMENT '曝光UV，独立访客数，范围：1-28385',
  `pv` BIGINT COMMENT '曝光PV，页面浏览量，范围：1-101829',
  `click_uv` BIGINT COMMENT '点击UV，独立点击用户数，范围：0-3872',
  `click_pv` BIGINT COMMENT '点击PV，总点击次数，范围：0-13363',
  `addbug_uv` BIGINT COMMENT '加购UV，独立加购用户数，范围：0-2382',
  `addbug_pv` BIGINT COMMENT '加购PV，总加购次数，范围：0-6060',
  `cust_cnt` BIGINT COMMENT '交易人数，完成购买的用户数，范围：0-1653'
) 
COMMENT '流量分渠道SKU转化表，记录各渠道商品流量转化数据，用于分析商品在不同渠道的曝光、点击、加购和交易转化情况'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '流量分渠道SKU转化分析表',
  'last_data_modified_time' = '2025-09-18 02:32:34'
)
LIFECYCLE 365;
```