CREATE TABLE IF NOT EXISTS app_kpi_operate_large_area_delivery_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务发生日期',
	`large_area_name` STRING COMMENT '运营服务大区名称，枚举值包括：上海大区、南宁大区、成都大区、昆明大区、昆明快递大区等',
	`origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV，原始订单总金额',
	`real_total_amt` DECIMAL(38,18) COMMENT '履约实付GMV，实际支付总金额',
	`marketing_amt` DECIMAL(38,18) COMMENT '营销费用，用于推广和营销活动的费用',
	`cost_amt` DECIMAL(38,18) COMMENT '成本金额，业务运营的直接成本',
	`origin_gross` DECIMAL(38,18) COMMENT '应付毛利润，原始订单的毛利润',
	`real_gross` DECIMAL(38,18) COMMENT '实付毛利润，实际支付的毛利润',
	`origin_gross_margin` DECIMAL(38,18) COMMENT '应付毛利率，原始订单的毛利率比例',
	`real_gross_margin` DECIMAL(38,18) COMMENT '实付毛利率，实际支付的毛利率比例',
	`cust_cnt` BIGINT COMMENT '履约客户数，完成履约的客户数量',
	`point_cnt` BIGINT COMMENT '点位数，业务覆盖的服务点位数量',
	`origin_pre_cust_price` DECIMAL(38,18) COMMENT '应付客单价，原始订单的客户平均消费金额',
	`real_pre_cust_price` DECIMAL(38,18) COMMENT '实付客单价，实际支付的客户平均消费金额',
	`timing_origin_amt` DECIMAL(38,18) COMMENT '省心送应付GMV，省心送服务的原始订单金额',
	`timing_real_amt` DECIMAL(38,18) COMMENT '省心送实付GMV，省心送服务的实际支付金额',
	`consign_origin_amt` DECIMAL(38,18) COMMENT '代售应付GMV，代售服务的原始订单金额',
	`consign_real_amt` DECIMAL(38,18) COMMENT '代售实付GMV，代售服务的实际支付金额',
	`consign_marketing_amt` DECIMAL(38,18) COMMENT '代售营销费用，代售服务的营销费用',
	`consign_origin_gross` DECIMAL(38,18) COMMENT '代售应付毛利润，代售服务的原始毛利润',
	`consign_real_gross` DECIMAL(38,18) COMMENT '代售实付毛利润，代售服务的实际毛利润',
	`consign_cust_cnt` BIGINT COMMENT '代售履约客户数，代售服务完成的客户数量',
	`storage_amt` DECIMAL(38,18) COMMENT '仓储费，商品存储相关的费用',
	`arterial_roads_amt` DECIMAL(38,18) COMMENT '单点干线费，主要运输干线的费用',
	`self_picked_amt` DECIMAL(38,18) COMMENT '采购自提费，客户自提商品产生的费用',
	`allocation_amt` DECIMAL(38,18) COMMENT '调拨费，商品调拨运输的费用',
	`other_amt` DECIMAL(38,18) COMMENT '其他费，其他未分类的费用',
	`deliver_amt` DECIMAL(38,18) COMMENT '配送费，商品配送服务的费用'
) 
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='运营履约KPI结果表（平台客户），包含各大区的履约业务关键指标数据，如GMV、毛利率、客户数、费用明细等') 
LIFECYCLE 30;