```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_crm_team_brandalias_performance_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务日期',
  `cust_team` STRING COMMENT '客户团队类型，枚举值：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户',
  `brand_alias` STRING COMMENT '品牌别名，如：一鸣、七分甜、乐乐茶等',
  `order_origin_total_amt` DECIMAL(38,18) COMMENT '交易应付总金额（元），含优惠前的订单金额',
  `order_real_total_amt` DECIMAL(38,18) COMMENT '交易实付总金额（元），实际支付金额',
  `order_brand_cnt` BIGINT COMMENT '交易公司数，发生交易的公司数量',
  `order_cust_cnt` BIGINT COMMENT '交易门店数，发生交易的门店数量',
  `order_order_cnt` BIGINT COMMENT '交易订单数，总订单数量',
  `new_cust_cnt` BIGINT COMMENT '活跃门店数中新增门店数，新激活的门店数量',
  `new_cust_gmv` DECIMAL(38,18) COMMENT '新增活跃门店GMV（元），新增门店的交易总额',
  `close_cust_cnt` BIGINT COMMENT '活跃门店数中倒闭门店数，关闭的门店数量',
  `close_cust_gmv` DECIMAL(38,18) COMMENT '倒闭门店GMV（元），倒闭门店的交易总额',
  `old_cust_cnt` BIGINT COMMENT '老活跃门店数，持续活跃的既有门店数量',
  `old_cust_gmv` DECIMAL(38,18) COMMENT '老活跃门店GMV（元），既有门店的交易总额',
  `new_noactive_cust_cnt` BIGINT COMMENT '拉新门店数（仅注册未下单），注册但未下单的新门店数量',
  `new_active_cust_cnt` BIGINT COMMENT '拉新门店数（注册且下单），注册并下单的新门店数量',
  `new_active_gmv` DECIMAL(38,18) COMMENT '拉新门店GMV（元），新注册下单门店的交易总额',
  `order_replace_cust_cnt` BIGINT COMMENT '代下单门店数，代为下单的门店数量',
  `order_replace_gmv` DECIMAL(38,18) COMMENT '代下单实付金额（元），代为下单的实际支付金额',
  `origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额（元），履约环节的应付总额',
  `real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额（元），履约环节的实际支付总额',
  `cost_amt` DECIMAL(38,18) COMMENT '履约成本（元），履约环节的成本金额',
  `order_cnt` BIGINT COMMENT '履约订单数，完成履约的订单数量',
  `sku_cnt` BIGINT COMMENT '总配送件数，配送的商品总件数',
  `point_cnt` BIGINT COMMENT '总点位数，配送点位总数',
  `cust_cnt` BIGINT COMMENT '履约门店数，完成履约的门店数量',
  `brand_cnt` BIGINT COMMENT '履约公司数，完成履约的公司数量',
  `self_real_total_amt` DECIMAL(38,18) COMMENT '自营实付总金额（元），自营业务的实际支付总额',
  `self_cost_amt` DECIMAL(38,18) COMMENT '自营成本（元），自营业务的成本金额',
  `self_cust_cnt` BIGINT COMMENT '自营品牌门店数，自营业务的门店数量',
  `self_brand_cnt` BIGINT COMMENT '自营品牌公司数，自营业务的公司数量',
  `timing_real_total_amt` DECIMAL(38,18) COMMENT '省心送实付总金额（元），省心送业务的实际支付总额',
  `timing_cost_amt` DECIMAL(38,18) COMMENT '省心送成本（元），省心送业务的成本金额',
  `timing_cust_cnt` BIGINT COMMENT '省心送门店数，省心送业务的门店数量',
  `timing_brand_cnt` BIGINT COMMENT '省心送公司数，省心送业务的公司数量',
  `fruit_real_total_amt` DECIMAL(38,18) COMMENT '鲜果实付总金额（元），鲜果业务的实际支付总额',
  `fruit_cost_amt` DECIMAL(38,18) COMMENT '鲜果成本（元），鲜果业务的成本金额',
  `fruit_cash_real_total_amt` DECIMAL(38,18) COMMENT '鲜果账期实付金额（元），鲜果账期业务的实际支付金额',
  `fruit_cust_cnt` BIGINT COMMENT '鲜果门店数，鲜果业务的门店数量',
  `dairy_real_total_amt` DECIMAL(38,18) COMMENT '乳制品实付总金额（元），乳制品业务的实际支付总额',
  `dairy_cost_amt` DECIMAL(38,18) COMMENT '乳制品成本（元），乳制品业务的成本金额',
  `dairy_cash_real_cost_amt` DECIMAL(38,18) COMMENT '乳制品账期实付金额（元），乳制品账期业务的实际支付金额',
  `dairy_cust_cnt` BIGINT COMMENT '乳制品门店数，乳制品业务的门店数量',
  `nodairy_real_total_amt` DECIMAL(38,18) COMMENT '非乳制品实付总金额（元），非乳制品业务的实际支付总额',
  `nodairy_cost_amt` DECIMAL(38,18) COMMENT '非乳制品成本（元），非乳制品业务的成本金额',
  `nodairy_cash_cost_amt` DECIMAL(38,18) COMMENT '非乳制品账期实付金额（元），非乳制品账期业务的实际支付金额',
  `nodairy_cust_cnt` BIGINT COMMENT '非乳制品门店数，非乳制品业务的门店数量',
  `cash_real_total_amt` DECIMAL(38,18) COMMENT '账期实付金额（元），所有账期业务的实际支付总额',
  `nocash_real_total_amt` DECIMAL(38,18) COMMENT '非账期实付金额（元），非账期业务的实际支付总额',
  `replace_origin_total_amt` DECIMAL(38,18) COMMENT '代仓应付总金额（元），代仓业务的应付总额',
  `replace_real_total_amt` DECIMAL(38,18) COMMENT '代仓实付总金额（元），代仓业务的实际支付总额',
  `replace_cust_cnt` BIGINT COMMENT '代仓门店数，代仓业务的门店数量',
  `self_after_sale_amt` DECIMAL(38,18) COMMENT '自营售后金额（元），自营业务的售后金额',
  `replace_after_sale_amt` DECIMAL(38,18) COMMENT '代仓售后金额（元），代仓业务的售后金额'
) 
COMMENT '大客户团队汇总表，按客户团队和品牌别名的维度统计交易、履约、各业务线的关键指标数据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据分区日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment'='大客户团队业绩汇总表，包含交易、履约、各业务线（自营、省心送、鲜果、乳制品、非乳制品）的详细业绩指标',
  'columnar.nested.type'='true'
) 
LIFECYCLE 30;
```