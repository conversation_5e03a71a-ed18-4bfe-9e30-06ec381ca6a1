```sql
CREATE TABLE IF NOT EXISTS app_crm_merchant_sku_label_df(
    cust_id BIGINT COMMENT '商户ID，唯一标识一个商户，取值范围：2-7600',
    merchant_label STRING COMMENT '商品标签名称，如：ZILIULIU竹蔗冰糖糖浆、三麟苏打汽水、妙可蓝多马斯卡彭奶酪等',
    group_name STRING COMMENT '标签组名称，枚举值：自营品牌热门商品、全平台热门商品(其他)、全品类热门商品、全平台热门商品(鲜果)'
)
COMMENT 'CRM用户商品标签表，存储商户的商品标签信息，用于商品推荐和用户画像分析'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据所属日期')
STORED AS ALIORC
TBLPROPERTIES ('comment'='CRM用户商品标签表，包含商户的商品标签和标签组信息')
LIFECYCLE 30;
```