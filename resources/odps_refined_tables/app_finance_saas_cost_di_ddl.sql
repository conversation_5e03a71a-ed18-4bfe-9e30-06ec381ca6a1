CREATE TABLE IF NOT EXISTS app_finance_saas_cost_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd',
	`service_area` STRING COMMENT '大区：华中/华东/未知等',
	`warehouse_no` BIGINT COMMENT '库存仓ID，数值型标识，范围2-145',
	`warehouse_name` STRING COMMENT '库存仓名称，如长沙总仓、嘉兴总仓等',
	`category1` STRING COMMENT '商品一级类目：鲜果/乳制品/其他',
	`sell_cost_amt` DECIMAL(38,18) COMMENT '含税销售成本，货币金额',
	`damage_cost_amt` DECIMAL(38,18) COMMENT '含税货损成本，货币金额',
	`sample_cost_amt` DECIMAL(38,18) COMMENT '含税出样成本，货币金额',
	`stocktake_cost_amt` DECIMAL(38,18) COMMENT '含税盘盈盘亏成本，货币金额',
	`sell_cost_amt_notax` DECIMAL(38,18) COMMENT '不含税销售成本，货币金额',
	`damage_cost_amt_notax` DECIMAL(38,18) COMMENT '不含税货损成本，货币金额',
	`sample_cost_amt_notax` DECIMAL(38,18) COMMENT '不含税出样成本，货币金额',
	`stocktake_cost_amt_notax` DECIMAL(38,18) COMMENT '不含税盘盈盘亏成本，货币金额'
) 
COMMENT '财务口径收入数据表，记录各区域仓库的商品成本数据，包含含税和不含税成本明细'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='财务成本数据表，用于财务分析和成本核算') 
LIFECYCLE 30;