CREATE TABLE IF NOT EXISTS app_crm_follow_record_detail_df(
	`date` STRING COMMENT '拜访日期，格式：yyyyMMdd',
	`month` STRING COMMENT '拜访月份，格式：yyyyMM',
	`follow_up_record_id` BIGINT COMMENT '拜访记录ID，唯一标识每次拜访',
	`add_time` DATETIME COMMENT '拜访时间，年月日时分秒格式',
	`follow_up_way` STRING COMMENT '拜访方式，枚举值：有效拜访、普通上门拜访、普通拜访-电话&微信等',
	`visit_objective` STRING COMMENT '拜访目的，枚举值：客户维护、催月活等',
	`bd_id` BIGINT COMMENT '销售ID，销售人员唯一标识',
	`bd_name` STRING COMMENT '销售姓名',
	`m1_name` STRING COMMENT 'M1管理者（销售主管）姓名，即BD的直接上级',
	`m2_name` STRING COMMENT 'M2管理者（销售经理）姓名，即M1的直接上级',
	`m3_name` STRING COMMENT 'M3管理者（销售总监）姓名，即M2的直接上级',
	`zone_name` STRING COMMENT '销售区域，如：苏州、上海等',
	`cust_id` BIGINT COMMENT '客户ID，客户唯一标识',
	`cust_name` STRING COMMENT '客户名称',
	`register_city` STRING COMMENT '注册城市',
	`time_type` STRING COMMENT '拜访时间分段，枚举值：0-7点、13-21点、22-23点等',
	`is_compliant` STRING COMMENT '是否合规，枚举值：是、否',
	`is_abnormal` STRING COMMENT '是否异常，枚举值：是、否',
	`is_2hours_interval` STRING COMMENT '是否间隔2小时，枚举值：<120（小于120分钟）、>=120（大于等于120分钟）',
	`condition` STRING COMMENT '跟进情况描述，记录拜访过程中的具体情况',
	`expected_content` STRING COMMENT '期望内容，拜访前预期的沟通内容',
	`feedback` STRING COMMENT '客户反馈，拜访后客户的反馈信息',
	`is_escort` STRING COMMENT '是否陪访，枚举值：是、否',
	`escort_name` STRING COMMENT '陪访人姓名',
	`rn` BIGINT COMMENT '拜访顺序，当天拜访客户的顺序编号',
	`next_add_time` DATETIME COMMENT '下一个客户拜访时间，年月日时分秒格式',
	`min_add_time` DATETIME COMMENT '最早出勤时间，年月日时分秒格式',
	`max_add_time` DATETIME COMMENT '最晚出勤时间，年月日时分秒格式',
	`visit_cust_cnt` BIGINT COMMENT 'BD当天拜访客户数',
	`effective_visit_cust_cnt` BIGINT COMMENT 'BD当天上门拜访有效客户数',
	`cust_visit_cnt_7d` BIGINT COMMENT '该客户近7天被拜访次数',
	`is_paid` STRING COMMENT '该客户拜访后7天内是否购买，枚举值：是、否',
	`dlv_profit_group` STRING COMMENT '履约后利润分层(大类)，枚举值：A、B、无等'
) 
COMMENT '销售拜访客户过程跟踪表，记录销售人员拜访客户的详细过程信息，包括拜访时间、方式、目的、客户反馈等关键业务数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：yyyyMMdd') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='销售拜访客户过程跟踪表，用于分析销售拜访行为和客户关系管理') 
LIFECYCLE 30;