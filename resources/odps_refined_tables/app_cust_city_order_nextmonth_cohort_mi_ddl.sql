CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_cust_city_order_nextmonth_cohort_mi` (
  `month` STRING COMMENT '月份，格式为yyyyMM，如202508表示2025年8月',
  `cust_team` STRING COMMENT '客户团队类型，枚举值：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户、批发',
  `cust_type` STRING COMMENT '客户行业类型，枚举值：面包蛋糕、西餐、咖啡、甜品冰淇淋、茶饮、其他',
  `register_province` STRING COMMENT '客户注册时所在省份',
  `register_city` STRING COMMENT '客户注册时所在城市',
  `first_order_cust_cnt` BIGINT COMMENT '当月首次购买客户数量',
  `first_order_cohort_cust_cnt` BIGINT COMMENT '当月首次购买客户中次月复购的客户数量',
  `notfirst_order_cust_cnt` BIGINT COMMENT '当月非首次购买客户数量',
  `notfirst_order_cohort_cust_cnt` BIGINT COMMENT '当月非首次购买客户中次月复购的客户数量'
)
COMMENT '客户城市回购分析表，按月份、客户团队类型、客户行业类型、注册省市维度统计客户回购行为'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '客户城市回购分析表，统计各维度下客户的首购和复购行为数据',
  'lifecycle' = '30'
);