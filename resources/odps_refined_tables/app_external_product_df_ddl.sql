CREATE TABLE IF NOT EXISTS app_external_product_df(
	goods_name STRING COMMENT '货品名称，如：王婆牌宁夏无籽麒麟西瓜4头36~44斤B级',
	baby_name STRING COMMENT 'baby名称，通常与货品名称相同',
	skucode STRING COMMENT 'sku编码，商品唯一标识，如：1012568544839',
	specification STRING COMMENT '规格描述，如：约39斤、50-60等',
	unit STRING COMMENT '单位，枚举类型：PIECE-件',
	volume STRING COMMENT '体积，单位为立方厘米或其他体积单位',
	net_weight DECIMAL(38,18) COMMENT '净重，单位为千克',
	gross_weight DECIMAL(38,18) COMMENT '毛重，单位为千克',
	status STRING COMMENT '状态，枚举类型：ALREADY_UP-已上架',
	standard_price DECIMAL(38,18) COMMENT '标准价格，单位为元',
	activity_price DECIMAL(38,18) COMMENT '活动价格，单位为元',
	single_limited STRING COMMENT '单次限购数量',
	attalisticon STRING COMMENT '图标URL地址',
	seller_type STRING COMMENT '售卖类型，枚举类型：BUYER-买家',
	length DECIMAL(38,18) COMMENT '长，单位为厘米',
	width DECIMAL(38,18) COMMENT '宽，单位为厘米',
	height DECIMAL(38,18) COMMENT '高，单位为厘米',
	back_category_id BIGINT COMMENT '后台类目id，取值范围：506-7864',
	back_category_code STRING COMMENT '后台类目编码，层级结构如：/1/500/637/640/675/',
	back_category_name STRING COMMENT '后台类目名称，如：麒麟西瓜、蜜桔等',
	goods_type STRING COMMENT '货品类型，枚举类型：ORDINARY-普通商品',
	interval_status STRING COMMENT '内部状态，枚举类型：DISABLED-禁用',
	goods_prop_detail_list STRING COMMENT '属性信息，JSON格式的属性详情',
	competitor STRING COMMENT '竞对平台名称，如：标果-杭州',
	category_name STRING COMMENT '类目名称，如：未分类',
	goods_detail STRING COMMENT '详情描述，商品的详细说明信息'
) 
COMMENT '外部平台商品表，存储来自外部平台的商品基础信息和属性数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，yyyyMMdd格式日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='外部平台商品数据表，包含商品基本信息、价格、规格、类目等完整信息') 
LIFECYCLE 30;