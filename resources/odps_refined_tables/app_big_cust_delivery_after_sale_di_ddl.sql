CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_big_cust_delivery_after_sale_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务发生日期',
  `big_cust` STRING COMMENT '大客户名称，取值范围：喜茶、乐蔻、茶大将、苏阁、海底捞、到点咖啡等',
  `sku_type` STRING COMMENT '商品类型，取值范围：自营、代仓',
  `point_cnt` BIGINT COMMENT '配送点位数',
  `delay_point_cnt` BIGINT COMMENT '延迟配送点位数',
  `order_cnt` BIGINT COMMENT '订单数量',
  `origin_total_amt` DECIMAL(38,18) COMMENT '配送应付GMV（商品总价值）',
  `real_total_amt` DECIMAL(38,18) COMMENT '配送实付GMV（实际支付金额）',
  `delivary_amt` DECIMAL(38,18) COMMENT '配送费用',
  `out_sku_cnt` BIGINT COMMENT '出库商品件数',
  `short_sku_cnt` BIGINT COMMENT '缺货商品件数',
  `after_order_cnt` BIGINT COMMENT '售后订单数量',
  `after_total_amt` DECIMAL(38,18) COMMENT '售后总金额'
) 
COMMENT '大客户配送信息汇总表，包含大客户的配送数据、订单数据和售后数据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC  
TBLPROPERTIES ('comment'='大客户配送信息汇总表，用于分析大客户的配送效率、订单情况和售后问题') 
LIFECYCLE 30;