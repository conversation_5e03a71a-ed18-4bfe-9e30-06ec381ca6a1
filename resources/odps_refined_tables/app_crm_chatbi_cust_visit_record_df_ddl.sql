CREATE TABLE IF NOT EXISTS app_crm_chatbi_cust_visit_record_df(
	cust_id STRING COMMENT '被拜访的客户ID',
	bd_id STRING COMMENT '拜访执行人ID',
	bd_name STRING COMMENT '拜访执行人姓名',
	visit_type STRING COMMENT '拜访形式，枚举值：[普通上门拜访，普通拜访-企微，普通拜访-微信，普通拜访-电话，有效拜访]',
	visit_feedback STRING COMMENT 'BD填写的客户情况反馈',
	visit_objective STRING COMMENT '此次拜访目的，枚举值：0-拉新, 1-催月活, 2-客户维护, 3-拓品, 4-售后处理, 5-催省心送, 6-市场调研, 7-客户服务, 9-重复账号合并',
	visit_time STRING COMMENT '拜访时间，格式为年月日时分秒：YYYY-MM-DD HH:MM:SS'
) 
COMMENT '客户拜访记录信息表，包含销售员对客户的拜访记录（最近6个月的记录）'
PARTITIONED BY (ds STRING COMMENT '分区字段，业务日期，格式为年月日：YYYYMMDD') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='客户拜访记录信息表，包含销售员对客户的拜访记录（最近6个月的记录）') 
LIFECYCLE 7;