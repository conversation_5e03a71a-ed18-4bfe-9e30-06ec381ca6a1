CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_cust_city_order_category_mi` (
  `month` STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
  `cust_team` STRING COMMENT '客户团队类型，取值范围：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户、批发',
  `cust_type` STRING COMMENT '客户行业类型，取值范围：面包蛋糕、西餐、咖啡、甜品冰淇淋、茶饮、其他',
  `register_province` STRING COMMENT '注册时省份',
  `register_city` STRING COMMENT '注册时城市',
  `first_order_fruit_cust_cnt` BIGINT COMMENT '首单客户鲜果客户数',
  `first_order_fruit_order_cnt` BIGINT COMMENT '首单客户鲜果订单数',
  `first_order_fruit_real_amt` DECIMAL(38,18) COMMENT '首单客户鲜果下单实付金额',
  `first_order_fruit_order_days` BIGINT COMMENT '首单客户鲜果下单频次（天数）',
  `first_order_fruit_gross_rate` DECIMAL(38,18) COMMENT '首单客户鲜果毛利率',
  `first_order_fruit_gross` DECIMAL(38,18) COMMENT '首单客户鲜果实付毛利润',
  `first_order_dairy_cust_cnt` BIGINT COMMENT '首单客户乳品客户数',
  `first_order_dairy_order_cnt` BIGINT COMMENT '首单客户乳品订单数',
  `first_order_dairy_real_amt` DECIMAL(38,18) COMMENT '首单客户乳品下单实付金额',
  `first_order_dairy_order_days` BIGINT COMMENT '首单客户乳品下单频次（天数）',
  `first_order_dairy_gross_rate` DECIMAL(38,18) COMMENT '首单客户乳品毛利率',
  `first_order_dairy_gross` DECIMAL(38,18) COMMENT '首单客户乳品实付毛利润',
  `first_order_other_cust_cnt` BIGINT COMMENT '首单客户其他客户数',
  `first_order_other_order_cnt` BIGINT COMMENT '首单客户其他订单数',
  `first_order_other_real_amt` DECIMAL(38,18) COMMENT '首单客户其他下单实付金额',
  `first_order_other_order_days` BIGINT COMMENT '首单客户其他下单频次（天数）',
  `first_order_other_gross_rate` DECIMAL(38,18) COMMENT '首单客户其他毛利率',
  `first_order_other_gross` DECIMAL(38,18) COMMENT '首单客户其他实付毛利润',
  `first_order_self_cust_cnt` BIGINT COMMENT '首单客户自营品牌客户数',
  `first_order_self_order_cnt` BIGINT COMMENT '首单客户自营品牌订单数',
  `first_order_self_real_amt` DECIMAL(38,18) COMMENT '首单客户自营品牌下单实付金额',
  `first_order_self_order_days` BIGINT COMMENT '首单客户自营品牌下单频次（天数）',
  `first_order_self_gross_rate` DECIMAL(38,18) COMMENT '首单客户自营品牌毛利率',
  `first_order_self_gross` DECIMAL(38,18) COMMENT '首单客户自营品牌实付毛利润',
  `notfirst_order_fruit_cust_cnt` BIGINT COMMENT '非首单客户鲜果客户数',
  `notfirst_order_fruit_order_cnt` BIGINT COMMENT '非首单客户鲜果订单数',
  `notfirst_order_fruit_real_amt` DECIMAL(38,18) COMMENT '非首单客户鲜果下单实付金额',
  `notfirst_order_fruit_order_days` BIGINT COMMENT '非首单客户鲜果下单频次（天数）',
  `notfirst_order_fruit_gross_rate` DECIMAL(38,18) COMMENT '非首单客户鲜果毛利率',
  `notfirst_order_fruit_gross` DECIMAL(38,18) COMMENT '非首单客户鲜果实付毛利润',
  `notfirst_order_dairy_cust_cnt` BIGINT COMMENT '非首单客户乳品客户数',
  `notfirst_order_dairy_order_cnt` BIGINT COMMENT '非首单客户乳品订单数',
  `notfirst_order_dairy_real_amt` DECIMAL(38,18) COMMENT '非首单客户乳品下单实付金额',
  `notfirst_order_dairy_order_days` BIGINT COMMENT '非首单客户乳品下单频次（天数）',
  `notfirst_order_dairy_gross_rate` DECIMAL(38,18) COMMENT '非首单客户乳品毛利率',
  `notfirst_order_dairy_gross` DECIMAL(38,18) COMMENT '非首单客户乳品实付毛利润',
  `notfirst_order_other_cust_cnt` BIGINT COMMENT '非首单客户其他客户数',
  `notfirst_order_other_order_cnt` BIGINT COMMENT '非首单客户其他订单数',
  `notfirst_order_other_real_amt` DECIMAL(38,18) COMMENT '非首单客户其他下单实付金额',
  `notfirst_order_other_order_days` BIGINT COMMENT '非首单客户其他下单频次（天数）',
  `notfirst_order_other_gross_rate` DECIMAL(38,18) COMMENT '非首单客户其他毛利率',
  `notfirst_order_other_gross` DECIMAL(38,18) COMMENT '非首单客户其他实付毛利润',
  `notfirst_order_self_cust_cnt` BIGINT COMMENT '非首单客户自营品牌客户数',
  `notfirst_order_self_order_cnt` BIGINT COMMENT '非首单客户自营品牌订单数',
  `notfirst_order_self_real_amt` DECIMAL(38,18) COMMENT '非首单客户自营品牌下单实付金额',
  `notfirst_order_self_order_days` BIGINT COMMENT '非首单客户自营品牌下单频次（天数）',
  `notfirst_order_self_gross_rate` DECIMAL(38,18) COMMENT '非首单客户自营品牌毛利率',
  `notfirst_order_self_gross` DECIMAL(38,18) COMMENT '非首单客户自营品牌实付毛利润',
  `first_order_order_days` BIGINT COMMENT '首单客户总下单频次（天数）',
  `notfirst_order_order_days` BIGINT COMMENT '非首单客户总下单频次（天数）'
) 
COMMENT '客户分品类交易表，按客户团队类型、行业类型、注册地域维度统计首单和非首单客户的各品类交易数据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '客户分品类交易统计表',
  'last_data_modified_time' = '2025-09-18 03:04:56'
)
LIFECYCLE 30;