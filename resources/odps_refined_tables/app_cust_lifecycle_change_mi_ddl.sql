CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_cust_lifecycle_change_mi` (
  `month` STRING COMMENT '月份，格式为yyyyMM，如202508表示2025年8月',
  `cust_team` STRING COMMENT '客户团队类型，取值范围：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户',
  `cust_type` STRING COMMENT '客户行业类型，取值范围：面包蛋糕、西餐、咖啡、甜品冰淇淋、茶饮、其他',
  `register_province` STRING COMMENT '注册时省份名称',
  `life_cycle_detail_begin` STRING COMMENT '当月生命周期标签，如A2、A3、B2、L1、L2等',
  `life_cycle_detail_last` STRING COMMENT '次月生命周期标签，如A2、A3、B2、L1、L2等',
  `cust_cnt` BIGINT COMMENT '客户数量，统计客户等级流转的数量'
)
COMMENT '客户等级流转表，记录客户在不同生命周期标签之间的流转情况'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '客户等级流转分析表，用于分析客户生命周期变化趋势',
  'lifecycle' = '30'
);