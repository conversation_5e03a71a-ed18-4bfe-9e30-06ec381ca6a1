CREATE TABLE IF NOT EXISTS app_chatbi_search_analysis_df(
	`cust_id` STRING COMMENT '客户ID，唯一标识客户',
	`cust_name` STRING COMMENT '客户名称',
	`cust_type` STRING COMMENT '客户类型，枚举值：烘焙、茶饮、面包蛋糕、其他等',
	`query_list` STRING COMMENT '搜索词列表，以英文逗号分隔，以逗号结尾',
	`sku_viewed` BIGINT COMMENT '客户总计搜索的SKU数量，统计范围：正整数',
	`sku_clicked` BIGINT COMMENT '客户总计点击的SKU数量，统计范围：非负整数',
	`searched_date_range` STRING COMMENT '客户有搜索行为的日期时间段，格式：yyyyMMdd~yyyyMMdd',
	`last_searched_date` STRING COMMENT '客户最后一次搜索日期，格式：yyyyMMdd'
)
COMMENT '客户的搜索分析汇总表，以客户ID作为维度，包含客户搜索行为和商品交互统计'
PARTITIONED BY (
	`ds` STRING NOT NULL COMMENT '分区字段，格式：yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
	'comment' = '客户搜索行为分析表，记录客户搜索词、SKU浏览和点击统计',
	'columnar.nested.type' = 'true'
)
LIFECYCLE 7;