CREATE TABLE IF NOT EXISTS app_self_cust_delivery_kpi_wi(
	year STRING COMMENT '年份，格式：YYYY',
	week_of_year STRING COMMENT '一年中的第几周，范围：1-53',
	monday STRING COMMENT '周一日期，格式：YYYYMMDD',
	sunday STRING COMMENT '周日日期，格式：YYYYMMDD',
	cust_team STRING COMMENT '客户团队类型，枚举值：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户',
	origin_total_amt DECIMAL(38,18) COMMENT '原始总金额（元）',
	real_total_amt DECIMAL(38,18) COMMENT '实际总金额（元）',
	cost_amt DECIMAL(38,18) COMMENT '成本金额（元）',
	timing_origin_total_amt DECIMAL(38,18) COMMENT '省心送原始总金额（元）',
	timing_real_total_amt DECIMAL(38,18) COMMENT '省心送实际总金额（元）',
	cust_cnt BIGINT COMMENT '客户数量',
	order_cnt BIGINT COMMENT '订单数量',
	point_cnt BIGINT COMMENT '点位数量',
	day_point_cnt BIGINT COMMENT '日均点位数量',
	sku_cnt BIGINT COMMENT 'SKU数量',
	delivery_amt DECIMAL(38,18) COMMENT '运费金额（元）',
	after_sale_received_amt DECIMAL(38,18) COMMENT '已到货售后总金额（元）',
	storage_amt DECIMAL(38,18) COMMENT '仓储成本金额（元）',
	arterial_roads_amt DECIMAL(38,18) COMMENT '干线成本金额（元）',
	deliver_amt DECIMAL(38,18) COMMENT '配送成本金额（元）',
	self_picked_amt DECIMAL(38,18) COMMENT '采购自提成本金额（元）',
	other_amt DECIMAL(38,18) COMMENT '其他成本金额（元）',
	allocation_amt DECIMAL(38,18) COMMENT '调拨成本金额（元）'
) 
COMMENT '履约口径KPI指标日汇总表（自营业务），按周维度统计各项业务指标'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：YYYYMMDD，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='自营业务履约口径KPI指标周度汇总表，包含收入、成本、客户、订单、点位等核心业务指标') 
LIFECYCLE 30;