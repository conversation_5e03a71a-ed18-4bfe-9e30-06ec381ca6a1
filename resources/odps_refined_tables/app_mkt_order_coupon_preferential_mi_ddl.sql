CREATE TABLE IF NOT EXISTS app_mkt_order_coupon_preferential_mi(
	`month` STRING COMMENT '月份，格式：yyyyMM',
	`city_id` BIGINT COMMENT '运营服务区ID',
	`city_name` STRING COMMENT '运营服务区名称',
	`large_area_id` BIGINT COMMENT '运营服务大区ID',
	`large_area_name` STRING COMMENT '运营服务大区名称',
	`cust_team` STRING COMMENT '客户团队类型，枚举：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户',
	`cust_type` STRING COMMENT '客户类型，枚举：面包蛋糕,西餐,咖啡,甜品冰淇淋,茶饮,其他',
	`life_cycle_detail` STRING COMMENT '生命周期标签（细），枚举：S1,S2,A1等',
	`coupon_group` STRING COMMENT '优惠券分组，枚举：行业活动券,售后补偿券,区域拉新券,平台活动券等',
	`issued_cust_cnt` BIGINT COMMENT '发券人数（发放+领取）',
	`receive_cust_cnt` BIGINT COMMENT '领取人数',
	`used_cust_cnt` BIGINT COMMENT '使用人数',
	`used_coupon_amt` DECIMAL(38,18) COMMENT '使用优惠券金额',
	`origin_total_amt` DECIMAL(38,18) COMMENT '使用优惠券订单应付金额',
	`real_total_amt` DECIMAL(38,18) COMMENT '使用优惠券订单实付金额',
	`issued_used_cust_cnt` BIGINT COMMENT '当月领取且使用人数',
	`issued_used_coupon_amt` DECIMAL(38,18) COMMENT '当月领取且使用优惠券金额',
	`issued_origin_total_amt` DECIMAL(38,18) COMMENT '当月领取且使用优惠券订单应付金额',
	`issued_real_total_amt` DECIMAL(38,18) COMMENT '当月领取且使用优惠券订单实付金额'
) 
COMMENT '优惠券使用明细月表，记录各维度下优惠券的发放、领取、使用情况统计'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：yyyyMMdd') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='优惠券使用明细月表，按天分区存储月度优惠券使用统计数据') 
LIFECYCLE 30;