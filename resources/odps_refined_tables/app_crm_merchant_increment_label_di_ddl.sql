CREATE TABLE IF NOT EXISTS app_crm_merchant_increment_label_di(
	`cust_id` BIGINT COMMENT '商户ID，唯一标识一个商户',
	`merchant_label` STRING COMMENT '客户标签，如：月活老客户、活跃客户、月活新客户等',
	`unionid` STRING COMMENT '微信unionid，用户的微信唯一标识',
	`type` BIGINT COMMENT '变动类型：0-新增标签，1-删除标签，2-标签更新',
	`day_tag` STRING COMMENT '数据日期，格式为yyyyMMdd，表示标签对应的业务日期',
	`group_name` STRING COMMENT '分组名称，如：企微标签组'
) 
COMMENT '门店增量标签更新表，记录商户标签的新增、删除和更新操作'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据处理的日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='门店增量标签更新表，用于记录商户标签的变动历史') 
LIFECYCLE 30;