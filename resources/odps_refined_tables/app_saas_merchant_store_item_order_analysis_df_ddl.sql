CREATE TABLE IF NOT EXISTS app_saas_merchant_store_item_order_analysis_df(
	tenant_id BIGINT COMMENT '租户ID，取值范围：2-10',
	type BIGINT COMMENT '统计周期类型：1-周，2-月，3-季度',
	time_tag STRING COMMENT '时间标签，格式：yyyyMMdd，表示统计周期的起始日期',
	item_id BIGINT COMMENT '商品ID，取值范围：1-1386',
	title STRING COMMENT '商品名称',
	store_id BIGINT COMMENT '门店ID，取值范围：1-2365',
	average_order_period DECIMAL(38,18) COMMENT '平均订货周期（天）',
	average_order_period_last_period DECIMAL(38,18) COMMENT '上周期平均订货周期（天）',
	average_order_period_upper_period DECIMAL(38,18) COMMENT '平均订货周期环比变化率（百分数）',
	order_amount BIGINT COMMENT '订货数量，取值范围：1-437',
	order_amount_last_period BIGINT COMMENT '上周期订货数量，取值范围：0-350',
	order_amount_upper_period DECIMAL(38,18) COMMENT '订货数量环比变化率（百分数）',
	order_price DECIMAL(38,18) COMMENT '订货金额',
	order_price_last_period DECIMAL(38,18) COMMENT '上周期订货金额',
	order_price_upper_period DECIMAL(38,18) COMMENT '订货金额环比变化率（百分数）',
	last_order_time STRING COMMENT '最后订货日期，格式：yyyy-MM-dd，表示年月日',
	last_order_amount BIGINT COMMENT '最后订货数量，取值范围：1-250',
	last_order_price DECIMAL(38,18) COMMENT '最后订货金额'
) 
COMMENT 'SaaS门店商品订货分析表，包含商品订货的周期、数量、金额等统计分析指标，支持周、月、季度不同维度的分析'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='SaaS门店商品订货分析结果表，用于分析商品订货趋势和周期性规律') 
LIFECYCLE 30;