CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_crm_wecom_conversation_visit_detail_di` (
  `bd_id` BIGINT COMMENT 'BD ID，销售人员唯一标识',
  `bd_user_id` STRING COMMENT 'BD用户ID，企业微信中的用户标识',
  `bd_name` STRING COMMENT 'BD姓名，销售人员姓名',
  `cust_external_user_id` STRING COMMENT '客户外部用户ID，企业微信中客户的唯一标识',
  `m_id` BIGINT COMMENT 'M ID，管理者ID，销售主管或经理的唯一标识',
  `cust_name` STRING COMMENT '客户名称',
  `conversation_list` STRING COMMENT '对话内容列表，JSON格式存储对话记录',
  `image_list` STRING COMMENT '图片列表，JSON格式存储对话中发送的图片链接',
  `date` STRING COMMENT '日期，格式为yyyy-MM-dd',
  `conversation_start_time` DATETIME COMMENT '对话开始时间，格式为年月日时分秒',
  `conversation_end_time` DATETIME COMMENT '对话结束时间，格式为年月日时分秒',
  `is_valid_visit` BOOLEAN COMMENT '是否有效拜访：true-有效拜访，false-无效拜访',
  `create_time` STRING COMMENT '记录创建时间，格式为yyyy-MM-dd HH:mm:ss'
)
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES ('comment' = '企业微信对话拜访明细表，记录销售人员与客户在企业微信中的对话详情和拜访有效性判断')
LIFECYCLE 365;