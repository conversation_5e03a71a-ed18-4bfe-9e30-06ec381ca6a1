CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_finance_saas_after_sale_details_di` (
  `tenant_id` BIGINT COMMENT '租户ID，唯一标识一个租户',
  `tenant_name` STRING COMMENT '租户名称',
  `company_name` STRING COMMENT '公司名称',
  `after_sale_order_no` STRING COMMENT '售后单号，售后业务的唯一标识',
  `order_no` STRING COMMENT '订单编号，原始订单的唯一标识',
  `order_item_id` STRING COMMENT '订单项编号，订单中具体商品的标识',
  `province` STRING COMMENT '省份',
  `city` STRING COMMENT '城市',
  `area` STRING COMMENT '区域/区县',
  `pay_type` STRING COMMENT '支付方式枚举：微信支付,账期,余额支付,支付宝支付',
  `sku` STRING COMMENT 'SKU编码，商品库存单位唯一标识',
  `pd_name` STRING COMMENT '商品名称',
  `category1` STRING COMMENT '商品一级类目',
  `tax_rate` DECIMAL(38,18) COMMENT '税率，小数形式表示',
  `finish_time` DATETIME COMMENT '售后完结时间，格式为年月日时分秒',
  `after_sale_amt` DECIMAL(38,18) COMMENT '售后退款金额（含税）',
  `after_sale_amt_notax` DECIMAL(38,18) COMMENT '售后退款金额（不含税）',
  `after_sale_amt_tax` DECIMAL(38,18) COMMENT '收入税额',
  `after_sale_in_sku_cnt` BIGINT COMMENT '售后入库数量',
  `after_sale_in_cost` DECIMAL(38,18) COMMENT '售后入库成本金额（含税）',
  `after_sale_in_cost_notax` DECIMAL(38,18) COMMENT '售后入库成本金额（不含税）',
  `after_sale_add_sku_cnt` BIGINT COMMENT '售后补发数量',
  `after_sale_add_cost` DECIMAL(38,18) COMMENT '售后补发成本金额（含税）',
  `after_sale_add_cost_notax` DECIMAL(38,18) COMMENT '售后补发成本金额（不含税）',
  `cust_group` STRING COMMENT '客户团队类型枚举：平台客户、大客户',
  `sub_type` STRING COMMENT '商品二级性质枚举：代销不入仓、代销入仓、经销',
  `service_type` STRING COMMENT '售后服务类型',
  `service_area` STRING COMMENT '大区',
  `store_no` BIGINT COMMENT '配送仓ID',
  `warehouse_no` BIGINT COMMENT '库存仓ID',
  `settle_type` STRING COMMENT '结算类型'
)
COMMENT 'SaaS业财一体化售后明细表，包含售后业务的财务和业务明细数据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = 'SaaS业财一体化售后明细表',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;