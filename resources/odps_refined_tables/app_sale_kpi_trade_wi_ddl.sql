```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_sale_kpi_trade_wi` (
  `year` STRING COMMENT '年份，格式：YYYY',
  `week_of_year` BIGINT COMMENT '周数，取值范围：1-53',
  `monday` STRING COMMENT '周一日期，格式：YYYYMMDD（年月日）',
  `sunday` STRING COMMENT '周日日期，格式：YYYYMMDD（年月日）',
  `order_origin_total_amt` DECIMAL(38,18) COMMENT '交易应付总金额（元），订单原始金额总计',
  `order_real_total_amt` DECIMAL(38,18) COMMENT '交易实付总金额（元），客户实际支付金额总计',
  `order_cust_cnt` BIGINT COMMENT '交易客户数，产生交易订单的客户数量',
  `order_cust_arpu` DECIMAL(38,18) COMMENT 'ARPU值（元/客户），计算公式：交易应付总金额/交易客户数',
  `order_cnt` BIGINT COMMENT '交易订单数，所有交易订单的总数量',
  `delivery_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额（元），履约订单原始金额总计',
  `delivery_real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额（元），履约订单实际支付金额总计',
  `delivery_cust_cnt` BIGINT COMMENT '履约活跃客户数，参与履约的客户数量',
  `delivery_origin_profit` DECIMAL(38,18) COMMENT '履约应付毛利润（元），基于原始金额计算的毛利润',
  `delivery_real_profit` DECIMAL(38,18) COMMENT '履约实付毛利润（元），基于实付金额计算的毛利润',
  `delivery_after_profit` DECIMAL(38,18) COMMENT '履约后毛利润（元），扣除各项费用后的净利润',
  `delivery_days_avg` DECIMAL(38,18) COMMENT '履约频次，平均每个客户的履约天数',
  `delivery_point_cnt` BIGINT COMMENT '履约点位数，参与履约的网点或门店数量',
  `delivery_amt` DECIMAL(38,18) COMMENT '自营履约费用（元），自营业务的履约成本',
  `new_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '新客履约应付总金额（元），新客户履约订单原始金额',
  `new_delivery_real_total_amt` DECIMAL(38,18) COMMENT '新客履约实付总金额（元），新客户履约订单实际支付金额',
  `new_delivery_cust_cnt` BIGINT COMMENT '新客履约活跃客户数，新客户中参与履约的数量',
  `new_delivery_real_profit` DECIMAL(38,18) COMMENT '新客履约实付毛利润（元），新客户履约产生的毛利润',
  `old_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '老客履约应付总金额（元），老客户履约订单原始金额',
  `old_delivery_real_total_amt` DECIMAL(38,18) COMMENT '老客履约实付总金额（元），老客户履约订单实际支付金额',
  `old_delivery_cust_cnt` BIGINT COMMENT '老客履约活跃客户数，老客户中参与履约的数量',
  `old_delivery_real_profit` DECIMAL(38,18) COMMENT '老客履约实付毛利润（元），老客户履约产生的毛利润',
  `order_sku_cnt` BIGINT COMMENT '交易SKU款数，交易订单中涉及的SKU种类数量',
  `order_sku_weight` DECIMAL(38,18) COMMENT '交易SKU总重量（KG），所有交易商品的总重量',
  `delivery_sku_cnt` BIGINT COMMENT '履约SKU款数，履约订单中涉及的SKU种类数量',
  `delivery_sku_weight` DECIMAL(38,18) COMMENT '履约SKU总重量（KG），所有履约商品的总重量'
) 
COMMENT '销售KPI指标汇总表，按周统计的销售关键绩效指标数据，包含交易和履约相关的金额、客户数、利润等核心指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：YYYYMMDD（年月日），表示数据日期'
)
STORED AS ALIORC  
TBLPROPERTIES (
  'comment' = '销售KPI指标周度汇总表',
  'lifecycle' = '30'
);
```