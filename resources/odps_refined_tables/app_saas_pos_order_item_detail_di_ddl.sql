```sql
CREATE TABLE IF NOT EXISTS app_saas_pos_order_item_detail_di(
    channel_type BIGINT COMMENT '渠道类型：1=美团，2=饿了么，3=其他（根据数据样本显示当前只有3=其他）',
    tenant_id BIGINT COMMENT '租户ID',
    pos_order_id BIGINT COMMENT 'POS订单ID',
    pos_order_item_id BIGINT COMMENT 'POS子订单ID',
    out_store_code STRING COMMENT '外部系统门店编码',
    merchant_store_code STRING COMMENT '帆台门店编码',
    out_item_code STRING COMMENT '外部系统物料编码',
    market_item_id BIGINT COMMENT '帆台商品ID（数据样本显示为None，可能为空值）',
    available_date DATETIME COMMENT '生效日期，格式为年月日时分秒'
)
COMMENT 'POS订单物料明细表（日分区），存储POS系统的订单物料详细信息'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期')
STORED AS ALIORC
TBLPROPERTIES ('comment'='POS订单物料明细表，包含订单渠道、租户、门店、商品等维度信息')
LIFECYCLE 30;
```