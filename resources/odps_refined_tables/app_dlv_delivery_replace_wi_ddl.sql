CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_dlv_delivery_replace_wi` (
  `year` STRING COMMENT '年份，格式为yyyy',
  `week_of_year` BIGINT COMMENT '周数，取值范围：1-53',
  `monday` STRING COMMENT '周一日期，格式为yyyyMMdd（年月日）',
  `sunday` STRING COMMENT '周日日期，格式为yyyyMMdd（年月日）',
  `origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额（元）',
  `real_total_amt` DECIMAL(38,18) COMMENT '实付总金额（元）',
  `deliver_total_weight` DECIMAL(38,18) COMMENT '配送总重量（kg）',
  `brand_cnt` BIGINT COMMENT '客户数（品牌数量）',
  `cust_cnt` BIGINT COMMENT '门店数（客户门店数量）',
  `order_cnt` BIGINT COMMENT '订单数',
  `data_source` STRING COMMENT '数据来源，枚举值：鲜沐、SaaS'
) 
COMMENT '履约代仓日汇总表，记录代仓配送业务的每日汇总数据，包括金额、重量、客户数、订单数等指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd（年月日）'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '履约代仓业务日度汇总统计表',
  'lifecycle' = '30'
);