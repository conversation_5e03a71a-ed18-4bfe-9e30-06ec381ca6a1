CREATE TABLE IF NOT EXISTS app_estimated_sku_warehouse_record_df(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示年月日',
	`warehouse_no` BIGINT COMMENT '库存仓编号，取值范围：2-155',
	`sku_id` STRING COMMENT 'SKU编号，商品最小库存单位标识',
	`spu_name` STRING COMMENT '商品名称，标准产品单元名称',
	`quantity` BIGINT COMMENT '销量，取值范围：1-3425，50%分位数为2，75%分位数为6'
) 
COMMENT '销量预估明细表，包含商品在各仓库的每日销量预测数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='销量预估明细表，用于商品销量预测分析和库存管理') 
LIFECYCLE 3;