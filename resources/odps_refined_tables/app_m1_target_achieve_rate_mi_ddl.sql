CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_m1_target_achieve_rate_mi` (
  `months` STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
  `m3` STRING COMMENT '最新M3管理者姓名',
  `m2` STRING COMMENT '最新M2管理者姓名',
  `m1` STRING COMMENT '最新M1管理者姓名',
  `zone_name` STRING COMMENT '销售区域名称',
  `no_at_gmv_target` DECIMAL(38,18) COMMENT '非ATGMV目标金额',
  `no_at_gmv_achieve_amt` DECIMAL(38,18) COMMENT '非ATGMV实际达成金额',
  `no_at_gmv_achieve_rate` DECIMAL(38,18) COMMENT '非ATGMV达成率（实际达成金额/目标金额）',
  `no_at_gmv_score_num` DECIMAL(38,18) COMMENT '非ATGMV绩效得分',
  `fruit_gmv_target` DECIMAL(38,18) COMMENT '鲜果GMV目标金额',
  `fruit_gmv_achieve_amt` DECIMAL(38,18) COMMENT '鲜果GMV实际达成金额',
  `fruit_gmv_achieve_rate` DECIMAL(38,18) COMMENT '鲜果GMV达成率（实际达成金额/目标金额）',
  `fruit_gmv_score_num` DECIMAL(38,18) COMMENT '鲜果GMV绩效得分',
  `effective_cust_target` DECIMAL(38,18) COMMENT '有效月活客户数目标',
  `effect_cust_cnt` DECIMAL(38,18) COMMENT '有效月活客户数实际达成',
  `effect_cust_achieve_rate` DECIMAL(38,18) COMMENT '有效月活达成率（实际达成数/目标数）',
  `effect_cust_score_num` DECIMAL(38,18) COMMENT '有效月活绩效得分',
  `total_score_num` DECIMAL(38,18) COMMENT '综合绩效总得分'
)
COMMENT '销售M1维度目标达成绩效得分表，用于月度算薪，包含非ATGMV、鲜果GMV、有效月活等维度的目标达成情况和绩效得分'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '销售M1维度目标达成绩效得分_月度算薪',
  'lifecycle' = '30'
);