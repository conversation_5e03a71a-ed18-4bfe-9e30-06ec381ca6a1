CREATE TABLE IF NOT EXISTS app_finance_cash_after_sale_details_di(
	after_sale_order_id STRING COMMENT '售后单编号',
	order_no STRING COMMENT '订单编号',
	order_item_id BIGINT COMMENT '订单项编号',
	delivery_path_id BIGINT COMMENT 'delivery_path的ID',
	service_area STRING COMMENT '大区，取值范围：未知、华中、华东等',
	province STRING COMMENT '省',
	city STRING COMMENT '市',
	sku STRING COMMENT 'SKU',
	pd_name STRING COMMENT '商品名',
	category1 STRING COMMENT '商品一级类目，取值范围：鲜果等',
	tax_rate DECIMAL(38,18) COMMENT '税率',
	finish_time DATETIME COMMENT '售后完结时间，格式：年月日时分秒',
	after_sale_amt DECIMAL(38,18) COMMENT '售后退款金额（含税）',
	after_sale_amt_notax DECIMAL(38,18) COMMENT '售后退款金额（不含税）',
	after_sale_in_sku_cnt BIGINT COMMENT '售后入库数量',
	after_sale_in_cost DECIMAL(38,18) COMMENT '售后入库成本金额（含税）',
	after_sale_in_cost_notax DECIMAL(38,18) COMMENT '售后入库成本金额（不含税）',
	after_sale_add_sku_cnt BIGINT COMMENT '售后补发数量',
	after_sale_add_cost DECIMAL(38,18) COMMENT '售后补发成本金额（含税）',
	after_sale_add_cost_notax DECIMAL(38,18) COMMENT '售后补发成本金额（不含税）',
	cust_team STRING COMMENT '客户团队类型，取值范围：平台客户、大客户',
	handle_type STRING COMMENT '售后服务类型，取值范围：退款、退货退款等',
	after_sale_add_lack_sku_cnt BIGINT COMMENT '售后补发(缺货导致)商品数量',
	after_sale_add_lack_revenue_amt DECIMAL(38,18) COMMENT '售后补发(缺货导致)确认收入金额（含税）',
	after_sale_add_lack_revenue_amt_notax DECIMAL(38,18) COMMENT '售后补发(缺货导致)确认收入金额（不含税）',
	sub_type BIGINT COMMENT '商品二级性质，取值范围：1-自营-代销不入仓、2-自营-代销入仓、3-自营-经销、4-代仓-代仓',
	settle_type STRING COMMENT '结算类型'
) 
COMMENT '财务口径现结收入明细表，记录售后相关的财务结算明细数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，yyyyMMdd格式日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='财务口径现结收入明细表，包含售后订单的财务结算相关信息') 
LIFECYCLE 30;