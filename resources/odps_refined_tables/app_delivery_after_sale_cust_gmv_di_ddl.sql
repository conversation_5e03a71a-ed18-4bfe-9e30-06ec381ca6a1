CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_delivery_after_sale_cust_gmv_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd',
  `province` STRING COMMENT '注册时省份',
  `city` STRING COMMENT '注册时城市',
  `area` STRING COMMENT '注册时区域',
  `city_id` BIGINT COMMENT '运营服务ID，取值范围：1001-44269',
  `city_name` STRING COMMENT '运营服务名称',
  `large_area_id` BIGINT COMMENT '运营服务大区ID，取值范围：1-91',
  `large_area_name` STRING COMMENT '运营服务大区名称',
  `cust_type` STRING COMMENT '客户类型，枚举值：大客户等',
  `cust_team` STRING COMMENT '客户团队类型，枚举值：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户、批发',
  `sku_type` STRING COMMENT '商品类型，枚举值：自营、代仓、代售',
  `sku_id` STRING COMMENT '商品SKU',
  `spu_id` BIGINT COMMENT '商品PD_ID，取值范围：7-18866',
  `spu_name` STRING COMMENT '商品名称',
  `sku_disc` STRING COMMENT '商品规格描述',
  `category1` STRING COMMENT '一级分类，枚举值：鲜果等',
  `category2` STRING COMMENT '二级分类，枚举值：新鲜水果等',
  `category3` STRING COMMENT '三级分类，枚举值：柑果类、仁果类等',
  `category4` STRING COMMENT '四级分类，枚举值：柠檬、人参果等',
  `delivery_total_amt` DECIMAL(38,18) COMMENT '配送销售金额',
  `delivery_real_total_amt` DECIMAL(38,18) COMMENT '配送实付GMV',
  `coupon_amt` DECIMAL(38,18) COMMENT '优惠券金额',
  `after_sale_amt` DECIMAL(38,18) COMMENT '已到货售后金额',
  `store_no` BIGINT COMMENT '配送仓编号，取值范围：-1-154',
  `store_name` STRING COMMENT '配送仓名称',
  `warehouse_no` BIGINT COMMENT '库存仓编号，取值范围：-1-155',
  `warehouse_name` STRING COMMENT '库存仓名称',
  `brand_alias` STRING COMMENT '品牌名称',
  `delivery_origin_total_amt` DECIMAL(38,18) COMMENT '配送应付GMV',
  `delivery_after_sale_amt` DECIMAL(38,18) COMMENT '已到货售后金额(仓配责任)',
  `purchase_after_sale_amt` DECIMAL(38,18) COMMENT '已到货售后金额(采购责任)',
  `quality_after_sale_amt` DECIMAL(38,18) COMMENT '已到货售后金额(品控责任)',
  `purchase_quality_after_sale_amt` DECIMAL(38,18) COMMENT '已到货售后金额(采购品控责任)',
  `other_after_sale_amt` DECIMAL(38,18) COMMENT '已到货售后金额(其余责任)',
  `normal_after_sale_amt` DECIMAL(38,18) COMMENT '正常售后金额',
  `special_after_sale_amt` DECIMAL(38,18) COMMENT '特殊售后金额'
)
COMMENT '配送售后GMV数据表，记录配送相关的销售金额、实付金额、优惠券金额以及各类售后责任金额等业务指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '配送售后GMV明细表，用于分析配送业务的销售和售后数据',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;