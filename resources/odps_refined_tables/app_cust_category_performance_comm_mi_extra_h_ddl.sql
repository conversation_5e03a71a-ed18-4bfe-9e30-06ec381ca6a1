```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_cust_category_performance_comm_mi_extra_h` (
  `bd_id` BIGINT COMMENT 'BD ID，销售人员唯一标识',
  `bd_name` STRING COMMENT 'BD姓名，销售人员姓名',
  `bd_region` STRING COMMENT '大区，销售所属大区名称',
  `bd_work_zone` STRING COMMENT '区域，销售所属工作区域',
  `cust_id` BIGINT COMMENT '客户ID，客户唯一标识',
  `cust_name` STRING COMMENT '客户名称，客户公司或店铺名称',
  `cust_type` STRING COMMENT '客户类型：新增-新客户，存量-老客户',
  `cust_type_detail` STRING COMMENT '客户详细类型：上月履约（交易）-上月有交易记录，近2月有履约（交易）-近2个月有交易记录，近3月有履约（交易）-近3个月有交易记录，近4~6月有履约（交易）-近4-6个月有交易记录，近7~12月有履约（交易）-近7-12个月有交易记录，近365天无履约（交易）-近一年无交易记录',
  `category1` STRING COMMENT '一级类目，商品一级分类',
  `spu_group` STRING COMMENT '推广品类，具体推广的商品品类',
  `is_dlv_payment` BIGINT COMMENT '结算方式标识：1-履约结算，0-交易结算',
  `is_complete` BIGINT COMMENT '是否完成标识：1-是（本月履约件数>0），0-否',
  `dlv_origin_amt` DECIMAL(38,18) COMMENT '履约(交易)应付GMV，原始应付金额',
  `dlv_real_amt` DECIMAL(38,18) COMMENT '履约(交易)实付GMV，实际支付金额',
  `item_profit_amt` DECIMAL(38,18) COMMENT '自营商品毛利润，商品销售毛利',
  `dlv_sku_cnt` BIGINT COMMENT '履约(交易)件数，实际交易商品数量',
  `small_sku_cnt` DECIMAL(38,18) COMMENT '小规格履约(交易)件数，小规格商品交易数量',
  `big_sku_cnt` DECIMAL(38,18) COMMENT '大规格履约(交易)件数，大规格商品交易数量',
  `old_cust_comm` DECIMAL(38,18) COMMENT '存量推广佣金，老客户推广获得的佣金',
  `new_cust_comm` DECIMAL(38,18) COMMENT '新客推广佣金，新客户推广获得的佣金',
  `category_comm_amt` DECIMAL(38,18) COMMENT '品类推广佣金，按品类推广获得的佣金',
  `dlv_sku_cnt_today` DECIMAL(38,18) COMMENT '今日待履约数量，当天待完成的商品数量',
  `dlv_order_cnt_today` DECIMAL(38,18) COMMENT '今日交易待履约数量，当天交易待完成的商品数量',
  `dlv_other_cnt_today` DECIMAL(38,18) COMMENT '其余待履约数量，其他类型待完成的商品数量',
  `dlv_real_amt_today` DECIMAL(38,18) COMMENT '今日待履约实付金额，当天待完成的实际支付金额',
  `dlv_order_amt_today` DECIMAL(38,18) COMMENT '今日交易待履约实付金额，当天交易待完成的实际支付金额',
  `dlv_other_amt_today` DECIMAL(38,18) COMMENT '其余待履约实付金额，其他类型待完成的实际支付金额',
  `old_cust_comm_today` DECIMAL(38,18) COMMENT '今日存量推广佣金，当天老客户推广获得的佣金',
  `new_cust_comm_today` DECIMAL(38,18) COMMENT '今日新客推广佣金，当天新客户推广获得的佣金',
  `category_comm_amt_today` DECIMAL(38,18) COMMENT '今日品类推广佣金，当天按品类推广获得的佣金',
  `month_old_cust_comm` DECIMAL(38,18) COMMENT '本月存量推广佣金（离线+今日），当月老客户推广累计佣金',
  `month_new_cust_comm` DECIMAL(38,18) COMMENT '本月新客推广佣金（离线+今日），当月新客户推广累计佣金',
  `month_category_cust_comm` DECIMAL(38,18) COMMENT '本月品类推广佣金（离线+今日），当月品类推广累计佣金',
  `month_dlv_sku_cnt_today` DECIMAL(38,18) COMMENT '本月履约数量（离线+今日），当月累计履约商品数量',
  `month_dlv_real_amt_today` DECIMAL(38,18) COMMENT '本月履约实付金额（离线+今日），当月累计履约实际支付金额'
) 
COMMENT '客户MTD维度绩效表现表，记录客户在月度至今（Month-to-Date）维度的绩效表现数据，包括销售业绩、佣金计算、履约情况等指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment'='客户月度绩效表现明细表',
  'columnar.nested.type'='true'
)
LIFECYCLE 30;
```