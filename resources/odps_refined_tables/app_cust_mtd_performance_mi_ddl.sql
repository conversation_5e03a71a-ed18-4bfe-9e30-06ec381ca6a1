CREATE TABLE IF NOT EXISTS app_cust_mtd_performance_mi(
	last_bd_name STRING COMMENT '最新归属BD姓名',
	last_bd_id STRING COMMENT 'BD ID',
	bd_region STRING COMMENT '大区名称',
	bd_work_zone STRING COMMENT '区域名称',
	cust_id STRING COMMENT '客户ID',
	last_cust_name STRING COMMENT '客户名称',
	cust_dlv_type STRING COMMENT '客户类型：A-高价值客户，非A-普通客户',
	total_score_num DOUBLE COMMENT 'BD利润积分累计',
	bd_performance_rate DOUBLE COMMENT '利润积分系数',
	dlv_cust_cnt BIGINT COMMENT '履约客户数',
	cust_comm_amt DOUBLE COMMENT '客户数佣金',
	dlv_ori_amt DOUBLE COMMENT '履约应付GMV',
	dlv_real_amt DOUBLE COMMENT '履约实付GMV',
	item_profit_amt DOUBLE COMMENT '自营商品毛利润',
	dlv_real_amt_at DOUBLE COMMENT 'AT_履约实付金额',
	dlv_real_amt_expo DOUBLE COMMENT '流量品_履约实付金额',
	dlv_real_amt_profit DOUBLE COMMENT '利润品_履约实付金额',
	dlv_real_amt_normal DOUBLE COMMENT '常规品_履约实付金额',
	dlv_real_amt_fruit DOUBLE COMMENT '鲜果_履约实付金额',
	cate_group_score_num DOUBLE COMMENT '利润得分',
	dlv_spu_cnt BIGINT COMMENT '履约SPU数',
	more_than_spu_cnt BIGINT COMMENT '超额SPU数',
	more_than_spu_comm DOUBLE COMMENT '超额SPU数佣金',
	more_than_spu_cust BIGINT COMMENT '超额SPU客户数',
	total_comm_amt DOUBLE COMMENT '高价值客户佣金汇总',
	is_test_bd STRING COMMENT '是否测试BD：1-是，0-否',
	dlv_planned_spu_cnt BIGINT COMMENT '出库任务新增SPU数',
	dlv_planned_real_amt DOUBLE COMMENT '出库任务中新增履约金额',
	cust_value_lable STRING COMMENT '高价值客户标签：高价值客户，普通客户',
	arpu_target DOUBLE COMMENT '高价值ARPU标准',
	spu_target DOUBLE COMMENT '高价值SPU标准',
	arpu_comm_amt DOUBLE COMMENT '超ARPU激励金额'
) 
COMMENT '单客户MTD维度绩效表现表，记录客户维度的月度累计绩效数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，数据日期，格式为yyyyMMdd') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='单客户MTD维度绩效表现表，包含客户维度的利润积分、履约金额、佣金等绩效指标') 
LIFECYCLE 720;