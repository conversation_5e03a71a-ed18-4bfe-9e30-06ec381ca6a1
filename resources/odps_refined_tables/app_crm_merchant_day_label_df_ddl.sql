CREATE TABLE IF NOT EXISTS app_crm_merchant_day_label_df(
    cust_id BIGINT COMMENT '商户ID，唯一标识一个商户',
    merchant_label STRING COMMENT '客户标签，如：月活老客户、活跃客户、ZILIULIU竹蔗冰糖糖浆等',
    area_no BIGINT COMMENT '地区编号，用于标识商户所在地区',
    size STRING COMMENT '客户规模分类：单店-单点客户，1-大客户，2-大连锁，3-小连锁，4-单点',
    label_type BIGINT COMMENT '客户标签类型：1-客户属性及行为标签，2-商品标签'
)
COMMENT '客户标签表，记录商户的各类标签信息，包括客户属性标签和商品标签'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期')
STORED AS ALIORC
TBLPROPERTIES ('comment'='客户标签明细表，包含商户的基本标签信息和分类')
LIFECYCLE 30;