CREATE TABLE IF NOT EXISTS app_crm_sku_area_month_merchant_di(
	month_tag STRING COMMENT '月份标记，格式为yyyyMM，表示年月，如202509表示2025年9月',
	sku_id STRING COMMENT '商品SKU编号，唯一标识商品规格',
	area_no BIGINT COMMENT '运营区域编号，取值范围1001-44269，表示不同的运营区域',
	merchant_id_text STRING COMMENT '商户ID列表，使用英文逗号分隔多个商户ID'
) 
COMMENT '每个SKU每月下单商户ID表，记录各商品在各运营区域每月的下单商户信息'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日，如20250917表示2025年9月17日') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='商品SKU月度商户下单统计表，用于分析各商品在各区域的商户购买行为') 
LIFECYCLE 30;