CREATE TABLE IF NOT EXISTS app_crm_wecom_state_df(
	bd_id BIGINT COMMENT '销售员工ID，唯一标识一个销售人员',
	bd_name STRING COMMENT '销售员工姓名',
	parent_id BIGINT COMMENT '上级管理者ID，指向该销售的直属上级',
	parent_name STRING COMMENT '上级管理者姓名',
	job_state BIGINT COMMENT '在职状态：0-离职，1-在职',
	wecom_state BIGINT COMMENT '企业微信状态：1-已激活，2-已禁用，4-未激活，5-退出企业'
) 
COMMENT '销售团队在职状态及企业微信激活状态表，用于追踪销售人员的在职情况和企微账号状态'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='销售在职&企微激活状态表，包含销售人员的基本信息、层级关系和状态信息') 
LIFECYCLE 30;