```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_saas_merchant_store_purchase_day_di` (
  `tenant_id` BIGINT COMMENT '租户ID，唯一标识一个租户',
  `time_tag` STRING COMMENT '时间标签，格式为yyyyMMdd，表示年月日',
  `pay_type` BIGINT COMMENT '支付方式：1-现结，2-账期',
  `store_in_operation_num` BIGINT COMMENT '经营中门店总数',
  `direct_store_in_operation_num` BIGINT COMMENT '经营中直营门店数量',
  `join_store_in_operation_num` BIGINT COMMENT '经营中加盟门店数量',
  `managed_store_in_operation_num` BIGINT COMMENT '经营中托管门店数量',
  `purchased_store_num` BIGINT COMMENT '采购门店总数',
  `purchased_direct_store_num` BIGINT COMMENT '采购直营门店数量',
  `purchased_join_store_num` BIGINT COMMENT '采购加盟门店数量',
  `purchased_managed_store_num` BIGINT COMMENT '采购托管门店数量'
)
COMMENT 'SaaS门店采购概况表（日维度），统计各租户每日门店经营和采购情况'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = 'SaaS门店采购概况日维度统计表，包含门店经营状态和采购情况的各项指标',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```