CREATE TABLE IF NOT EXISTS app_wareshoue_sku_store_df_17(
	warehouse_no BIGINT COMMENT '库存仓号，取值范围：2-155',
	warehouse_name STRING COMMENT '库存仓名称',
	sku_id STRING COMMENT 'SKU编码，商品唯一标识',
	spu_name STRING COMMENT '商品名称',
	sku_disc STRING COMMENT '商品描述，包含规格、等级等信息',
	store_quantity BIGINT COMMENT '仓库库存数量，取值范围：0-958',
	ues_quantity BIGINT COMMENT '可用库存数量，取值范围：-34-615（负值表示库存不足）',
	road_quantity BIGINT COMMENT '在途库存数量，取值范围：0-703',
	sale_snt BIGINT COMMENT '当日销量，取值范围：0-802',
	sale_amt DECIMAL(38,18) COMMENT '当日应付GMV，销售金额'
)
COMMENT '当日17时自营水果库存数据表，记录各仓库水果商品的库存、销量和销售金额信息'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='自营水果库存数据表，每日17点更新，用于库存管理和销售分析') 
LIFECYCLE 30;