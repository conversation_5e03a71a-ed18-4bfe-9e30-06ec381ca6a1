CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_city_delivery_selfowend_wi` (
  `year` STRING COMMENT '年份，格式：YYYY',
  `week_of_year` BIGINT COMMENT '周数，取值范围：1-53',
  `monday` STRING COMMENT '周一日期，格式：YYYYMMDD',
  `sunday` STRING COMMENT '周日日期，格式：YYYYMMDD',
  `cause_type` STRING COMMENT '业务类型；枚举：鲜沐,SAAS',
  `province` STRING COMMENT '省份名称',
  `admin_city` STRING COMMENT '城市名称',
  `area` STRING COMMENT '区域/区县名称',
  `cust_type` STRING COMMENT '客户类型；枚举：面包蛋糕,西餐,咖啡,甜品冰淇淋,茶饮,其他',
  `brand_type` STRING COMMENT '大客户类型；枚举：大客户,普通,批发客户（注：单店,批发大客户,普通大客户,KA大客户 已弃用）',
  `brand_name` STRING COMMENT '品牌名称',
  `category1` STRING COMMENT '一级类目名称',
  `category2_id` STRING COMMENT '二级类目ID',
  `category2` STRING COMMENT '二级类目名称',
  `category3_id` STRING COMMENT '三级类目ID',
  `category3` STRING COMMENT '三级类目名称',
  `category4_id` STRING COMMENT '四级类目ID',
  `category4` STRING COMMENT '四级类目名称',
  `origin_total_amt` DECIMAL(38,18) COMMENT '配送应付总金额（原始金额）',
  `real_total_amt` DECIMAL(38,18) COMMENT '配送应付总金额（实际金额）',
  `cost_amt` DECIMAL(38,18) COMMENT '总成本金额',
  `origin_pay_margin` DECIMAL(38,18) COMMENT '应付毛利润（原始金额）',
  `real_pay_margin` DECIMAL(38,18) COMMENT '实付毛利润（实际金额）',
  `preferential_amt` DECIMAL(38,18) COMMENT '营销优惠金额',
  `after_sale_amt` DECIMAL(38,18) COMMENT '已到货售后总金额',
  `deliver_total_amt` DECIMAL(38,18) COMMENT '配送GMV（商品交易总额）'
)
COMMENT '城市整体配送数据周表，按周统计各城市的配送业务数据，包含金额、成本、利润等核心业务指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：YYYYMMDD，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment'='城市配送业务周度汇总表',
  'lifecycle'='30'
)