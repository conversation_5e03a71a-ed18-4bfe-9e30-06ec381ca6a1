```sql
CREATE TABLE IF NOT EXISTS app_saas_merchant_store_item_order_analysis_week_df(
    tenant_id BIGINT COMMENT '租户ID，取值范围：2-8',
    time_tag STRING COMMENT '时间标签，格式：yyyyMMdd，表示周一日期',
    item_id BIGINT COMMENT '商品ID，取值范围：1-398',
    store_id BIGINT COMMENT '门店ID，取值范围：1-419',
    average_order_period DECIMAL(38,18) COMMENT '平均订货周期（天）',
    average_order_period_last_period DECIMAL(38,18) COMMENT '上周期平均订货周期（天）',
    average_order_period_upper_period DECIMAL(38,18) COMMENT '平均订货周期环比变化率',
    order_amount BIGINT COMMENT '订货数量，取值范围：1-250',
    order_amount_last_period BIGINT COMMENT '上周期订货数量，取值范围：0-70',
    order_amount_upper_period DECIMAL(38,18) COMMENT '订货数量环比变化率',
    order_price DECIMAL(38,18) COMMENT '订货金额（元）',
    order_price_last_period DECIMAL(38,18) COMMENT '上周期订货金额（元）',
    order_price_upper_period DECIMAL(38,18) COMMENT '订货金额环比变化率',
    last_order_time STRING COMMENT '最后订货日期，格式：yyyy-MM-dd，年月日格式',
    last_order_amount BIGINT COMMENT '最后订货数量，取值范围：1-250',
    last_order_price DECIMAL(38,18) COMMENT '最后订货金额（元）'
)
COMMENT 'SaaS门店商品订货分析周报表：包含门店商品的订货周期、订货数量、订货金额等指标的周度分析数据，以及环比变化情况'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据日期')
STORED AS ALIORC
TBLPROPERTIES ('comment'='SaaS门店商品订货分析周度统计表',
               'last_data_modified_time'='2025-09-18 02:20:49')
LIFECYCLE 30;
```