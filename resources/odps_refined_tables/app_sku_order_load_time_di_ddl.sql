CREATE TABLE IF NOT EXISTS app_sku_order_load_time_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示年月日',
	`week` BIGINT COMMENT '星期，取值范围：1-7（1表示周一，7表示周日）',
	`sku_id` STRING COMMENT 'SKU编号，商品唯一标识',
	`spu_name` STRING COMMENT '商品名称',
	`sku_disc` STRING COMMENT '商品描述，包含规格信息如"10KG*1箱"',
	`cust_cnt` BIGINT COMMENT '交易客户数，统计周期内购买该SKU的客户数量',
	`loading_cust_cnt` BIGINT COMMENT '登录客户数，统计周期内浏览该SKU的客户数量'
) 
COMMENT '下单时间段偏好SKU数据表，记录不同时间段内各SKU的交易和浏览客户数据'
PARTITIONED BY (`ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='下单时间段偏好SKU数据分析表，用于分析不同时间段的商品销售和用户浏览偏好') 
LIFECYCLE 30;