CREATE TABLE IF NOT EXISTS app_pcs_supplier_in_bound_df(
	`date` STRING COMMENT '日期，格式为yyyyMMdd',
	`warehouse_no` BIGINT COMMENT '库存仓号，取值范围：10-69',
	`warehouse_name` STRING COMMENT '库存仓名称，如：东莞总仓、长沙总仓、嘉兴总仓等',
	`sku_id` STRING COMMENT 'SKU编号，商品最小库存单位标识',
	`spu_name` STRING COMMENT '商品名称，如：紫香1号百香果、国产红心火龙果、伦晚橙等',
	`spu_disc` STRING COMMENT '商品描述，包含规格、等级、重量等信息',
	`supplier_id` BIGINT COMMENT '供应商编号，取值范围：147-2284',
	`supplier` STRING COMMENT '供应商名称，如：陕果集市（成都）特产有限公司、石云花等',
	`price_start_time` DATETIME COMMENT '价格生效时间，格式为yyyy-MM-dd HH:mm:ss，表示年月日时分秒',
	`price_end_time` DATETIME COMMENT '价格失效时间，格式为yyyy-MM-dd HH:mm:ss，表示年月日时分秒',
	`in_bound_sku_cnt` BIGINT COMMENT '90天内采购入库量，取值范围：0-16886',
	`in_bound_amt` DECIMAL(38,18) COMMENT '90天内采购入库金额，单位：元',
	`in_bound_avg_amt` DECIMAL(38,18) COMMENT '90天内采购入库平均金额，单位：元',
	`quoted_price` DECIMAL(38,18) COMMENT '报价，单位：元'
) 
COMMENT '竞价供应商历史入库情况表，记录供应商90天内的采购入库数据和报价信息'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='竞价供应商历史入库情况分析表，用于供应商绩效评估和采购决策支持') 
LIFECYCLE 30;