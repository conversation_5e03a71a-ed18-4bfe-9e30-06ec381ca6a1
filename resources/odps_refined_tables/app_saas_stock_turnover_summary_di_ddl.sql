CREATE TABLE IF NOT EXISTS app_saas_stock_turnover_summary_di(
	time_tag STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
	tenant_id BIGINT COMMENT '租户ID，取值范围：2-118，表示SaaS平台的租户标识',
	sku_id BIGINT COMMENT 'SKU ID，取值范围：100039-126024，表示商品的唯一标识',
	warehouse_no BIGINT COMMENT '仓库编号，取值范围：1-176，表示库存仓库的唯一标识',
	warehouse_name STRING COMMENT '仓库名称，如"杭州总仓"等具体仓库名称',
	turnover_days DECIMAL(38,18) COMMENT '近30天库存周转天数，表示商品库存周转效率指标',
	opening_quantity BIGINT COMMENT '期初库存数量，取值范围：0-1065170，表示统计周期开始时的库存数量',
	ending_quantity BIGINT COMMENT '期末库存数量，取值范围：0-1060080，表示统计周期结束时的库存数量',
	sale_out_quantity BIGINT COMMENT '销售出库数量，取值范围：0-5274，包含自提的销售出库数量'
) 
COMMENT 'SaaS平台近30天库存周转天数汇总表，包含各租户商品的库存周转效率指标和库存变动情况'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据加载的日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='SaaS库存周转分析表，用于监控商品库存周转效率和库存管理情况') 
LIFECYCLE 30;