CREATE TABLE IF NOT EXISTS app_cust_effective_mi(
	month STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
	m1_name STRING COMMENT 'M1管理者（销售主管）姓名',
	m2_name STRING COMMENT 'M2管理者（销售经理）姓名',
	m3_name STRING COMMENT 'M3管理者（销售总监）姓名',
	zone_name STRING COMMENT '运营区域名称，枚举值包括：东莞、佛山、厦门、四川、大粤西等',
	region STRING COMMENT '销售大区名称，枚举值包括：华南大区、闽桂大区、西南大区等',
	effective_cust_target BIGINT COMMENT '有效月活客户数目标值，取值范围：380-4476',
	effect_cust_cnt BIGINT COMMENT '实际有效月活客户数，取值范围：318-3818',
	time_scheduling DECIMAL(38,18) COMMENT '时间进度比例，取值范围：0.0-1.0'
) 
PARTITIONED BY (ds STRING COMMENT '分区字段，yyyyMMdd格式，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='M1/M2/M3管理者履约有效月活客户数统计表，包含各层级管理者的月活客户目标完成情况') 
LIFECYCLE 30;