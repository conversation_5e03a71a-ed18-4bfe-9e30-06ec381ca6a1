CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_category_cust_self_delivery_kpi_wi` (
  `year` STRING COMMENT '年份，格式：YYYY',
  `week_of_year` BIGINT COMMENT '周数，一年中的第几周，范围：1-53',
  `monday` STRING COMMENT '周一日期，格式：YYYYMMDD',
  `sunday` STRING COMMENT '周日日期，格式：YYYYMMDD',
  `category` STRING COMMENT '商品类型：乳制品，鲜果，其他',
  `cust_team` STRING COMMENT '客户团队类型：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户',
  `origin_total_amt` DECIMAL(38,18) COMMENT '原始总金额，订单原始金额总计',
  `real_total_amt` DECIMAL(38,18) COMMENT '应付总金额，实际应付金额总计',
  `cost_amt` DECIMAL(38,18) COMMENT '成本，总成本金额',
  `timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送原始总金额，省心送服务的原始金额总计',
  `timing_real_total_amt` DECIMAL(38,18) COMMENT '省心送实际总金额，省心送服务的实际金额总计',
  `cust_cnt` BIGINT COMMENT '客户数，客户数量统计',
  `cust_unit_amt` DECIMAL(38,18) COMMENT '客户单价，平均每个客户的金额',
  `order_cnt` BIGINT COMMENT '订单数，订单数量统计',
  `point_cnt` BIGINT COMMENT '点位数，点位数量统计',
  `day_point_cnt` BIGINT COMMENT '日点位数，每日点位数量统计',
  `sku_cnt` BIGINT COMMENT 'SKU数量，商品SKU数量统计',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费，配送费用金额',
  `after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后总金额，售后已到货金额总计',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储成本，仓储费用金额',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本，干线运输费用金额',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送成本，配送服务费用金额',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提成本，自提相关费用金额',
  `other_amt` DECIMAL(38,18) COMMENT '其他成本，其他杂项费用金额',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨成本，调拨相关费用金额'
) 
COMMENT '履约口径客户、商品类型维度KPI指标日汇总表，按客户团队和商品类型维度统计的履约相关KPI指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '履约口径客户、商品类型维度KPI指标日汇总表，包含原始金额、实际金额、成本、客户数、订单数、点位数、SKU数等关键业务指标',
  'lifecycle' = '30'
);