CREATE TABLE IF NOT EXISTS app_crm_sku_month_gmv_di(
	month_tag STRING COMMENT '月份标记，格式：yyyyMM，如202509表示2025年9月',
	sku_id STRING COMMENT '商品SKU ID',
	self_support BIGINT COMMENT '自营品标记：0-普通商品，1-自营商品',
	area_no BIGINT COMMENT '运营区域编号，取值范围：1001-44269',
	gmv DECIMAL(38,18) COMMENT '下单GMV（商品交易总额）',
	sales_volume BIGINT COMMENT '销量，取值范围：1-400',
	merchant_num BIGINT COMMENT '下单客户数，取值范围：1-48',
	province STRING COMMENT '省份名称',
	city STRING COMMENT '城市名称',
	area STRING COMMENT '区县名称'
) 
COMMENT 'SKU每月下单GMV表，记录商品SKU在不同运营区域的月度交易数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='SKU月度GMV统计表，包含商品销售、区域分布和客户下单等核心指标') 
LIFECYCLE 30;