```sql
CREATE TABLE IF NOT EXISTS app_chatbi_cust_orders_df(
    `order_no` STRING COMMENT '订单号，唯一标识一个订单',
    `order_item_id` STRING COMMENT '订单明细ID，唯一标识订单中的一个商品项',
    `order_type` BIGINT COMMENT '订单类型：0-普通订单（一次购买一次性配送完），1-省心送订单（一次购买可分多次配送），2-运费订单，3-代下单，10-虚拟商品（特指奶油黄金卡），30-顺鹿达订单（鲜果POP）',
    `order_status` BIGINT COMMENT '订单状态：1-待支付，2-待配送，3-待收货，6-已收货，7-申请退款订单，8-已退款订单，9-支付失败订单，10-支付中断超时关闭订单，11-已撤销订单，14-手动关闭订单，15-人工退款中',
    `order_item_status` BIGINT COMMENT '订单明细状态，取值同order_status字段',
    `order_date` STRING COMMENT '订单创建日期，格式：yyyyMMdd，如：20220811',
    `order_time` STRING COMMENT '订单创建时间，格式：yyyy-MM-dd HH:mm:ss，如：2022-08-11 16:42:11',
    `pay_date` STRING COMMENT '订单支付日期，格式：yyyyMMdd，如：20220811',
    `confirm_date` STRING COMMENT '订单确认收货日期，格式：yyyyMMdd，如：20220814',
    `spu_id` STRING COMMENT '商品SPU ID，关联products.pd_id，每个SPU可能有多个SKU',
    `sku_id` STRING COMMENT 'SKU ID，关联inventory.sku',
    `spu_name` STRING COMMENT '商品名称，如：安佳淡奶油、国产红心火龙果、嘉利宝白巧克力28%等',
    `sku_disc` STRING COMMENT 'SKU规格描述，关联inventory.weight，如：250G*24包、毛重6-8斤/一级/标准规格',
    `cust_id` STRING COMMENT '客户ID，关联merchant.m_id',
    `cust_name` STRING COMMENT '客户名称，如：霸王茶姬石犀里店、旺山知味恋歌，关联merchant.mname',
    `cust_type` STRING COMMENT '客户类型，枚举值：面包蛋糕、茶饮、咖啡、其他、甜品冰淇淋、西餐',
    `admin_id` STRING COMMENT '大客户ID，关联admin.admin_id',
    `admin_full_name` STRING COMMENT '大客户的工商主体全名，如：鲜沐科技有限公司等',
    `admin_name` STRING COMMENT '大客户品牌名称，如：霸王茶姬、浙江茶百道等，关联admin.name_remakes',
    `account_id` STRING COMMENT '下单账户ID，关联merchant_sub_account.account_id',
    `product_type` BIGINT COMMENT '产品类型',
    `sku_cnt` BIGINT COMMENT 'SKU购买件数',
    `gift_cnt` BIGINT COMMENT '赠品件数',
    `real_unit_amt` DECIMAL(18,2) COMMENT 'SKU实际单价（优惠后）',
    `real_total_amt` DECIMAL(18,2) COMMENT 'SKU实际总价（优惠前）',
    `origin_unit_amt` DECIMAL(18,2) COMMENT 'SKU原始单价（优惠前）',
    `origin_total_amt` DECIMAL(18,2) COMMENT 'SKU原始总价（优惠后）',
    `delivery_fee_amt` DECIMAL(18,2) COMMENT 'SKU分摊的配送费金额',
    `real_total_gmv_amt` DECIMAL(18,2) COMMENT 'SKU+运费的实际GMV总金额',
    `delivery_fee_coupon_amt` DECIMAL(18,2) COMMENT '配送费优惠券金额',
    `category_type` STRING COMMENT '类目类型，枚举值：鲜果、乳制品、非乳制品、其他',
    `category1` STRING COMMENT '一级类目名称',
    `category2` STRING COMMENT '二级类目名称',
    `category3` STRING COMMENT '三级类目名称，如：桃、搅打型稀奶油、纯牛奶等',
    `is_self_owned_brand` BIGINT COMMENT '是否自有品牌：0-否（NB品），1-是（PB品-private brand）',
    `sub_type` BIGINT COMMENT 'SKU子类型：1-代销不入仓（全品类）、2-代销入仓（全品类）、3-自营（经销）、5-顺鹿达商品（鲜果POP）',
    `sku_brand` STRING COMMENT 'SKU品牌，如：安佳、C味道、Protag',
    `cust_register_province` STRING COMMENT '客户注册省份，如：浙江、广东、上海',
    `cust_register_city` STRING COMMENT '客户注册城市，如：杭州市、广州市、上海市',
    `cust_register_area` STRING COMMENT '客户注册行政区县（县级市），如：浦东',
    `cust_register_time` STRING COMMENT '客户注册时间，格式：yyyy-MM-dd HH:mm:ss，如：2022-06-29 21:46:14',
    `cust_register_date` STRING COMMENT '客户注册日期，格式：yyyyMMdd，如：20220629',
    `cust_operate_status` BIGINT COMMENT '商户经营状态：0-正常，1-倒闭，2-待提交核验，3-核验中，4-已核验拒绝',
    `cust_business_line` BIGINT COMMENT '客户业务线：0-鲜沐，1-顺鹿达（POP）',
    `area_no` BIGINT COMMENT '运营区域编号，如：1001',
    `area_name` STRING COMMENT '运营区域名称，如：杭州、宁波、苏州',
    `large_area_name` STRING COMMENT '运营大区名称，如：杭州大区（覆盖杭州周边城市）、苏南大区（覆盖苏州等数个城市）',
    `large_area_no` STRING COMMENT '运营大区编号',
    `bd_name` STRING COMMENT '门店所属BD姓名，来自crm_bd_org.bd_name',
    `bd_id` BIGINT COMMENT '门店所属BD ID，来自crm_bd_org.bd_id',
    `cust_phone` STRING COMMENT '客户电话，关联merchant.phone',
    `cust_first_order_date` STRING COMMENT '客户首次订单日期，格式：yyyyMMdd，如：20250101',
    `cust_first_sku_order_date` STRING COMMENT '客户首次购买本SKU的日期，格式：yyyyMMdd，如：20240909',
    `warehouse_no` STRING COMMENT '履约库存仓号',
    `warehouse_name` STRING COMMENT '履约库存仓名称，如：嘉兴总仓、南京总仓',
    `sku_cost_amt` DECIMAL(18,2) COMMENT 'SKU总成本，用于计算履约毛利率',
    `delivery_date_list` STRING COMMENT '此SKU的履约日期列表（省心送订单会有多个日期），如：20250101,20250122,20250103',
    `first_deliver_date` STRING COMMENT '此SKU的首次配送日期（省心送订单），格式：yyyyMMdd，如：20250101',
    `last_deliver_date` STRING COMMENT '此SKU的最近一次配送日期（省心送订单），格式：yyyyMMdd，如：20250121',
    `point_id` BIGINT COMMENT '配送地址点位ID，每个配送地址对应一个唯一ID',
    `m1_name` STRING COMMENT 'BD的直属上级M1姓名',
    `m2_name` STRING COMMENT 'BD的上级的上级M2姓名（M1的主管）',
    `m3_name` STRING COMMENT '销售总监M3姓名（M2的直接主管）',
    `after_sale_amount` DECIMAL(38,18) COMMENT '售后总金额',
    `deliveryed_after_sale_amount` DECIMAL(38,18) COMMENT '已配送售后金额（小于等于售后总金额）',
    `after_sales_times` BIGINT COMMENT '此SKU售后次数',
    `first_after_sale_handle_date` STRING COMMENT '首次售后处理日期，格式：yyyyMMdd，如：20220811',
    `last_after_sale_handle_date` STRING COMMENT '最近一次售后处理日期，格式：yyyyMMdd，如：20220811',
    `is_valid_bd` BIGINT COMMENT 'BD有效性标识：0-无效BD（测试BD等），1-有效BD（真实BD）'
)
COMMENT '订单分析明细表，包含订单基本信息、商品信息、客户信息、配送信息、售后信息等完整订单分析数据'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = '订单分析明细表，用于订单数据分析和业务报表生成',
    'columnar.nested.type' = 'true'
)
LIFECYCLE 7
```