CREATE TABLE IF NOT EXISTS app_sku_cust_order_wi(
	year STRING COMMENT '年份，格式为YYYY',
	week_of_year BIGINT COMMENT '周数，取值范围：1-53',
	monday STRING COMMENT '周一日期，格式为YYYYMMDD，年月日',
	sunday STRING COMMENT '周日日期，格式为YYYYMMDD，年月日',
	sku_id STRING COMMENT 'SKU编号，商品最小库存单位标识',
	spu_name STRING COMMENT '商品名称，标准产品单元名称',
	sku_disc STRING COMMENT '商品描述，包含规格、包装等信息',
	cust_type STRING COMMENT '客户业态，枚举类型：其他、餐饮、零售等',
	cust_cnt BIGINT COMMENT '交易客户数，统计周期内购买该SKU的客户数量',
	group_cust_cnt BIGINT COMMENT '业态总客户数，统计周期内该业态的总客户数量'
) 
COMMENT '区域渗透数据表，统计各SKU在不同客户业态中的渗透情况'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为YYYYMMDD，年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='区域渗透数据分析表，用于分析商品在各业态客户中的覆盖情况') 
LIFECYCLE 30;