CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_log_mall_cust_category_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计日期',
  `cust_type` STRING COMMENT '客户业态，枚举值包括：其他等',
  `category_type` STRING COMMENT '品类，枚举值包括：NB-A&T淡奶油、PB-标品、乳制品、其他、鲜果等',
  `sku_exposure_pv` BIGINT COMMENT '商品曝光PV（页面浏览量）',
  `sku_exposure_uv` BIGINT COMMENT '商品曝光UV（独立访客数）',
  `sku_click_pv` BIGINT COMMENT '商品点击PV（页面点击量）',
  `sku_click_uv` BIGINT COMMENT '商品点击UV（独立点击用户数）',
  `sku_add_to_cart_pv` BIGINT COMMENT '商品加购PV（加入购物车次数）',
  `sku_add_to_cart_uv` BIGINT COMMENT '商品加购UV（加入购物车独立用户数）',
  `buy_cust_cnt` BIGINT COMMENT '购买人数（实际购买商品的独立用户数）'
) 
COMMENT '品类转化-商品维度数据表，统计各品类商品的用户行为转化数据，包括曝光、点击、加购和购买等关键指标'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，用于按天分区管理数据'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '品类转化-商品维度数据分析表，用于商品转化率分析和用户行为洞察',
  'lifecycle' = '30'
);