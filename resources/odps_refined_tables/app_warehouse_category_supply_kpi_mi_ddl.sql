CREATE TABLE IF NOT EXISTS app_warehouse_category_supply_kpi_mi(
	month STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
	warehouse_no BIGINT COMMENT '仓库编号，取值范围：1-155',
	warehouse_name STRING COMMENT '仓库名称，如：上海总仓、华西总仓等',
	category STRING COMMENT '商品类别，枚举值：鲜果、标品',
	sale_out_time DECIMAL(38,18) COMMENT '售罄时长（单位：小时）',
	on_sale_time DECIMAL(38,18) COMMENT '上架时长（单位：小时）',
	store_cost_amt DECIMAL(38,18) COMMENT '期末库存成本（单位：元）',
	sale_amt DECIMAL(38,18) COMMENT '销售出库成本（单位：元）',
	temporary_store_amt DECIMAL(38,18) COMMENT '临保成本（单位：元）',
	damage_amt DECIMAL(38,18) COMMENT '滞销过期货损出库成本（单位：元）',
	origin_total_amt DECIMAL(38,18) COMMENT '履约应付GMV（单位：元）'
) 
COMMENT '供应链KPI指标表，包含各仓库不同商品类别的库存周转、成本核算等关键绩效指标'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='供应链KPI监控表，用于分析仓库运营效率和成本控制情况') 
LIFECYCLE 30;