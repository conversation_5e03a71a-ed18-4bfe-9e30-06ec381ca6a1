CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_operate_kpi_trade_wi` (
  `year` STRING COMMENT '年份，格式：YYYY',
  `week_of_year` BIGINT COMMENT '周数，取值范围：1-53',
  `monday` STRING COMMENT '周一日期，格式：YYYYMMDD（年月日）',
  `sunday` STRING COMMENT '周日日期，格式：YYYYMMDD（年月日）',
  `origin_total_amt` DECIMAL(38,18) COMMENT '交易应付总金额（单位：元）',
  `real_total_amt` DECIMAL(38,18) COMMENT '交易实付总金额（单位：元）',
  `cust_cnt` BIGINT COMMENT '交易客户数',
  `cust_arpu` DECIMAL(38,18) COMMENT 'ARPU(平均每用户收入)，计算公式：应付总金额/客户数（单位：元）',
  `order_cnt` BIGINT COMMENT '交易订单数',
  `order_avg` DECIMAL(38,18) COMMENT '订单均价，计算公式：应付总金额/订单数（单位：元）',
  `consign_origin_total_amt` DECIMAL(38,18) COMMENT '代售应付总金额（单位：元）',
  `consign_real_total_amt` DECIMAL(38,18) COMMENT '代售实付总金额（单位：元）',
  `consign_preferential_amt` DECIMAL(38,18) COMMENT '代售营销金额（单位：元）',
  `consign_cust_cnt` BIGINT COMMENT '代售客户数',
  `consign_order_cnt` BIGINT COMMENT '代售订单数',
  `register_cust_cnt` BIGINT COMMENT '注册客户数',
  `new_active_cust_cnt` BIGINT COMMENT '有效拉新客户数（注册且首日下单金额>=15元）'
) 
COMMENT '交易（订单）运营KPI指标汇总表，按周统计的交易相关核心运营指标，包括交易金额、客户数、订单数、ARPU等关键绩效指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：YYYYMMDD（年月日）'
)
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='交易运营KPI周度汇总表，用于监控和分析交易业务的核心运营指标') 
LIFECYCLE 30;