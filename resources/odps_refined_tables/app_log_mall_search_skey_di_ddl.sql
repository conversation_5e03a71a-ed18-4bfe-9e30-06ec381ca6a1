CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_log_mall_search_skey_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据发生的年月日',
  `cust_team` STRING COMMENT '客户团队类型，取值范围：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户、批发',
  `cust_type` STRING COMMENT '客户行业类型，取值范围：面包蛋糕、西餐、咖啡、甜品冰淇淋、茶饮、其他',
  `life_cycle` STRING COMMENT '生命周期标签（粗），如：稳定期、成长期等',
  `life_cycle_detail` STRING COMMENT '生命周期标签（细），如：S1、S2等',
  `register_province` STRING COMMENT '注册时省份名称',
  `register_city` STRING COMMENT '注册时城市名称',
  `register_area` STRING COMMENT '注册时区县名称',
  `city_id` BIGINT COMMENT '运营服务区ID，数值型标识',
  `city_name` STRING COMMENT '运营服务区名称',
  `large_area_id` BIGINT COMMENT '运营服务大区ID，数值型标识',
  `large_area_name` STRING COMMENT '运营服务大区名称',
  `module_name` STRING COMMENT '模块名称',
  `skey` STRING COMMENT '搜索词，用户输入的搜索关键词',
  `pv` BIGINT COMMENT '页面浏览量，统计搜索词的访问次数',
  `uv` BIGINT COMMENT '独立访客数，统计搜索词的独立用户访问数'
)
COMMENT '商城搜索词流量分析表，用于分析商城搜索关键词的流量数据，包括PV、UV等指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据采集的年月日'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment'='商城搜索词流量分析表，包含搜索关键词的流量统计和用户属性信息',
  'lifecycle'='30'
);