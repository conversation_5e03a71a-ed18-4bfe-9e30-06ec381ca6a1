CREATE TABLE IF NOT EXISTS app_sku_cust_delivery_mi(
	`month` STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
	`sku_id` STRING COMMENT 'SKU编号，商品最小库存单位的唯一标识',
	`category1` STRING COMMENT '一级类目，商品所属的最顶层分类，如：乳制品',
	`spu_name` STRING COMMENT '商品名称，标准产品单元的名称',
	`sku_disc` STRING COMMENT '商品描述，包含规格信息，如：10KG*1箱',
	`cust_cnt` BIGINT COMMENT '人群数量，偏好该商品的目标客户数量，取值范围：17-1905',
	`dlv_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV，该商品的原始应付总金额',
	`other_spu_name` STRING COMMENT '连带商品名称，与该商品一起被偏好的其他商品名称',
	`other_cust_cnt` BIGINT COMMENT '连带客户数，同时偏好连带商品的客户数量，取值范围：0-776',
	`other_cdlv_origin_total_amt` DECIMAL(38,18) COMMENT '连带履约应付GMV，连带商品的原始应付总金额'
) 
COMMENT '人群偏好商品明细表，记录不同人群对商品的偏好情况，包含主商品和连带商品的相关指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='人群偏好商品明细分析表') 
LIFECYCLE 30;