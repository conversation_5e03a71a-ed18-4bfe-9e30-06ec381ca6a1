```sql
CREATE TABLE IF NOT EXISTS app_saas_merchant_store_order_analysis_df(
    tenant_id BIGINT COMMENT '租户ID，取值范围：2-64',
    type BIGINT COMMENT '统计周期类型：1-周，2-月，3-季度',
    time_tag STRING COMMENT '时间标签，格式：yyyyMMdd（年月日）',
    store_id BIGINT COMMENT '门店ID，取值范围：1-396432',
    average_order_period DECIMAL(38,18) COMMENT '平均订货周期（天）',
    average_order_period_last_period DECIMAL(38,18) COMMENT '上周期平均订货周期（天）',
    average_order_period_upper_period DECIMAL(38,18) COMMENT '平均订货周期环比（百分比）',
    order_amount BIGINT COMMENT '订货数量，取值范围：1-86643',
    order_amount_last_period BIGINT COMMENT '上周期订货数量，取值范围：0-86643',
    order_amount_upper_period DECIMAL(38,18) COMMENT '订货数量环比（百分比）',
    order_price DECIMAL(38,18) COMMENT '订货金额（元）',
    order_price_last_period DECIMAL(38,18) COMMENT '上周期订货金额（元）',
    order_price_upper_period DECIMAL(38,18) COMMENT '订货金额环比（百分比）',
    last_order_time STRING COMMENT '最后订货日期，格式：yyyy-MM-dd（年月日）',
    last_order_amount BIGINT COMMENT '最后订货数量，取值范围：1-12902',
    last_order_price DECIMAL(38,18) COMMENT '最后订货金额（元）'
)
COMMENT 'SaaS门店订货分析表，包含门店订货周期、数量、金额等指标的统计分析'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd（年月日）')
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
               'comment'='SaaS门店订货分析结果表，用于分析门店订货行为和趋势')
LIFECYCLE 30;
```