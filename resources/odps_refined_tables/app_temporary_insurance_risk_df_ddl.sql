CREATE TABLE IF NOT EXISTS app_temporary_insurance_risk_df(
    sku STRING COMMENT '商品SKU编号，唯一标识一个商品',
    warehouse_no BIGINT COMMENT '库存仓编号，取值范围：2-10，常见值为10',
    area_no BIGINT COMMENT '城市编号，取值范围：1001-44271，常见值集中在8577-25541之间',
    large_area_no BIGINT COMMENT '运营大区编号，取值范围：1-89，常见值为1和84',
    date_flag STRING COMMENT '业务日期标识，格式为yyyyMMdd，表示年月日'
)
COMMENT '临保风险品数据表，记录商品在临保期内的风险信息'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
     'comment'='临保风险品数据表，用于存储商品在临保期内的风险监控数据') 
LIFECYCLE 30;