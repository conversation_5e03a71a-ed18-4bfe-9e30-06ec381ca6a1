CREATE TABLE IF NOT EXISTS app_kpi_all_trade_mi(
	month STRING COMMENT '月份，格式：yyyyMM',
	manage_type STRING COMMENT '业务线类型：自营，代仓，代售，批发，SAAS鲜沐自营，SAAS鲜沐代仓，SAAS品牌方自营',
	origin_total_amt DECIMAL(38,18) COMMENT '应付总金额',
	real_total_amt DECIMAL(38,18) COMMENT '实付总金额',
	cust_cnt BIGINT COMMENT '客户数',
	order_cnt BIGINT COMMENT '订单数',
	tenant_cnt BIGINT COMMENT '租户数（仅SAAS业务线有值，非SAAS业务线为0）',
	delivery_amt DECIMAL(38,18) COMMENT '运费（运费+超时加单费）',
	cust_arpu DECIMAL(38,18) COMMENT 'ARPU值（应付总金额/客户数）',
	order_avg DECIMAL(38,18) COMMENT '订单均价（应付总金额/订单数）',
	after_sale_noreceived_order_cnt BIGINT COMMENT '未到货售后订单数',
	after_sale_noreceived_amt DECIMAL(38,18) COMMENT '未到货售后总金额',
	after_sale_rate DECIMAL(38,18) COMMENT '退货率（未到货售后总金额/应付总金额）'
) 
COMMENT '交易口径业务线KPI指标月汇总表，包含各业务线的交易金额、客户数、订单数、售后指标等关键业务指标'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='交易口径业务线KPI指标月汇总表，用于业务线经营分析和监控') 
LIFECYCLE 30;