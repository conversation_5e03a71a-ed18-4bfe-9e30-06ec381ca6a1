CREATE TABLE IF NOT EXISTS app_kpi_category_trade_mi(
	month STRING COMMENT '月份，格式：yyyyMM',
	category STRING COMMENT '品类:取值范围[鲜果,乳制品,其他]',
	origin_total_amt DECIMAL(38,18) COMMENT '应付总金额，单位：元',
	real_total_amt DECIMAL(38,18) COMMENT '实付总金额，单位：元',
	cust_cnt BIGINT COMMENT '客户数',
	cust_arpu DECIMAL(38,18) COMMENT 'ARPU(应付总金额/客户数)，单位：元/客户',
	order_cnt BIGINT COMMENT '订单数',
	order_avg DECIMAL(38,18) COMMENT '订单均价(应付总金额/订单数)，单位：元/订单',
	after_sale_noreceived_amt DECIMAL(38,18) COMMENT '未到货售后总金额，单位：元',
	after_sale_rate DECIMAL(38,18) COMMENT '退货率(未到货售后总金额/应付总金额)',
	dire_origin_total_amt DECIMAL(38,18) COMMENT '直发采购应付总金额，单位：元',
	delivery_amt DECIMAL(38,18) COMMENT '运费（运费+超时加单费），单位：元',
	timing_origin_total_amt DECIMAL(38,18) COMMENT '省心送应付总金额，单位：元'
) 
PARTITIONED BY (ds STRING COMMENT '分区字段，yyyyMMdd格式，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='交易口径KPI指标月汇总表，包含各品类交易相关的关键绩效指标月度统计数据') 
LIFECYCLE 30;