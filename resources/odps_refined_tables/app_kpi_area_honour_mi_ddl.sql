CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_kpi_area_honour_mi` (
  `month` STRING COMMENT '日期，格式为yyyyMM，表示年月',
  `area_no` BIGINT COMMENT '城配仓编号，取值范围：1-155',
  `area_name` STRING COMMENT '城配仓名称',
  `sku_type` STRING COMMENT '商品类型，取值范围：自营、代仓、代售',
  `origin_total_amt` DECIMAL(38,18) COMMENT '原始总金额，单位：元',
  `real_total_amt` DECIMAL(38,18) COMMENT '实际总金额，单位：元',
  `cost_amt` DECIMAL(38,18) COMMENT '商品成本价，单位：元',
  `preferential_amt` DECIMAL(38,18) COMMENT '营销金额，单位：元',
  `origin_pay_rate` DECIMAL(38,18) COMMENT '应付毛利率，百分比小数形式',
  `real_pay_rate` DECIMAL(38,18) COMMENT '实付毛利率，百分比小数形式',
  `refund_amt` DECIMAL(38,18) COMMENT '售后退款总金额，单位：元',
  `cust_cnt` BIGINT COMMENT '客户数，取值范围：1-2127',
  `cust_unit_amt` DECIMAL(38,18) COMMENT '客户单价，单位：元/客户',
  `order_cnt` BIGINT COMMENT '订单数，取值范围：2-7785',
  `point_cnt` BIGINT COMMENT '点位数，取值范围：1-2275',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储成本，单位：元',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本，单位：元',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送成本，单位：元',
  `total_deliver_amt` DECIMAL(38,18) COMMENT '履约总费用，单位：元'
) 
COMMENT '履约KPI城配仓维度日表，包含各城配仓的履约相关KPI指标数据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMM，表示年月'
)
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
               'comment'='履约KPI城配仓维度日表，统计各城配仓的销售额、成本、毛利率、客户数、订单数等关键绩效指标') 
LIFECYCLE 30;