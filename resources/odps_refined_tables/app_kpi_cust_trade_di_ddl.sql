```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_kpi_cust_trade_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务日期',
  `cust_class` STRING COMMENT '客户类型枚举：Mars大客户、平台客户、集团大客户（茶百道）、大客户（非茶百道）、批发、普通（非品牌）、普通（品牌）',
  `origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额，交易订单的原始应付金额总计',
  `real_total_amt` DECIMAL(38,18) COMMENT '实付总金额，客户实际支付的金额总计',
  `cust_cnt` BIGINT COMMENT '客户数，当日有交易记录的客户数量',
  `cust_arpu` DECIMAL(38,18) COMMENT 'ARPU值，平均每客户收入（应付总金额/客户数）',
  `order_cnt` BIGINT COMMENT '订单数，当日产生的交易订单总数',
  `order_avg` DECIMAL(38,18) COMMENT '订单均价，平均每订单金额（应付总金额/订单数）',
  `after_sale_noreceived_amt` DECIMAL(38,18) COMMENT '未到货售后总金额，商品未送达情况下的售后退款金额',
  `after_sale_rate` DECIMAL(38,18) COMMENT '退货率，未到货售后金额占应付总金额的比例',
  `dire_origin_total_amt` DECIMAL(38,18) COMMENT '直发采购应付总金额，直发采购模式的应付金额',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费总计，包含运费和超时加单费用',
  `timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送应付总金额，省心送服务模式的应付金额'
) 
COMMENT '交易口径KPI指标日汇总表，包含每日各类客户交易相关的核心业务指标统计'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，用于数据管理和查询优化'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '交易口径KPI指标日汇总表，用于业务分析和报表展示',
  'lifecycle' = '30'
);
```