CREATE TABLE IF NOT EXISTS app_finance_other_cost_details_di(
	service_area STRING COMMENT '大区',
	warehouse_no BIGINT COMMENT '库存仓编号',
	warehouse_name STRING COMMENT '库存仓名称',
	sku STRING COMMENT '商品sku',
	cost_type STRING COMMENT '成本类型，枚举值：货损出库、调拨货损出库、盘盈入库、盘亏出库、出样出库',
	quantity BIGINT COMMENT '库存变动数',
	cost DECIMAL(38,18) COMMENT '成本（含税）',
	cost_notax DECIMAL(38,18) COMMENT '成本（不含税）',
	date_flag STRING COMMENT '数据产生日期，格式：yyyyMMdd',
	category1 STRING COMMENT '商品一级类目'
) 
COMMENT '财务口径其他成本明细表，记录除正常销售外的其他成本明细数据，包括货损、调拨、盘点等业务场景的成本信息'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='财务口径其他成本明细表') 
LIFECYCLE 30;