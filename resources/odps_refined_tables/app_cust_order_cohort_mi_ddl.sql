CREATE TABLE IF NOT EXISTS app_cust_order_cohort_mi(
	first_order_month STRING COMMENT '首购月份，格式为yyyyMM，表示客户首次下单的年月',
	cust_team STRING COMMENT '客户团队类型，取值范围：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户、批发',
	first_order_cust_cnt BIGINT COMMENT 'm0首购人数，即当月首次下单的客户数量',
	cohort_cust_cnt_1 BIGINT COMMENT 'm1复购人数，即首购后第1个月复购的客户数量',
	cohort_cust_cnt_2 BIGINT COMMENT 'm2复购人数，即首购后第2个月复购的客户数量',
	cohort_cust_cnt_3 BIGINT COMMENT 'm3复购人数，即首购后第3个月复购的客户数量',
	cohort_cust_cnt_4 BIGINT COMMENT 'm4复购人数，即首购后第4个月复购的客户数量',
	cohort_cust_cnt_5 BIGINT COMMENT 'm5复购人数，即首购后第5个月复购的客户数量',
	cohort_cust_cnt_6 BIGINT COMMENT 'm6复购人数，即首购后第6个月复购的客户数量',
	cohort_cust_cnt_7 BIGINT COMMENT 'm7复购人数，即首购后第7个月复购的客户数量',
	cohort_cust_cnt_8 BIGINT COMMENT 'm8复购人数，即首购后第8个月复购的客户数量',
	cohort_cust_cnt_9 BIGINT COMMENT 'm9复购人数，即首购后第9个月复购的客户数量',
	cohort_cust_cnt_10 BIGINT COMMENT 'm10复购人数，即首购后第10个月复购的客户数量',
	cohort_cust_cnt_11 BIGINT COMMENT 'm11复购人数，即首购后第11个月复购的客户数量',
	cohort_cust_cnt_12 BIGINT COMMENT 'm12复购人数，即首购后第12个月复购的客户数量',
	cohort_cust_cnt_13 BIGINT COMMENT 'm13复购人数，即首购后第13个月复购的客户数量',
	cohort_cust_cnt_14 BIGINT COMMENT 'm14复购人数，即首购后第14个月复购的客户数量',
	cohort_cust_cnt_15 BIGINT COMMENT 'm15复购人数，即首购后第15个月复购的客户数量',
	cohort_cust_cnt_16 BIGINT COMMENT 'm16复购人数，即首购后第16个月复购的客户数量',
	cohort_cust_cnt_17 BIGINT COMMENT 'm17复购人数，即首购后第17个月复购的客户数量',
	cohort_cust_cnt_18 BIGINT COMMENT 'm18复购人数，即首购后第18个月复购的客户数量',
	cohort_cust_cnt_19 BIGINT COMMENT 'm19复购人数，即首购后第19个月复购的客户数量',
	cohort_cust_cnt_20 BIGINT COMMENT 'm20复购人数，即首购后第20个月复购的客户数量',
	cohort_cust_cnt_21 BIGINT COMMENT 'm21复购人数，即首购后第21个月复购的客户数量',
	cohort_cust_cnt_22 BIGINT COMMENT 'm22复购人数，即首购后第22个月复购的客户数量',
	cohort_cust_cnt_23 BIGINT COMMENT 'm23复购人数，即首购后第23个月复购的客户数量',
	cohort_cust_cnt_24 BIGINT COMMENT 'm24复购人数，即首购后第24个月复购的客户数量',
	first_order_real_amt DECIMAL(38,18) COMMENT 'm0首购实付金额，即当月首次下单客户的实付金额总和',
	cohort_real_amt_1 DECIMAL(38,18) COMMENT 'm1复购实付金额，即首购后第1个月复购客户的实付金额总和',
	cohort_real_amt_2 DECIMAL(38,18) COMMENT 'm2复购实付金额，即首购后第2个月复购客户的实付金额总和',
	cohort_real_amt_3 DECIMAL(38,18) COMMENT 'm3复购实付金额，即首购后第3个月复购客户的实付金额总和',
	cohort_real_amt_4 DECIMAL(38,18) COMMENT 'm4复购实付金额，即首购后第4个月复购客户的实付金额总和',
	cohort_real_amt_5 DECIMAL(38,18) COMMENT 'm5复购实付金额，即首购后第5个月复购客户的实付金额总和',
	cohort_real_amt_6 DECIMAL(38,18) COMMENT 'm6复购实付金额，即首购后第6个月复购客户的实付金额总和',
	cohort_real_amt_7 DECIMAL(38,18) COMMENT 'm7复购实付金额，即首购后第7个月复购客户的实付金额总和',
	cohort_real_amt_8 DECIMAL(38,18) COMMENT 'm8复购实付金额，即首购后第8个月复购客户的实付金额总和',
	cohort_real_amt_9 DECIMAL(38,18) COMMENT 'm9复购实付金额，即首购后第9个月复购客户的实付金额总和',
	cohort_real_amt_10 DECIMAL(38,18) COMMENT 'm10复购实付金额，即首购后第10个月复购客户的实付金额总和',
	cohort_real_amt_11 DECIMAL(38,18) COMMENT 'm11复购实付金额，即首购后第11个月复购客户的实付金额总和',
	cohort_real_amt_12 DECIMAL(38,18) COMMENT 'm12复购实付金额，即首购后第12个月复购客户的实付金额总和',
	cohort_real_amt_13 DECIMAL(38,18) COMMENT 'm13复购实付金额，即首购后第13个月复购客户的实付金额总和',
	cohort_real_amt_14 DECIMAL(38,18) COMMENT 'm14复购实付金额，即首购后第14个月复购客户的实付金额总和',
	cohort_real_amt_15 DECIMAL(38,18) COMMENT 'm15复购实付金额，即首购后第15个月复购客户的实付金额总和',
	cohort_real_amt_16 DECIMAL(38,18) COMMENT 'm16复购实付金额，即首购后第16个月复购客户的实付金额总和',
	cohort_real_amt_17 DECIMAL(38,18) COMMENT 'm17复购实付金额，即首购后第17个月复购客户的实付金额总和',
	cohort_real_amt_18 DECIMAL(38,18) COMMENT 'm18复购实付金额，即首购后第18个月复购客户的实付金额总和',
	cohort_real_amt_19 DECIMAL(38,18) COMMENT 'm19复购实付金额，即首购后第19个月复购客户的实付金额总和',
	cohort_real_amt_20 DECIMAL(38,18) COMMENT 'm20复购实付金额，即首购后第20个月复购客户的实付金额总和',
	cohort_real_amt_21 DECIMAL(38,18) COMMENT 'm21复购实付金额，即首购后第21个月复购客户的实付金额总和',
	cohort_real_amt_22 DECIMAL(38,18) COMMENT 'm22复购实付金额，即首购后第22个月复购客户的实付金额总和',
	cohort_real_amt_23 DECIMAL(38,18) COMMENT 'm23复购实付金额，即首购后第23个月复购客户的实付金额总和',
	cohort_real_amt_24 DECIMAL(38,18) COMMENT 'm24复购实付金额，即首购后第24个月复购客户的实付金额总和',
	noorder_cust_cnt BIGINT COMMENT '注册未下单客户数，截止到月末的注册但未下单的非闭店审核通过客户数量'
) 
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='客户cohort分析表，按首购月份和客户团队类型统计客户留存和复购情况，包含24个月的复购人数和实付金额数据') 
LIFECYCLE 30;