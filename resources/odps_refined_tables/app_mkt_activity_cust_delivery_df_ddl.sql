CREATE TABLE IF NOT EXISTS app_mkt_activity_cust_delivery_df(
	activity_id BIGINT COMMENT '活动ID，唯一标识一个营销活动',
	activity_name STRING COMMENT '活动名称，描述活动的具体内容',
	start_time DATETIME COMMENT '活动开始时间，格式为年月日时分秒',
	end_time DATETIME COMMENT '活动结束时间，格式为年月日时分秒',
	activity_type STRING COMMENT '活动类型，枚举值：特价活动、满减活动、折扣活动等',
	activity_tag STRING COMMENT '活动目的标签，枚举值：新品推广、滞销促销、清仓处理、节日促销等',
	city_id BIGINT COMMENT '运营服务区ID，标识城市级别的运营区域',
	city_name STRING COMMENT '运营服务区名称，对应城市ID的具体城市名称',
	large_area_id BIGINT COMMENT '运营服务大区ID，标识大区级别的运营区域',
	large_area_name STRING COMMENT '运营服务大区名称，对应大区ID的具体大区名称',
	cust_id BIGINT COMMENT '客户ID，唯一标识参与活动的客户',
	cust_name STRING COMMENT '客户名称，参与活动的客户企业或店铺名称',
	life_cycle_detail STRING COMMENT '客户生命周期标签（细分），活动开始时的客户生命周期状态',
	order_cnt BIGINT COMMENT '活动期间产生的订单数量',
	sku_cnt BIGINT COMMENT '活动期间销售的SKU种类数量',
	real_total_amt DECIMAL(38,18) COMMENT '实付金额，客户实际支付的金额',
	origin_total_amt DECIMAL(38,18) COMMENT '应付金额，订单原始应付金额',
	delivery_fruit_origin_amt_before_8_14 DECIMAL(38,18) COMMENT '活动前8-14天鲜果类商品的应付GMV',
	delivery_fruit_origin_gross_before_8_14 DECIMAL(38,18) COMMENT '活动前8-14天鲜果类商品的应付毛利润',
	delivery_notfruit_origin_amt_before_8_14 DECIMAL(38,18) COMMENT '活动前8-14天标品类商品的应付GMV',
	delivery_notfruit_origin_gross_before_8_14 DECIMAL(38,18) COMMENT '活动前8-14天标品类商品的应付毛利润',
	delivery_fruit_origin_amt_before_1_7 DECIMAL(38,18) COMMENT '活动前1-7天鲜果类商品的应付GMV',
	delivery_fruit_origin_gross_before_1_7 DECIMAL(38,18) COMMENT '活动前1-7天鲜果类商品的应付毛利润',
	delivery_notfruit_origin_amt_before_1_7 DECIMAL(38,18) COMMENT '活动前1-7天标品类商品的应付GMV',
	delivery_notfruit_origin_gross_before_1_7 DECIMAL(38,18) COMMENT '活动前1-7天标品类商品的应付毛利润',
	delivery_fruit_origin_amt_after_1_7 DECIMAL(38,18) COMMENT '活动后1-7天鲜果类商品的应付GMV',
	delivery_fruit_origin_gross_after_1_7 DECIMAL(38,18) COMMENT '活动后1-7天鲜果类商品的应付毛利润',
	delivery_notfruit_origin_amt_after_1_7 DECIMAL(38,18) COMMENT '活动后1-7天标品类商品的应付GMV',
	delivery_notfruit_origin_gross_after_1_7 DECIMAL(38,18) COMMENT '活动后1-7天标品类商品的应付毛利润',
	delivery_fruit_origin_amt_after_8_14 DECIMAL(38,18) COMMENT '活动后8-14天鲜果类商品的应付GMV',
	delivery_fruit_origin_gross_after_8_14 DECIMAL(38,18) COMMENT '活动后8-14天鲜果类商品的应付毛利润',
	delivery_notfruit_origin_amt_after_8_14 DECIMAL(38,18) COMMENT '活动后8-14天标品类商品的应付GMV',
	delivery_notfruit_origin_gross_after_8_14 DECIMAL(38,18) COMMENT '活动后8-14天标品类商品的应付毛利润',
	delivery_fruit_origin_amt_after_15_21 DECIMAL(38,18) COMMENT '活动后15-21天鲜果类商品的应付GMV',
	delivery_fruit_origin_gross_after_15_21 DECIMAL(38,18) COMMENT '活动后15-21天鲜果类商品的应付毛利润',
	delivery_notfruit_origin_amt_after_15_21 DECIMAL(38,18) COMMENT '活动后15-21天标品类商品的应付GMV',
	delivery_notfruit_origin_gross_after_15_21 DECIMAL(38,18) COMMENT '活动后15-21天标品类商品的应付毛利润',
	delivery_fruit_origin_amt_after_22_28 DECIMAL(38,18) COMMENT '活动后22-28天鲜果类商品的应付GMV',
	delivery_fruit_origin_gross_after_22_28 DECIMAL(38,18) COMMENT '活动后22-28天鲜果类商品的应付毛利润',
	delivery_notfruit_origin_amt_after_22_28 DECIMAL(38,18) COMMENT '活动后22-28天标品类商品的应付GMV',
	delivery_notfruit_origin_gross_after_22_28 DECIMAL(38,18) COMMENT '活动后22-28天标品类商品的应付毛利润'
) 
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据统计日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='营销活动客户交付效果评估统计表，用于分析营销活动对客户购买行为的影响，包含活动前后不同时间段的GMV和毛利润数据对比') 
LIFECYCLE 30;