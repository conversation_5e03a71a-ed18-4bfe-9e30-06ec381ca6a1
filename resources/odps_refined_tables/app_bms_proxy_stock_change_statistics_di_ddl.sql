CREATE TABLE IF NOT EXISTS app_bms_proxy_stock_change_statistics_di(
    `date_tag` STRING COMMENT '业务日期，格式：yyyyMMdd',
    `doc_type` BIGINT COMMENT '业务类型枚举：0-销售出库，1-退货入库，2-盘盈入库，3-盘亏出库，4-货损出库，5-出样出库',
    `doc_stock_type` BIGINT COMMENT '单据类型，取值范围：13-57',
    `doc_no` BIGINT COMMENT '业务ID，唯一标识业务单据',
    `doc_stock_no` STRING COMMENT '库存流水ID，唯一标识库存变动记录',
    `warehouse_no` BIGINT COMMENT '库存仓库ID，取值范围：10-155',
    `warehouse_name` STRING COMMENT '库存仓库名称',
    `doc_stock_change_date` DATETIME COMMENT '库存变动时间，格式：年月日时分秒',
    `supplier_id` STRING COMMENT '供应商ID',
    `supplier_name` STRING COMMENT '供应商名称',
    `purchaser` STRING COMMENT '采购员姓名',
    `batch` STRING COMMENT '商品批次号',
    `sku` STRING COMMENT '商品SKU编码',
    `title` STRING COMMENT '商品名称',
    `specification` STRING COMMENT '商品规格描述',
    `stock_change_quantity` BIGINT COMMENT '发生数量，可为负数（表示出库）',
    `doc_unit_price` DECIMAL(38,18) COMMENT '应付单价，单位：元',
    `doc_actual_unit_price` DECIMAL(38,18) COMMENT '实付单价，单位：元',
    `doc_price` DECIMAL(38,18) COMMENT '应付总价，单位：元',
    `doc_actual_price` DECIMAL(38,18) COMMENT '实付总价，单位：元',
    `order_no` STRING COMMENT '订单编号',
    `order_source` STRING COMMENT '订单来源枚举：xianmu-项目，saas-软件即服务',
    `cust_name` STRING COMMENT '客户名称',
    `kickback_rate` DECIMAL(38,18) COMMENT '佣金比例',
    `remark` STRING COMMENT '备注信息',
    `image` STRING COMMENT '图片URL或路径',
    `responsibility_source` STRING COMMENT '责任归属枚举：供应商，非供应商',
    `area_price` DECIMAL(38,18) COMMENT '区域售价，单位：元',
    `date_flag` DATETIME COMMENT '业务日期标志，格式：年月日',
    `doc_stock_change_finish_date` DATETIME COMMENT '业务变动完成日期，格式：年月日时分秒',
    `marketing_amount` DECIMAL(38,18) COMMENT '营销费用，单位：元'
)
COMMENT '代销对账信息表，记录代销业务中的库存变动、价格、订单等对账相关信息'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：yyyyMMdd')
STORED AS ALIORC
TBLPROPERTIES ('comment'='代销对账信息表，用于代销业务的对账和统计分析')
LIFECYCLE 30;