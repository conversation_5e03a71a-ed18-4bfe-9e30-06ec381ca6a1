CREATE TABLE IF NOT EXISTS app_saas_goods_no_sale_summary_di(
	time_tag STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计日期',
	tenant_id BIGINT COMMENT '租户ID，取值范围：2-118，表示SaaS平台的租户标识',
	sku_id BIGINT COMMENT 'SaaS SKU ID，取值范围：100039-126031，表示商品的SKU标识',
	item_id BIGINT COMMENT '商品ID，取值范围：40-44884，表示商品的唯一标识',
	sale_price DECIMAL(38,18) COMMENT '商品售价，单位为元，支持18位小数精度'
) 
COMMENT 'SaaS平台近15天滞销货品汇总表，统计各租户滞销商品信息'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='SaaS平台滞销商品统计表，用于分析近15天无销售记录的商品情况') 
LIFECYCLE 30;