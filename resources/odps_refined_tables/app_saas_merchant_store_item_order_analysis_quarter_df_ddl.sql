CREATE TABLE IF NOT EXISTS app_saas_merchant_store_item_order_analysis_quarter_df(
	tenant_id BIGINT COMMENT '租户ID，取值范围：2-10',
	time_tag STRING COMMENT '时间标签，格式：yyyyMMdd（年月日），表示季度起始月份的月初日期',
	item_id BIGINT COMMENT '商品ID，取值范围：1-1386',
	store_id BIGINT COMMENT '门店ID，取值范围：1-2365',
	average_order_period DECIMAL(38,18) COMMENT '平均订货周期（天）',
	average_order_period_last_period DECIMAL(38,18) COMMENT '上周期平均订货周期（天）',
	average_order_period_upper_period DECIMAL(38,18) COMMENT '平均订货周期环比变化率',
	order_amount BIGINT COMMENT '订货数量，取值范围：1-383',
	order_amount_last_period BIGINT COMMENT '上周期订货数量，取值范围：0-239',
	order_amount_upper_period DECIMAL(38,18) COMMENT '订货数量环比变化率',
	order_price DECIMAL(38,18) COMMENT '订货金额（元）',
	order_price_last_period DECIMAL(38,18) COMMENT '上周期订货金额（元）',
	order_price_upper_period DECIMAL(38,18) COMMENT '订货金额环比变化率',
	last_order_time STRING COMMENT '最后订货日期，格式：yyyy-MM-dd（年月日）',
	last_order_amount BIGINT COMMENT '最后订货数量，取值范围：1-250',
	last_order_price DECIMAL(38,18) COMMENT '最后订货金额（元）'
) 
COMMENT 'SaaS门店商品季度订货分析表，包含商品在各门店的订货周期、数量、金额等指标的季度分析数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd（年月日）') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='SaaS平台门店商品季度订货分析事实表，用于分析商品在各门店的订货行为和趋势') 
LIFECYCLE 30;