```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_stock_dashboard_history_di` (
  `view_date` DATETIME COMMENT '视图日期，年月日时分秒格式，表示数据统计的具体日期时间',
  `sku_id` STRING COMMENT 'SKU编号，商品最小库存单位的唯一标识',
  `pd_id` BIGINT COMMENT '商品编号，商品的唯一标识ID',
  `warehouse_no` BIGINT COMMENT '仓库编号，取值范围：2-176，表示不同仓库的唯一标识',
  `sales_quantity` BIGINT COMMENT '销量出库数量，取值范围：0-1303，表示实际销售出库的商品数量',
  `transfer_out_quantity` BIGINT COMMENT '调拨出库量，取值范围：0-500，表示仓库间调拨出库的商品数量',
  `consumption` BIGINT COMMENT '消耗量，取值范围：0-1303，表示商品的实际消耗数量',
  `enabled_quantity` BIGINT COMMENT '可用库存数量，取值范围：0-1090130，表示当前可用的库存数量',
  `on_way_quantity` BIGINT COMMENT '采购在途库存数量，取值范围：0-1000，表示采购订单已下单但未到货的在途库存',
  `allocate_in_quantity` BIGINT COMMENT '调拨在途库存数量，取值范围：0-42，表示调拨入库但未到货的在途库存',
  `init_quantity` BIGINT COMMENT '期初库存数量，取值范围：0-1090130，表示统计周期开始时的初始库存数量',
  `terminal_enabled_quantity` BIGINT COMMENT '期末可用库存数量，取值范围：0-1090130，表示统计周期结束时的可用库存数量',
  `on_way_order_quantity` BIGINT COMMENT '采购订单在途数量，取值范围：0-300，表示已下单采购但未到货的商品数量',
  `order_sales_quantity` BIGINT COMMENT '订单销量，取值范围：0-800，表示订单层面的销售数量',
  `timing_delivery_out_quantity` BIGINT COMMENT '省心送计划出库量，取值范围：0-54，表示省心送服务的计划出库数量'
) 
COMMENT '库存罗盘历史数据表，记录商品库存相关的历史统计指标，包括销售、调拨、消耗、库存状态等维度数据'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，yyyyMMdd格式日期字符串，表示数据所属的业务日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment'='库存罗盘历史数据表，用于库存管理和分析',
  'last_data_modified_time'='2025-09-18 02:57:56'
)
LIFECYCLE 30;
```