CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_dlv_delivery_replace_mi` (
  `month` STRING COMMENT '月份，格式为yyyyMM',
  `origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '实付总金额',
  `deliver_total_weight` DECIMAL(38,18) COMMENT '配送总重量（单位：千克）',
  `brand_cnt` BIGINT COMMENT '客户数（品牌数量）',
  `cust_cnt` BIGINT COMMENT '门店数（客户门店数量）',
  `order_cnt` BIGINT COMMENT '订单数',
  `data_source` STRING COMMENT '数据来源：鲜沐-鲜沐系统，SaaS-SaaS系统'
)
COMMENT '履约代仓日汇总表，记录每日代仓配送的履约数据汇总信息'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '履约代仓日汇总表',
  'lifecycle' = '30'
);