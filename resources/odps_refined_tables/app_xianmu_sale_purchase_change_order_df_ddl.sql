```sql
CREATE TABLE IF NOT EXISTS app_xianmu_sale_purchase_change_order_df(
    purchase_no STRING COMMENT '采购批次号，唯一标识一个采购批次',
    purchase_type STRING COMMENT '采购类型：普通采购',
    biz_type STRING COMMENT '业务类型：销转采',
    purchase_manager STRING COMMENT '采购负责人姓名',
    purchase_status STRING COMMENT '采购状态：已发布',
    supplier_confirmed STRING COMMENT '供货商确认状态：未确认',
    purchase_time DATETIME COMMENT '采购日期时间，格式为年月日时分秒',
    warehouse_no BIGINT COMMENT '收货仓库编号',
    warehouse_name STRING COMMENT '收货仓库名称',
    warehouse_manager STRING COMMENT '收货负责人姓名',
    order_no STRING COMMENT '销售单号，关联销售订单',
    remark STRING COMMENT '备注信息',
    purchase_entity STRING COMMENT '采购主体公司名称',
    purchase_order_time DATETIME COMMENT '采购下单时间，格式为年月日时分秒'
)
COMMENT '销转采采购单主表，记录销售转采购的采购订单主信息，包括采购批次、类型、状态、时间、仓库等核心信息'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据所属日期')
STORED AS ALIORC
TBLPROPERTIES ('comment'='销转采采购单主表，用于存储销售转采购的采购订单主数据')
LIFECYCLE 30;
```