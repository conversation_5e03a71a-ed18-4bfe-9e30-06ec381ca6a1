```sql
CREATE TABLE IF NOT EXISTS app_stc_warehouse_batch_sku_cost_df(
    `date` STRING COMMENT '业务日期，格式：yyyy-MM-dd，表示成本计算的业务日期',
    `warehouse_no` BIGINT COMMENT '仓库编号，唯一标识一个仓库',
    `warehouse_name` STRING COMMENT '仓库名称，如：贵阳总仓、重庆总仓等',
    `batch_no` STRING COMMENT '采购批次号，唯一标识一次采购批次',
    `product_batch_no` STRING COMMENT '生产批次号，唯一标识一个生产批次',
    `sku_id` STRING COMMENT '商品编码，唯一标识一个SKU',
    `sku_disc` STRING COMMENT '商品规格描述，如：10KG*1箱',
    `spu_name` STRING COMMENT '商品名称，即SPU名称',
    `category1` STRING COMMENT '一级类目，商品所属的一级分类，如：乳制品',
    `sku_property` STRING COMMENT 'SKU性质，取值范围：常规、活动、临保、拆包、破袋',
    `sku_sale_unit` DECIMAL(38,18) COMMENT '当前售价，单位：元，保留18位小数精度',
    `store_quantity` BIGINT COMMENT '库存量，当前库存数量',
    `batch_quantity` BIGINT COMMENT '批次量，当前批次的总数量',
    `batch_self_cost` DECIMAL(38,18) COMMENT '批次自提成本，单位：元，保留18位小数精度',
    `batch_allocate_cost` DECIMAL(38,18) COMMENT '批次调拨成本，单位：元，保留18位小数精度',
    `batch_1_quantity` BIGINT COMMENT '批次1库存量，第1个批次的库存数量',
    `batch_1_self_cost` DECIMAL(38,18) COMMENT '批次1自提成本，单位：元，保留18位小数精度',
    `batch_1_allocate_cost` DECIMAL(38,18) COMMENT '批次1调拨成本，单位：元，保留18位小数精度',
    `batch_2_quantity` BIGINT COMMENT '批次2库存量，第2个批次的库存数量',
    `batch_2_self_cost` DECIMAL(38,18) COMMENT '批次2自提成本，单位：元，保留18位小数精度',
    `batch_2_allocate_cost` DECIMAL(38,18) COMMENT '批次2调拨成本，单位：元，保留18位小数精度',
    `batch_3_quantity` BIGINT COMMENT '批次3库存量，第3个批次的库存数量',
    `batch_3_self_cost` DECIMAL(38,18) COMMENT '批次3自提成本，单位：元，保留18位小数精度',
    `batch_3_allocate_cost` DECIMAL(38,18) COMMENT '批次3调拨成本，单位：元，保留18位小数精度',
    `batch_4_quantity` BIGINT COMMENT '批次4库存量，第4个批次的库存数量',
    `batch_4_self_cost` DECIMAL(38,18) COMMENT '批次4自提成本，单位：元，保留18位小数精度',
    `batch_4_allocate_cost` DECIMAL(38,18) COMMENT '批次4调拨成本，单位：元，保留18位小数精度',
    `batch_5_quantity` BIGINT COMMENT '批次5库存量，第5个批次的库存数量',
    `batch_5_self_cost` DECIMAL(38,18) COMMENT '批次5自提成本，单位：元，保留18位小数精度',
    `batch_5_allocate_cost` DECIMAL(38,18) COMMENT '批次5调拨成本，单位：元，保留18位小数精度',
    `batch_6_quantity` BIGINT COMMENT '批次6库存量，第6个批次的库存数量',
    `batch_6_self_cost` DECIMAL(38,18) COMMENT '批次6自提成本，单位：元，保留18位小数精度',
    `batch_6_allocate_cost` DECIMAL(38,18) COMMENT '批次6调拨成本，单位：元，保留18位小数精度',
    `total_purchase_cost` DECIMAL(38,18) COMMENT '总采购单价，单位：元，保留18位小数精度',
    `total_unit_cost` DECIMAL(38,18) COMMENT '总单价，单位：元，保留18位小数精度',
    `total_cost` DECIMAL(38,18) COMMENT '总成本，单位：元，保留18位小数精度',
    `total_self_cost` DECIMAL(38,18) COMMENT '总自提单价，单位：元，保留18位小数精度',
    `total_allocate_cost` DECIMAL(38,18) COMMENT '总调拨单价，单位：元，保留18位小数精度'
) 
COMMENT '批次成本明细表，记录仓库中各个SKU的批次成本信息，包括自提成本和调拨成本等明细数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据计算的分区日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
    'comment'='批次成本明细表，用于存储仓库SKU的批次成本核算数据') 
LIFECYCLE 30;
```