```sql
CREATE TABLE IF NOT EXISTS app_crm_chatbi_cust_orders_2yrs_df(
    `order_no` STRING COMMENT '订单编号，唯一标识一个订单',
    `order_item_id` STRING COMMENT '订单子项编号，一个订单通常会包含多个子项，每个子项只会包含一个SKU的数据',
    `order_type` BIGINT COMMENT '订单类型：0-普通，1-省心送，2-运费，3-代下单，4-补发，10-虚拟商品（黄金卡、充值），11-直发采购，12-秒杀，13-预售',
    `order_sale_type` BIGINT COMMENT '订单扩展类型：普通订单（type=0）：0-普通，1-预售；虚拟商品（type=10）：0-黄金卡，1-充值',
    `order_status` BIGINT COMMENT '订单状态：1-待支付，2-待配送，3-待收货，6-已收货，8-已退款，10-支付中断超时关闭，11-已撤销，12-待支付尾款，13-尾款支付超时，14-手动关闭，15-人工退款中',
    `order_item_status` BIGINT COMMENT '订单项状态：1-待支付，2-待配送，3-待收货，6-已收货，8-已退款，10-支付中断超时关闭，11-已撤销，12-待支付尾款，13-尾款支付超时，14-手动关闭，15-人工退款中',
    `order_date` STRING COMMENT '下单日期，格式yyyyMMdd，年月日格式，如********',
    `order_time` DATETIME COMMENT '下单具体时间，年月日时分秒格式',
    `pay_date` STRING COMMENT '支付日期（不含尾款），格式yyyyMMdd，年月日格式，如********',
    `confirm_date` STRING COMMENT '确认售货日期，格式yyyyMMdd，年月日格式，如********',
    `sku_id` STRING COMMENT '商品SKU编号',
    `spu_name` STRING COMMENT '商品名称',
    `sku_disc` STRING COMMENT 'SKU规格描述',
    `cust_id` BIGINT COMMENT '客户ID',
    `cust_name` STRING COMMENT '客户名称',
    `cust_type` STRING COMMENT '客户类型；枚举：面包蛋糕,西餐,咖啡,甜品冰淇淋,茶饮,其他',
    `brand_name` STRING COMMENT '品牌的企业名称',
    `account_id` BIGINT COMMENT '客户下单的子账号ID',
    `area_name` STRING COMMENT '运营区域名称，隶属于某个运营大区，如杭州、广州，武汉普冷、长沙普冷等，不等于客户的注册城市',
    `product_type` BIGINT COMMENT '订单内商品类型：0-普通商品，1-赠品，2-换购商品',
    `sku_cnt` BIGINT COMMENT '商品下单件数，不含赠品',
    `gift_cnt` BIGINT COMMENT '赠品数量',
    `real_unit_amt` DECIMAL(38,18) COMMENT '实付单价，因折扣拆分问题，仅为近似值',
    `real_total_amt` DECIMAL(38,18) COMMENT '实付总价',
    `origin_unit_amt` DECIMAL(38,18) COMMENT '应付单价',
    `origin_total_amt` DECIMAL(38,18) COMMENT '应付总价',
    `delivery_amt` DECIMAL(38,18) COMMENT '运费金额',
    `real_total_gmv_amt` DECIMAL(38,18) COMMENT 'GMV专用实付总价，将预付子订单和代仓子订单中实付总价为0的部分，实付总价改为应付总价',
    `delivery_coupon_amt` DECIMAL(38,18) COMMENT '运费优惠金额',
    `category1` STRING COMMENT '商品的一级分类',
    `category2` STRING COMMENT '商品的二级分类',
    `category3` STRING COMMENT '商品的三级分类',
    `category4` STRING COMMENT '商品的四级分类（叶子类目）',
    `large_area_name` STRING COMMENT '运营大区名称，如杭州大区、广州大区',
    `cust_register_province` STRING COMMENT '客户注册省份，如浙江省、北京市',
    `cust_register_city` STRING COMMENT '客户注册城市，如杭州市、北京市',
    `cust_register_area` STRING COMMENT '客户注册区县，如滨江区、朝阳区',
    `cust_operate_status` STRING COMMENT '客户运营状态；枚举：0-正常，1-倒闭',
    `cust_business_line` STRING COMMENT '客户业务线；枚举：0-鲜沐，1-pop，2-SAAS',
    `bd_department` STRING COMMENT 'BD部门名称',
    `bd_region` STRING COMMENT 'BD区域名称',
    `cust_m3` STRING COMMENT '客户所属的M3管理者姓名',
    `cust_m2` STRING COMMENT '客户所属的M2管理者姓名',
    `cust_m1` STRING COMMENT '客户所属的M1管理者姓名',
    `bd_m3` STRING COMMENT 'BD M3管理者姓名（M2的上级，销售总监）',
    `bd_m2` STRING COMMENT 'BD M2管理者姓名（M1的上级，销售区域经理）',
    `bd_m1` STRING COMMENT 'BD M1管理者姓名（BD的上级，销售经理）',
    `bd_work_zone` STRING COMMENT 'BD工作区域',
    `bd_work_city` STRING COMMENT 'BD工作城市',
    `bd_name` STRING COMMENT 'BD姓名',
    `bd_id` BIGINT COMMENT 'BD编号',
    `cust_phone` STRING COMMENT '客户手机号'
)
COMMENT '两年内的自营订单全量表，包含订单基本信息、商品信息、客户信息、BD层级关系等完整数据'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式yyyyMMdd，年月日格式，如********，请总是使用最新的分区'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = '自营订单全量表，覆盖两年内的订单数据，用于订单分析和客户行为分析',
    'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```