CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_search_related_queries_df` (
  `query` STRING COMMENT '搜索查询词，如：芒果、牛奶、奶油等',
  `related_queries` STRING COMMENT '搜索词联想结果，包含与查询词相关的其他搜索词，以逗号分隔',
  `top_skus` STRING COMMENT '热门商品SKU列表，JSON格式，包含sku、spu_name、weight等信息',
  `query_frequency` BIGINT COMMENT '搜索频率，表示该查询词在统计周期内的搜索次数，取值范围：35-742257'
)
COMMENT '搜索词联想结果表，存储搜索词的联想推荐结果和热门商品信息，用于搜索推荐和商品关联分析'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '数据日期分区，格式为yyyyMMdd，表示数据所属的统计日期'
)
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
               'comment'='搜索词联想推荐表，包含搜索词、联想词、热门商品和搜索频率等信息')
LIFECYCLE 60;