CREATE TABLE IF NOT EXISTS app_search_frontcat_mini_rank_df(
    `category_cust_type` STRING COMMENT '前台一级类目-客户业态，枚举值包括：103-其他、103-咖啡、103-水果/果切/榨汁店、103-甜品冰淇淋、103-茶饮、103-蛋糕店、103-西餐、103-西餐披萨、103-面包蛋糕、105-其他等',
    `sku_ids` STRING COMMENT 'sku列表，多个sku用逗号分隔，如：N001S01R002,N001S01R005,N001H01Y003等'
)
COMMENT '搜索结果迷你榜单，用于展示搜索结果的商品排名信息'
PARTITIONED BY (
    `ds` STRING NOT NULL COMMENT '分区字段，数据日期，格式为yyyyMMdd（年月日）'
)
STORED AS ALIORC
TBLPROPERTIES ('comment'='搜索结果迷你榜单表，包含前台类目和对应sku的排名信息') 
LIFECYCLE 60;