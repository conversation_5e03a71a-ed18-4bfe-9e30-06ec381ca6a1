CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_service_kpi_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd',
  `cust_team` STRING COMMENT '客户类型：全量客户，平台客户',
  `channel_type` STRING COMMENT '渠道类型：鲜沐，SAAS',
  `after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后总金额',
  `after_sale_received_quality_amt` DECIMAL(38,18) COMMENT '已到货售后总金额（质量问题）',
  `after_sale_received_warehouse_amt` DECIMAL(38,18) COMMENT '已到货售后总金额（仓配问题）',
  `after_sale_received_other_amt` DECIMAL(38,18) COMMENT '已到货售后总金额（其他问题）',
  `delivery_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额',
  `after_sale_received_ratio` DECIMAL(38,18) COMMENT '已到货售后率',
  `delivery_evaluation_low_cnt` BIGINT COMMENT '司机评价差评数(3星以下)',
  `delivery_evaluation_cnt` BIGINT COMMENT '司机评价总数'
) 
COMMENT '客服KPI指标表，包含客服团队的各项关键绩效指标数据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd'
)
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
  'comment'='客服KPI指标表，包含客户类型、渠道类型、售后金额、履约金额、售后率、司机评价等关键绩效指标') 
LIFECYCLE 30;