CREATE TABLE IF NOT EXISTS app_saas_merchant_store_order_proportion_analysis_month_df(
	`tenant_id` BIGINT COMMENT '租户ID，取值范围：2-8',
	`time_tag` STRING COMMENT '时间标签，格式：yyyyMMdd（年月日）',
	`item_id` BIGINT COMMENT '商品ID，取值范围：1-573',
	`store_id` BIGINT COMMENT '门店ID，取值范围：1-655',
	`order_amount` BIGINT COMMENT '订货数量，取值范围：1-250',
	`order_amount_proportion` DECIMAL(38,18) COMMENT '订货数量占比，小数形式表示',
	`order_amount_proportion_upper_period` DECIMAL(38,18) COMMENT '订货数量占比环比，与上周期相比的变化比例',
	`order_price` DECIMAL(38,18) COMMENT '订货金额，单位：元',
	`order_price_proportion` DECIMAL(38,18) COMMENT '订货金额占比，小数形式表示',
	`order_price_proportion_upper_period` DECIMAL(38,18) COMMENT '订货金额占比环比，与上周期相比的变化比例',
	`total_order_amount` BIGINT COMMENT '总订货数量，取值范围：1-18716',
	`total_order_price` DECIMAL(38,18) COMMENT '总订货金额，单位：元',
	`order_amount_upper_period` BIGINT COMMENT '上周期订货数量，取值范围：1-142',
	`order_price_upper_period` DECIMAL(38,18) COMMENT '上周期订货金额，单位：元',
	`total_order_amount_upper_period` BIGINT COMMENT '上周期总订货数量，取值范围：58-14800',
	`total_order_price_upper_period` DECIMAL(38,18) COMMENT '上周期总订货金额，单位：元'
) 
COMMENT 'SaaS门店订货占比分析月表，用于分析各门店商品的订货数量、金额及其占比情况，包含环比数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：yyyyMMdd（年月日）') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='SaaS门店订货占比分析月表，提供门店商品维度的订货数量、金额、占比及环比分析数据') 
LIFECYCLE 30;