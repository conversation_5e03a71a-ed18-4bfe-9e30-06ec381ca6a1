CREATE TABLE IF NOT EXISTS app_trd_city_sku_price_df(
    `order_date` STRING COMMENT '订单日期，格式为yyyyMMdd，表示年月日',
    `area_no` STRING COMMENT '运营服务区编号，如9585、44125等',
    `area_name` STRING COMMENT '运营服务区名称，如苏州、武汉普冷等',
    `sku_id` STRING COMMENT '商品SKU编号，唯一标识一个具体商品规格',
    `spu_name` STRING COMMENT '商品SPU名称，表示商品品类',
    `sku_disc` STRING COMMENT '商品规格描述，如30KG*1包、1L*12瓶等',
    `real_price` DECIMAL(38,18) COMMENT '平均实付价格，单位：元',
    `sku_cnt` BIGINT COMMENT '实际商品销量，取值范围：1-1769',
    `cost_price` DECIMAL(38,18) COMMENT '平均成本价格，单位：元',
    `gross_profit` DECIMAL(38,18) COMMENT '总毛利润，单位：元',
    `prediction_price` DECIMAL(38,18) COMMENT '预测实付价格，单位：元',
    `prediction_price_max` DECIMAL(38,18) COMMENT '预测实付价格上限，单位：元',
    `prediction_price_min` DECIMAL(38,18) COMMENT '预测实付价格下限，单位：元',
    `prediction_sku_cnt` DECIMAL(38,18) COMMENT '预测商品销量',
    `prediction_sku_cnt_max` DECIMAL(38,18) COMMENT '预测商品销量上限',
    `prediction_sku_cnt_min` DECIMAL(38,18) COMMENT '预测商品销量下限',
    `prediction_profit` DECIMAL(38,18) COMMENT '预测商品毛利润，单位：元',
    `prediction_profit_max` DECIMAL(38,18) COMMENT '预测商品毛利润上限，单位：元',
    `prediction_profit_min` DECIMAL(38,18) COMMENT '预测商品毛利润下限，单位：元'
)
COMMENT '定价模型效果监控表，用于监控商品定价模型的预测效果和实际表现对比'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日')
STORED AS ALIORC
TBLPROPERTIES ('comment'='定价模型效果监控表，包含商品定价预测数据与实际销售数据的对比分析')
LIFECYCLE 30;