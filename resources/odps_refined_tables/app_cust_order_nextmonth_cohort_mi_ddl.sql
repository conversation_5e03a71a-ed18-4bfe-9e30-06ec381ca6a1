```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_cust_order_nextmonth_cohort_mi` (
  `month` STRING COMMENT '月份，格式为yyyyMM，如202508表示2025年8月',
  `cust_team` STRING COMMENT '客户团队类型，取值范围：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户、批发',
  `lastmonth_first_order_cust_cnt` BIGINT COMMENT '上月首购客户数',
  `lastmonth_first_order_invalid_cust_cnt` BIGINT COMMENT '上月首购客户中无效客户数',
  `lastmonth_first_order_cohort_cust_cnt` BIGINT COMMENT '上月首购中当月复购客户数',
  `lastmonth_first_order2_cohort_cust_cnt` BIGINT COMMENT '上月首购(下单天数>=2次)中当月复购客户数',
  `lastmonth_first_order_real_amt` DECIMAL(38,18) COMMENT '上月首购客户在上月实付金额',
  `lastmonth_first_order_cohort_real_amt` DECIMAL(38,18) COMMENT '上月首购中当月复购客户在上月实付金额',
  `lastmonth_first_order2_cohort_real_amt` DECIMAL(38,18) COMMENT '上月首购(下单天数>=2次)中当月复购客户在上月实付金额',
  `lastmonth_notfirst_order_cust_cnt` BIGINT COMMENT '上月非首购客户数',
  `lastmonth_notfirst_order_invalid_cust_cnt` BIGINT COMMENT '上月非首购客户中无效客户数',
  `lastmonth_notfirst_order_cohort_cust_cnt` BIGINT COMMENT '上月非首购中当月复购客户数',
  `lastmonth_notfirst_order2_cohort_cust_cnt` BIGINT COMMENT '上月非首购(下单天数>=2次)中当月复购客户数',
  `lastmonth_notfirst_order_real_amt` DECIMAL(38,18) COMMENT '上月非首购客户在上月实付金额',
  `lastmonth_notfirst_order_cohort_real_amt` DECIMAL(38,18) COMMENT '上月非首购中当月复购客户在上月实付金额',
  `lastmonth_notfirst_order2_cohort_real_amt` DECIMAL(38,18) COMMENT '上月非首购(下单天数>=2次)中当月复购客户在上月实付金额'
) 
COMMENT '客户次月复购统计表，用于分析客户在上月首次购买或非首次购买后的次月复购情况，包含客户数统计和实付金额统计'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '客户次月复购统计表',
  'lifecycle' = '30'
)
```