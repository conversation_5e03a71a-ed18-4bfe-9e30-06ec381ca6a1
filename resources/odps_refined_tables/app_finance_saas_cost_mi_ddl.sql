CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_finance_saas_cost_mi` (
  `month` STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
  `service_area` STRING COMMENT '大区，取值范围：未知、西南、华东、华南、华北、东北、西北等',
  `warehouse_no` BIGINT COMMENT '库存仓ID，数值型标识，取值范围：2-155',
  `warehouse_name` STRING COMMENT '库存仓名称，如：东莞总仓、华西总仓、嘉兴海盐总仓、南京总仓等',
  `category1` STRING COMMENT '商品一级类目，取值范围：鲜果、乳制品、其他',
  `sell_cost_amt` DECIMAL(38,18) COMMENT '含税销售成本，单位：元',
  `damage_cost_amt` DECIMAL(38,18) COMMENT '含税货损成本，单位：元',
  `sample_cost_amt` DECIMAL(38,18) COMMENT '含税出样成本，单位：元',
  `stocktake_cost_amt` DECIMAL(38,18) COMMENT '含税盘盈盘亏成本，单位：元',
  `sell_cost_amt_notax` DECIMAL(38,18) COMMENT '不含税销售成本，单位：元',
  `damage_cost_amt_notax` DECIMAL(38,18) COMMENT '不含税货损成本，单位：元',
  `sample_cost_amt_notax` DECIMAL(38,18) COMMENT '不含税出样成本，单位：元',
  `stocktake_cost_amt_notax` DECIMAL(38,18) COMMENT '不含税盘盈盘亏成本，单位：元'
) 
COMMENT '财务口径收入数据表，包含各仓库不同商品类别的销售成本、货损成本、出样成本和盘盈盘亏成本等财务数据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期，如20250917表示2025年9月17日'
)
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='财务SaaS成本明细表，用于财务分析和成本核算') 
LIFECYCLE 30;