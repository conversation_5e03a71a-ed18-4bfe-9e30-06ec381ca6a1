```sql
CREATE TABLE IF NOT EXISTS app_area_delivery_cost_di(
    area_no BIGINT COMMENT '城配仓编号，唯一标识每个城配仓',
    area_name STRING COMMENT '城配仓名称',
    service_area STRING COMMENT '服务区域：福建、贵阳、华东、华中、华南等',
    area_type STRING COMMENT '城配仓类型：内区-内部运营仓，外区-外部合作仓',
    path_cnt BIGINT COMMENT '配送线路数量',
    point_cnt BIGINT COMMENT '当前配送点位数',
    km_cnt DECIMAL(38,18) COMMENT '配送总公里数',
    delivery_amt DECIMAL(38,18) COMMENT '配送总费用（元）',
    point_cnt_b1d BIGINT COMMENT '昨日配送点位数',
    total_amt_b1d DECIMAL(38,18) COMMENT '昨日总成本（元）',
    point_cnt_b1w BIGINT COMMENT '上周配送点位数',
    total_amt_b1w DECIMAL(38,18) COMMENT '上周总成本（元）',
    delivery_total_amt DECIMAL(38,18) COMMENT '配送GVM（总交易额，元）'
) 
COMMENT '城配仓维度配送成本表，统计各城配仓的配送成本、点位数、公里数等关键指标'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='城配仓运营成本分析表，用于监控和优化配送成本效率') 
LIFECYCLE 30;
```