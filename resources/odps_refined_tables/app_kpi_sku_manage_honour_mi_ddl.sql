CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_kpi_sku_manage_honour_mi` (
  `month` STRING COMMENT '月份，格式为yyyyMM，例如202509表示2025年9月',
  `manage_type` STRING COMMENT '商品经营类型，取值范围：自营、代仓、代售、SAAS鲜沐自营',
  `origin_total_amt` DECIMAL(38,18) COMMENT '原始总金额(GMV)，单位：元',
  `real_total_amt` DECIMAL(38,18) COMMENT '实际总金额，单位：元',
  `origin_pay_rate` DECIMAL(38,18) COMMENT '应付毛利率，小数形式表示百分比',
  `real_pay_rate` DECIMAL(38,18) COMMENT '实付毛利率，小数形式表示百分比',
  `preferential_amt` DECIMAL(38,18) COMMENT '营销金额，单位：元',
  `refund_amt` DECIMAL(38,18) COMMENT '售后退款总金额，单位：元',
  `after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后总金额，单位：元',
  `after_sale_received_order_cnt` BIGINT COMMENT '已到货售后订单数',
  `cust_cnt` BIGINT COMMENT '客户数',
  `cust_unit_amt` DECIMAL(38,18) COMMENT '客户单价，单位：元/客户',
  `order_cnt` BIGINT COMMENT '订单数',
  `point_cnt` BIGINT COMMENT '点位数',
  `point_out_rate` DECIMAL(38,18) COMMENT '外区点位占比，小数形式表示百分比',
  `point_in_rate` DECIMAL(38,18) COMMENT '内区点位占比，小数形式表示百分比',
  `inventory_loss_amt` DECIMAL(38,18) COMMENT '盘亏总金额，单位：元',
  `inventory_profit_amt` DECIMAL(38,18) COMMENT '盘盈总金额，单位：元',
  `damage_amt` DECIMAL(38,18) COMMENT '货损总金额，单位：元',
  `damage_rate` DECIMAL(38,18) COMMENT '货损率，小数形式表示百分比',
  `replenish_out_amt` DECIMAL(38,18) COMMENT '补发出库总金额，单位：元',
  `return_in_amt` DECIMAL(38,18) COMMENT '退货入库总金额，单位：元',
  `sku_cnt` BIGINT COMMENT 'SKU数量',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储成本，单位：元',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本，单位：元',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送成本，单位：元',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提成本，单位：元',
  `other_amt` DECIMAL(38,18) COMMENT '其他成本，单位：元',
  `point_day_cnt` BIGINT COMMENT '均日点位数',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨成本，单位：元',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费，单位：元'
) 
COMMENT '履约口径KPI指标月汇总表，包含商品经营相关的各项财务和运营指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期，例如20250917表示2025年9月17日'
)
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='履约口径KPI指标月汇总表，用于存储商品经营相关的财务和运营指标数据') 
LIFECYCLE 30;