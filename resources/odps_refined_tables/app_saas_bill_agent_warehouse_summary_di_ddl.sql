CREATE TABLE IF NOT EXISTS app_saas_bill_agent_warehouse_summary_di(
	tenant_id BIGINT COMMENT '租户ID，取值范围：7-116',
	time_tag STRING COMMENT '时间标签，格式：yyyyMMdd，表示年月日',
	total_actual_warehouse_expenses DECIMAL(38,18) COMMENT '代仓实付费用合计（单位：元）',
	total_warehouse_expenses DECIMAL(38,18) COMMENT '代仓应付费用合计（单位：元）',
	total_after_sale_warehouse_expenses DECIMAL(38,18) COMMENT '已到货售后（代仓服务商责任）费用合计（单位：元）',
	total_sales_amount DECIMAL(38,18) COMMENT '销售金额总计（单位：元）',
	total_sales_amount_wechat_pay DECIMAL(38,18) COMMENT '销售金额总计（微信支付）（单位：元）',
	total_sales_amount_bill_balance_pay DECIMAL(38,18) COMMENT '销售金额总计（账期加余额支付）（单位：元）',
	after_sale_amount_wechat_pay DECIMAL(38,18) COMMENT '销售售后金额（微信支付）（单位：元）',
	after_sale_amount_bill_balance_pay DECIMAL(38,18) COMMENT '销售售后金额（账期加余额支付）（单位：元）',
	deduct_after_sales_amount_wechat_pay DECIMAL(38,18) COMMENT '扣除售后销售金额（微信支付）（单位：元）',
	deduct_after_sales_amount_bill_balance_pay DECIMAL(38,18) COMMENT '扣除售后销售金额（账期加余额支付）（单位：元）',
	delivery_fee_deduct_after_sales_amount DECIMAL(38,18) COMMENT '剔除售后的订单配送费（单位：元）'
) 
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='SAAS对账单-代仓账单概要表，记录各租户的代仓费用统计和销售金额汇总') 
LIFECYCLE 30;