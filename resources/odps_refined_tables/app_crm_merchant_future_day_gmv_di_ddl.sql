CREATE TABLE IF NOT EXISTS app_crm_merchant_future_day_gmv_di(
	`cust_id` BIGINT COMMENT '商户ID，唯一标识商户',
	`contact_id` BIGINT COMMENT '联系地址ID，唯一标识联系地址',
	`bd_id` BIGINT COMMENT '归属BD ID，公海为0，取值范围：0-1189140',
	`delivery_time` DATETIME COMMENT '计划配送时间，格式：年月日时分秒',
	`area_no` BIGINT COMMENT '商户所在运营区域编号，取值范围：1001-44269',
	`distribution_gmv` DECIMAL(38,18) COMMENT '配送GMV（总商品价值）',
	`fruit_gmv` DECIMAL(38,18) COMMENT '鲜果品类GMV',
	`dairy_gmv` DECIMAL(38,18) COMMENT '乳制品品类GMV',
	`non_dairy_gmv` DECIMAL(38,18) COMMENT '非乳制品品类GMV',
	`brand_gmv` DECIMAL(38,18) COMMENT '自营品牌GMV',
	`reward_gmv` DECIMAL(38,18) COMMENT '固定奖励SKU的GMV',
	`spu_num` BIGINT COMMENT 'SPU数（去重），取值范围：1-22',
	`estimated_income` DECIMAL(38,18) COMMENT '预估收益',
	`min_income_after` DECIMAL(38,18) COMMENT '达标后预估最低收益',
	`max_income_after` DECIMAL(38,18) COMMENT '达标后预估最高收益',
	`delivery_up_to_standard` BIGINT COMMENT '是否达标：0-未达标，1-已达标',
	`province` STRING COMMENT '省份名称',
	`city` STRING COMMENT '城市名称',
	`area` STRING COMMENT '区域名称'
)
COMMENT '商户当日及未来配送GMV表，记录商户配送相关的商品价值和收益预估数据'
PARTITIONED BY (
	`ds` STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
	'comment' = '商户配送GMV分析表，包含各品类GMV分解、收益预估和达标状态等信息',
	'columnar.nested.type' = 'true'
)
LIFECYCLE 30;