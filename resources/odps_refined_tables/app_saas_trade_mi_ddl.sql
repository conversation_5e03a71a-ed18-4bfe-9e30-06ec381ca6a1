CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_saas_trade_mi` (
  `month` STRING COMMENT '月份，格式为yyyyMM，例如：202509',
  `store_cnt` BIGINT COMMENT '门店总注册数',
  `direct_store_cnt` BIGINT COMMENT '直营门店注册数',
  `join_store_cnt` BIGINT COMMENT '加盟门店注册数',
  `managed_store_cnt` BIGINT COMMENT '托管门店注册数',
  `store_order_cnt` BIGINT COMMENT '门店总交易订单数',
  `direct_store_order_cnt` BIGINT COMMENT '直营门店交易订单数',
  `join_store_order_cnt` BIGINT COMMENT '加盟门店交易订单数',
  `managed_store_order_cnt` BIGINT COMMENT '托管门店交易订单数',
  `total_gmv` DECIMAL(38,18) COMMENT '总交易GMV（总交易额）',
  `direct_store_gmv` DECIMAL(38,18) COMMENT '直营门店GMV',
  `join_store_gmv` DECIMAL(38,18) COMMENT '加盟门店GMV',
  `managed_store_gmv` DECIMAL(38,18) COMMENT '托管门店GMV',
  `dc_sku_gmv` DECIMAL(38,18) COMMENT '代仓商品交易GMV',
  `dc_sku_gmv_rate` DECIMAL(38,18) COMMENT '代仓商品交易GMV占比（0-1之间的小数）',
  `khzy_sku_gmv` DECIMAL(38,18) COMMENT '客户自营商品交易GMV',
  `khzy_sku_gmv_rate` DECIMAL(38,18) COMMENT '客户自营商品交易GMV占比（0-1之间的小数）',
  `xianmu_sku_gmv` DECIMAL(38,18) COMMENT '鲜沐商品交易GMV',
  `xianmu_sku_gmv_rate` DECIMAL(38,18) COMMENT '鲜沐商品交易GMV占比（0-1之间的小数）',
  `bill_gmv` DECIMAL(38,18) COMMENT '账期GMV',
  `bill_gmv_rate` DECIMAL(38,18) COMMENT '账期GMV占比（0-1之间的小数）',
  `cash_gmv` DECIMAL(38,18) COMMENT '现结GMV',
  `cash_gmv_rate` DECIMAL(38,18) COMMENT '现结GMV占比（0-1之间的小数）',
  `xianmu_supply_sku_gmv` DECIMAL(38,18) COMMENT '鲜沐商品GMV（供应商原始GMV）',
  `xianmu_sku_add_gmv` DECIMAL(38,18) COMMENT '鲜沐商品品牌加价后GMV',
  `xianmu_sku_income_gmv` DECIMAL(38,18) COMMENT '鲜沐商品品牌加价收入',
  `xianmu_sku_income_gmv_rate` DECIMAL(38,18) COMMENT '鲜沐商品品牌加价收入占比（0-1之间的小数）',
  `dc_after_sale_nonarrival_sku_cnt` BIGINT COMMENT '代仓商品售后数量（未到货）',
  `dc_after_sale_nonarrival_amt` DECIMAL(38,18) COMMENT '代仓商品售后金额（未到货）',
  `dc_after_sale_arrival_sku_cnt` BIGINT COMMENT '代仓商品售后数量（已到货）',
  `dc_after_sale_arrival_amt` DECIMAL(38,18) COMMENT '代仓商品售后金额（已到货）',
  `khzy_after_sale_nonarrival_sku_cnt` BIGINT COMMENT '客户自营商品售后数量（未到货）',
  `khzy_after_sale_nonarrival_amt` DECIMAL(38,18) COMMENT '客户自营商品售后金额（未到货）',
  `khzy_after_sale_arrival_sku_cnt` BIGINT COMMENT '客户自营商品售后数量（已到货）',
  `khzy_after_sale_arrival_amt` DECIMAL(38,18) COMMENT '客户自营商品售后金额（已到货）',
  `xianmu_after_sale_nonarrival_sku_cnt` BIGINT COMMENT '鲜沐商品售后数量（未到货）',
  `xianmu_after_sale_nonarrival_amt` DECIMAL(38,18) COMMENT '鲜沐商品售后金额（未到货）',
  `xianmu_after_sale_arrival_sku_cnt` BIGINT COMMENT '鲜沐商品售后数量（已到货）',
  `xianmu_after_sale_arrival_amt` DECIMAL(38,18) COMMENT '鲜沐商品售后金额（已到货）',
  `fruit_gmv` DECIMAL(38,18) COMMENT '鲜果交易GMV',
  `not_fruit_gmv` DECIMAL(38,18) COMMENT '标品交易GMV',
  `khzy_gmv` DECIMAL(38,18) COMMENT '客户自营GMV（剔除供应商直发）',
  `supplier_direct_gmv` DECIMAL(38,18) COMMENT '供应商直发GMV'
) 
COMMENT 'SaaS总体监控月汇总表，包含门店注册、交易订单、GMV、售后等核心业务指标的月度汇总数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
               'comment'='SaaS业务监控月粒度汇总表，用于业务分析和监控') 
LIFECYCLE 30;