CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_spu_trade_selfowend_mi` (
  `month` STRING COMMENT '月份，格式为yyyyMM',
  `register_province` STRING COMMENT '注册省份',
  `register_city` STRING COMMENT '注册城市',
  `register_area` STRING COMMENT '注册区域',
  `cause_type` STRING COMMENT '业务类型；枚举：鲜沐,SAAS',
  `spu_id` STRING COMMENT 'SPU ID（商品ID）',
  `spu_name` STRING COMMENT 'SPU名称（商品名称）',
  `cust_type` STRING COMMENT '客户类型；枚举：面包蛋糕,西餐,咖啡,甜品冰淇淋,茶饮,其他',
  `brand_type` STRING COMMENT '大客户类型；枚举：大客户,普通,批发客户',
  `brand_name` STRING COMMENT '品牌名称',
  `category1` STRING COMMENT '一级类目',
  `category2_id` STRING COMMENT '二级类目ID',
  `category2` STRING COMMENT '二级类目名称',
  `category3_id` STRING COMMENT '三级类目ID',
  `category3` STRING COMMENT '三级类目名称',
  `category4_id` STRING COMMENT '四级类目ID',
  `category4` STRING COMMENT '四级类目名称',
  `origin_total_amt` DECIMAL(38,18) COMMENT '实付总金额（实际支付金额）',
  `real_total_amt` DECIMAL(38,18) COMMENT '应付总金额（应付金额）',
  `cust_cnt` BIGINT COMMENT '客户数量',
  `new_cust_cnt` BIGINT COMMENT '当月新客户数量（历史截止当月）',
  `order_time_cnt` DECIMAL(38,18) COMMENT '客户下单时间间隔之和，单位：天',
  `order_time_avg` DECIMAL(38,18) COMMENT '平均下单时间间隔，单位：天',
  `order_time_cnt_m` DECIMAL(38,18) COMMENT '客户下单时间间隔之和，单位：分钟',
  `order_time_avg_m` DECIMAL(38,18) COMMENT '平均下单时间间隔，单位：分钟',
  `before_month_cust_cnt` BIGINT COMMENT 'T-1月客户数量',
  `month_cust_cnt` BIGINT COMMENT 'T-1月到T月客户数量'
)
COMMENT '自营品城市整体交易数据月表，包含自营商品在各城市的交易统计信息'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMM，表示数据所属月份'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment'='自营品城市整体交易数据月表',
  'lifecycle'='30'
);