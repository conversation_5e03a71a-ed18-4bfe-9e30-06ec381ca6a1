CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_sku_log_search_mi`(
	`month` STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
	`sku_id` STRING COMMENT '商品SKU ID，唯一标识一个具体商品',
	`spu_name` STRING COMMENT '商品名称，即SPU名称',
	`search_key` STRING COMMENT '用户搜索关键词',
	`pv` BIGINT COMMENT '月PV值，表示该商品在该月份被搜索关键词触发的页面浏览量，取值范围：大于等于100'
)
COMMENT '人群浏览行为偏好表，记录用户对不同商品的搜索行为偏好数据'
PARTITIONED BY (`ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示数据日期')
STORED AS ALIORC
TBLPROPERTIES ('comment'='人群浏览行为偏好表，用于分析用户搜索行为和商品偏好')
LIFECYCLE 30;