```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_pop_purchaser_supplier_final_di` (
  `supplier_name` STRING COMMENT '供应商名称',
  `purchaser` STRING COMMENT '采购员姓名',
  `sku_id` STRING COMMENT 'SKU编号，商品最小库存单位标识',
  `sku_disc` STRING COMMENT '商品描述，包含规格、等级、包装等信息',
  `spu_name` STRING COMMENT '商品名称，标准产品单元名称',
  `purchase_quantity` BIGINT COMMENT '应结数量：采购商品数量，取值范围0-46，均值2.99，标准差5.77',
  `purchase_amount` DECIMAL(38,18) COMMENT '应结金额：采购商品总金额，精度38位，小数位18位',
  `back_quantity` BIGINT COMMENT '退供应扣数量：退货给供应商的数量，取值范围0-0（无退货）',
  `back_amount` DECIMAL(38,18) COMMENT '退供应扣金额：退货给供应商的金额，精度38位，小数位18位',
  `quality_amount` DECIMAL(38,18) COMMENT '质量问题应扣金额：因质量问题需要扣除的金额，精度38位，小数位18位',
  `settlement_amount` DECIMAL(38,18) COMMENT '实结金额：最终结算金额，计算公式=应结金额-退供应扣金额-质量问题应扣金额，精度38位，小数位18位'
) 
COMMENT 'POP鲜沐应结数据表，记录供应商与采购员之间的商品结算信息，包括采购数量、金额、退货扣款和质量问题扣款等'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = 'POP鲜沐供应商采购结算数据表',
  'lifecycle' = '30'
);
```