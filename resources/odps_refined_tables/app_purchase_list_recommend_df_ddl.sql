CREATE TABLE IF NOT EXISTS app_purchase_list_recommend_df(
    m_id BIGINT COMMENT '商家ID，取值范围：10-6593',
    sku STRING COMMENT '商品SKU编码，如：L001S01R001、K001N01Z001、N001S01R002等',
    product_id BIGINT COMMENT '商品ID，取值范围：1-17930',
    recommend_sort BIGINT COMMENT '推荐排序序号，取值范围：1-20，数值越小排序越靠前'
)
COMMENT '进货单-推荐商品表，用于存储商家进货单中的商品推荐排序信息'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='进货单推荐商品排序表，包含商家ID、商品SKU、商品ID和推荐排序信息') 
LIFECYCLE 30;