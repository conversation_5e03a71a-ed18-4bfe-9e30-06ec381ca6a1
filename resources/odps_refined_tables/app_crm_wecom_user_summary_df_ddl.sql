CREATE TABLE IF NOT EXISTS app_crm_wecom_user_summary_df(
	bd_id BIGINT COMMENT '销售ID，唯一标识一个销售人员',
	bd_name STRING COMMENT '销售人员姓名',
	private_sea_count BIGINT COMMENT '私海客户数量，取值范围：1-2409',
	effective_wecom_user_count BIGINT COMMENT '有效企微客户数量，取值范围：0-485',
	wecom_user_count BIGINT COMMENT '企微客户总数量，取值范围：0-559',
	bd_delete_wecom_count BIGINT COMMENT '销售删除客户数量，取值范围：0-51',
	user_delete_wecom_count BIGINT COMMENT '用户删除客户数量，取值范围：0-59',
	delete_wecom_count BIGINT COMMENT '互删客户数量，取值范围：0-12'
)
COMMENT 'CRM企微用户统计表，包含销售人员的企业微信客户相关统计数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期')
STORED AS ALIORC
TBLPROPERTIES ('comment'='CRM企业微信用户统计汇总表，用于分析销售人员的客户管理情况')
LIFECYCLE 30;