```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_saas_merchant_store_order_proportion_analysis_quarter_df` (
  `tenant_id` BIGINT COMMENT '租户ID，取值范围：2-8',
  `time_tag` STRING COMMENT '时间标签，格式：yyyyMMdd（年月日），表示季度统计的时间点',
  `item_id` BIGINT COMMENT '商品ID，取值范围：1-889',
  `store_id` BIGINT COMMENT '门店ID，取值范围：1-801',
  `order_amount` BIGINT COMMENT '订货数量，取值范围：1-350',
  `order_amount_proportion` DECIMAL(38,18) COMMENT '订货数量占比，小数形式表示的比例值',
  `order_amount_proportion_upper_period` DECIMAL(38,18) COMMENT '订货数量占比环比，与上周期相比的变化比例',
  `order_price` DECIMAL(38,18) COMMENT '订货金额，单位为元',
  `order_price_proportion` DECIMAL(38,18) COMMENT '订货金额占比，小数形式表示的比例值',
  `order_price_proportion_upper_period` DECIMAL(38,18) COMMENT '订货金额占比环比，与上周期相比的变化比例',
  `total_order_amount` BIGINT COMMENT '总订货数量，当前季度的总订货量',
  `total_order_price` DECIMAL(38,18) COMMENT '总订货金额，当前季度的总订货金额',
  `order_amount_upper_period` BIGINT COMMENT '上周期订货数量，上一季度的订货数量',
  `order_price_upper_period` DECIMAL(38,18) COMMENT '上周期订货金额，上一季度的订货金额',
  `total_order_amount_upper_period` BIGINT COMMENT '上周期总订货数量，上一季度的总订货量',
  `total_order_price_upper_period` DECIMAL(38,18) COMMENT '上周期总订货金额，上一季度的总订货金额'
) 
COMMENT 'SaaS门店订货占比分析季度表，用于分析各门店商品订货的数量和金额占比情况，包含环比数据对比'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：yyyyMMdd（年月日），表示数据加载日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = 'SaaS门店订货季度占比分析表，支持门店维度的订货数量和金额占比分析及环比计算',
  'lifecycle' = '365'
);
```