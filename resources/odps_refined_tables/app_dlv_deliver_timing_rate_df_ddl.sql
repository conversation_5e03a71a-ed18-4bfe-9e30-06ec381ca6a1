CREATE TABLE IF NOT EXISTS app_dlv_deliver_timing_rate_df(
    `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
    `timing_deliver_30_cust_cnt` BIGINT COMMENT '30天内省心送完成的客户数，取值范围：0-2917',
    `timing_deliver_30_60_cust_cnt` BIGINT COMMENT '30-60天内省心送完成的客户数，取值范围：0-2412',
    `timing_deliver_60_90_cust_cnt` BIGINT COMMENT '60-90天内省心送完成的客户数，取值范围：0-1697',
    `timing_deliver_90_cust_cnt` BIGINT COMMENT '90天以上省心送完成的客户数，取值范围：0-1802',
    `timing_deliver_30_origin_amt` DECIMAL(38,18) COMMENT '30天内省心送完成应付总金额',
    `timing_deliver_30_60_origin_amt` DECIMAL(38,18) COMMENT '30-60天内省心送完成应付总金额',
    `timing_deliver_60_90_origin_amt` DECIMAL(38,18) COMMENT '60-90天内省心送完成应付总金额',
    `timing_deliver_90_origin_amt` DECIMAL(38,18) COMMENT '90天以上省心送完成应付总金额',
    `timing_deliver_30_real_amt` DECIMAL(38,18) COMMENT '30天内省心送完成实付总金额',
    `timing_deliver_30_60_real_amt` DECIMAL(38,18) COMMENT '30-60天内省心送完成实付总金额',
    `timing_deliver_60_90_real_amt` DECIMAL(38,18) COMMENT '60-90天内省心送完成实付总金额',
    `timing_deliver_90_real_amt` DECIMAL(38,18) COMMENT '90天以上省心送完成实付总金额',
    `timing_order_cust_cnt` BIGINT COMMENT '当日省心送客户数，取值范围：1-6246',
    `timing_order_origin_amt` DECIMAL(38,18) COMMENT '当日省心送应付总金额',
    `timing_order_real_amt` DECIMAL(38,18) COMMENT '当日省心送实付总金额'
)
COMMENT '完成配送省心送明细数据表，包含不同时间段的省心送完成客户数、应付金额和实付金额等统计指标'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据加载的分区日期'
)
STORED AS ALIORC
TBLPROPERTIES ('comment'='省心送配送时效统计表，用于分析不同时间段内省心送服务的完成情况和金额统计')
LIFECYCLE 30;