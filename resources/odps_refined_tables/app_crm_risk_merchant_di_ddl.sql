CREATE TABLE IF NOT EXISTS app_crm_risk_merchant_di(
	m_id BIGINT COMMENT '客户ID，唯一标识一个门店',
	merchant_size STRING COMMENT '风控门店类型：单店',
	trigger_occasions BIGINT COMMENT '触发场景：0-新注册门店，1-存量动销门店',
	trigger_classification BIGINT COMMENT '命中分类：0-疑似重复，1-疑似虚假，2-疑似换壳',
	trigger_condition STRING COMMENT '命中条件，多个条件用;分割',
	similar_m_id BIGINT COMMENT '相似客户ID',
	similar_size STRING COMMENT '相似门店类型：平台单店，平台大客户-品牌名称，SAAS大客户-品牌名称',
	similar_name STRING COMMENT '相似门店名称/相似员工名称',
	similar_phone STRING COMMENT '相似门店电话',
	source_type BIGINT COMMENT '相似门店归属：0-xm，1-saas',
	day_tag STRING COMMENT '时间标签，格式为yyyyMMdd，表示年月日'
)
COMMENT 'CRM风控门店表，用于存储风控识别出的风险门店信息，包括触发场景、命中分类、相似门店等信息'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日')
STORED AS ALIORC
TBLPROPERTIES ('comment'='CRM风控门店表，记录风控识别结果和相似门店信息')
LIFECYCLE 30;