```sql
CREATE TABLE IF NOT EXISTS app_stock_dashboard_history_df(
    `view_date` DATETIME COMMENT '视图日期，格式为年月日时分秒，表示数据统计的具体时间点',
    `sku_id` STRING COMMENT 'SKU编号，商品库存单位的唯一标识',
    `pd_id` BIGINT COMMENT '商品编号，商品的唯一标识',
    `warehouse_no` BIGINT COMMENT '仓库编号，仓库的唯一标识，取值范围：2-176',
    `sales_quantity` BIGINT COMMENT '销量出库数量，商品实际销售出库的数量',
    `transfer_out_quantity` BIGINT COMMENT '调拨出库量，商品调拨出库的数量',
    `consumption` BIGINT COMMENT '消耗量，商品消耗的总数量',
    `enabled_quantity` BIGINT COMMENT '可用库存，当前可用的库存数量',
    `on_way_quantity` BIGINT COMMENT '采购在途库存，采购订单中在途的商品数量',
    `allocate_in_quantity` BIGINT COMMENT '调拨在途库存，调拨单中在途的商品数量',
    `init_quantity` BIGINT COMMENT '期初库存，统计周期开始时的库存数量',
    `terminal_enabled_quantity` BIGINT COMMENT '期末可用库存，统计周期结束时的可用库存数量',
    `terminal_way_quantity` BIGINT COMMENT '期末采购在途库存，统计周期结束时的采购在途数量',
    `terminal_allocate_in_quantity` BIGINT COMMENT '期末调拨在途库存，统计周期结束时的调拨在途数量',
    `on_way_order_quantity` BIGINT COMMENT '采购订单在途数量，采购订单中尚未到货的商品数量',
    `terminal_order_quantity` BIGINT COMMENT '期末采购订单在途数量，统计周期结束时的采购订单在途数量',
    `order_sale_cnt` BIGINT COMMENT '订单销量，订单中的销售数量',
    `timing_delivery_out_quantity` BIGINT COMMENT '省心送计划出库量，定时配送计划的出库数量'
)
COMMENT '罗盘历史数据表，存储商品库存和销售相关的历史统计数据，用于库存分析和销售监控'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据所属的统计日期'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = '商品库存仪表盘历史数据表',
    'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```