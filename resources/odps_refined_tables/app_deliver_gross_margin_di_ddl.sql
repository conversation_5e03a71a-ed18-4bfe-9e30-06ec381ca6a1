CREATE TABLE IF NOT EXISTS app_deliver_gross_margin_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务发生日期',
	`cust_class` STRING COMMENT '客户大类，取值范围：大客户（茶百道）、大客户（非茶百道）、批发、普通（非品牌）、普通（品牌）',
	`warehouse_no` BIGINT COMMENT '库存仓ID，数值型标识，取值范围：2-155',
	`warehouse_name` STRING COMMENT '库存仓名称',
	`spu_name` STRING COMMENT 'SPU名称，商品标准化产品单元名称',
	`category_1` STRING COMMENT '一级类目，商品分类的第一层级',
	`sku_type` STRING COMMENT 'SKU类型，取值范围：自营、代仓',
	`origin_total_amt` DECIMAL(38,18) COMMENT '配送总金额，精确到18位小数',
	`cost_amt` DECIMAL(38,18) COMMENT '配送总成本，精确到18位小数',
	`gross_margin` DECIMAL(38,18) COMMENT '毛利润，精确到18位小数'
) 
COMMENT '每日库存仓客户类型组合维度日汇总数据，按库存仓、客户类型、商品维度统计配送金额、成本和毛利润'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='配送业务毛利润分析表，用于库存仓客户类型组合维度的日度汇总分析') 
LIFECYCLE 30;