```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_cust_register_order_mi` (
  `register_month` STRING COMMENT '注册月份，格式为yyyyMM，如202201表示2022年1月',
  `cust_team` STRING COMMENT '客户团队类型，枚举值：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户、批发',
  `register_cust_cnt` BIGINT COMMENT '当月注册客户数',
  `first_order_cust_cnt_0` BIGINT COMMENT 'm0首单客户数（注册当月完成首单的客户数）',
  `first_order_cust_cnt_1` BIGINT COMMENT 'm1首单客户数（注册后第1个月完成首单的客户数）',
  `first_order_cust_cnt_2` BIGINT COMMENT 'm2首单客户数（注册后第2个月完成首单的客户数）',
  `first_order_cust_cnt_3` BIGINT COMMENT 'm3首单客户数（注册后第3个月完成首单的客户数）',
  `first_order_cust_cnt_4` BIGINT COMMENT 'm4首单客户数（注册后第4个月完成首单的客户数）',
  `first_order_cust_cnt_5` BIGINT COMMENT 'm5首单客户数（注册后第5个月完成首单的客户数）',
  `first_order_cust_cnt_6` BIGINT COMMENT 'm6首单客户数（注册后第6个月完成首单的客户数）',
  `first_order_cust_cnt_7` BIGINT COMMENT 'm7首单客户数（注册后第7个月完成首单的客户数）',
  `first_order_cust_cnt_8` BIGINT COMMENT 'm8首单客户数（注册后第8个月完成首单的客户数）',
  `first_order_cust_cnt_9` BIGINT COMMENT 'm9首单客户数（注册后第9个月完成首单的客户数）',
  `first_order_cust_cnt_10` BIGINT COMMENT 'm10首单客户数（注册后第10个月完成首单的客户数）',
  `first_order_cust_cnt_11` BIGINT COMMENT 'm11首单客户数（注册后第11个月完成首单的客户数）',
  `first_order_cust_cnt_12` BIGINT COMMENT 'm12首单客户数（注册后第12个月完成首单的客户数）'
) 
COMMENT '注册用户首购月分布表，统计不同注册月份、不同客户团队类型的用户在注册后各个月份的首单购买情况'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC  
TBLPROPERTIES ('comment'='注册用户首购月分布表，用于分析用户注册后的首单转化趋势和客户团队表现')
LIFECYCLE 30;
```