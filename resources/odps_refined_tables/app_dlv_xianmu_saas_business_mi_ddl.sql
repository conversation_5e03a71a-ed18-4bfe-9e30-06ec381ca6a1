```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_dlv_xianmu_saas_business_mi`(
  `month` STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
  `business_type` STRING COMMENT '业务类型：批发,自营,代仓,代售，SAAS鲜沐自营，SAAS鲜沐代仓，SAAS品牌方自营',
  `origin_total_amt` DECIMAL(38,18) COMMENT '原始总金额（元）',
  `real_total_amt` DECIMAL(38,18) COMMENT '实际总金额（元）',
  `cost_amt` DECIMAL(38,18) COMMENT '成本金额（元）',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费金额（元）',
  `point_cnt` BIGINT COMMENT '点位数',
  `sku_cnt` BIGINT COMMENT 'SKU数量',
  `total_sku_weight` DECIMAL(38,18) COMMENT '履约重量（KG）',
  `after_sale_received_order_cnt` BIGINT COMMENT '已到货售后总订单数',
  `after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后总金额（元）',
  `inventory_loss_amt` DECIMAL(38,18) COMMENT '盘亏总金额（元）',
  `inventory_profit_amt` DECIMAL(38,18) COMMENT '盘盈总金额（元）',
  `damage_amt` DECIMAL(38,18) COMMENT '货损总金额（元）',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储成本（元）',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本（元）',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送成本（元）',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提成本（元）',
  `other_amt` DECIMAL(38,18) COMMENT '其他成本（元）',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨成本（元）',
  `heytea_storage_amt` DECIMAL(38,18) COMMENT '喜茶仓储成本（元）',
  `heytea_arterial_roads_amt` DECIMAL(38,18) COMMENT '喜茶调拨成本（元）',
  `heytea_deliver_amt` DECIMAL(38,18) COMMENT '喜茶配送成本（元）',
  `heytea_way_deliver_amt` DECIMAL(38,18) COMMENT '喜茶专配成本（元）'
) 
COMMENT '各业务线数据大盘，包含批发、自营、代仓、代售、SAAS鲜沐自营、SAAS鲜沐代仓、SAAS品牌方自营等业务线的财务和运营数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
               'comment'='各业务线数据大盘表，用于业务数据分析和监控') 
LIFECYCLE 30;
```