CREATE TABLE IF NOT EXISTS app_crm_sku_performance_mi(
	month STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
	sku_id STRING COMMENT '商品SKU ID',
	spu_name STRING COMMENT '商品名称',
	sku_spec STRING COMMENT '商品规格描述',
	sku_category STRING COMMENT '商品类目：枚举值-自营品牌、鲜果、乳制品、非乳制品',
	category4 STRING COMMENT '商品四级分类名称',
	administrative_city STRING COMMENT '商家对应的行政城市名称',
	m1 STRING COMMENT '城市负责人（M1）姓名',
	m2 STRING COMMENT '区域负责人（M2）姓名',
	m3 STRING COMMENT '部门负责人（M3）姓名',
	is_simple STRING COMMENT '是否单店：枚举值-是、否',
	cust_type STRING COMMENT '客户类型：枚举值-面包蛋糕、西餐、咖啡、甜品冰淇淋、茶饮、其他',
	sku_cnt BIGINT COMMENT '商品销售数量，统计周期内该SKU的销售总数量',
	real_gmv_amt DECIMAL(38,18) COMMENT '订单实付金额，用户实际支付的金额',
	origin_gmv_amt DECIMAL(38,18) COMMENT '订单应付金额，订单原始金额',
	order_cnt BIGINT COMMENT '订单数量，统计周期内包含该SKU的订单总数',
	cust_cnt BIGINT COMMENT '订单客户数，统计周期内购买该SKU的独立客户数',
	deliver_gmv_amt DECIMAL(38,18) COMMENT '履约实付金额，实际履约的订单金额',
	deliver_cost DECIMAL(38,18) COMMENT '履约成本，订单履约过程中产生的成本'
) 
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='SKU粒度平台销售业绩报表月汇总表，按月份统计各SKU的销售业绩数据，包含销售数量、金额、订单数等关键指标') 
LIFECYCLE 30;