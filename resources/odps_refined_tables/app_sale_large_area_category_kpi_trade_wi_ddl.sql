CREATE TABLE IF NOT EXISTS app_sale_large_area_category_kpi_trade_wi(
	`year` STRING COMMENT '年份，格式：YYYY',
	`week_of_year` BIGINT COMMENT '周数，取值范围：1-53',
	`monday` STRING COMMENT '周一日期，格式：YYYYMMDD（年月日）',
	`sunday` STRING COMMENT '周日日期，格式：YYYYMMDD（年月日）',
	`large_area_name` STRING COMMENT '运营服务大区名称',
	`category` STRING COMMENT '品类，取值范围：鲜果、乳制品、其他',
	`order_origin_total_amt` DECIMAL(38,18) COMMENT '交易应付总金额（元）',
	`order_real_total_amt` DECIMAL(38,18) COMMENT '交易实付总金额（元）',
	`order_cust_cnt` BIGINT COMMENT '交易客户数，取值范围：0-2596',
	`order_cust_arpu` DECIMAL(38,18) COMMENT 'ARPU值（应付总金额/客户数，元/客户）',
	`order_cnt` BIGINT COMMENT '交易订单数，取值范围：1-3772',
	`delivery_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额（元）',
	`delivery_real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额（元）',
	`delivery_cust_cnt` BIGINT COMMENT '履约活跃客户数，取值范围：0-2632',
	`delivery_origin_profit` DECIMAL(38,18) COMMENT '履约应付毛利润（元）',
	`delivery_real_profit` DECIMAL(38,18) COMMENT '履约实付毛利润（元）',
	`delivery_after_profit` DECIMAL(38,18) COMMENT '履约后毛利润（元）',
	`delivery_days_avg` DECIMAL(38,18) COMMENT '履约频次（平均履约天数）',
	`delivery_point_cnt` BIGINT COMMENT '履约点位数，取值范围：0-3448',
	`delivery_amt` DECIMAL(38,18) COMMENT '自营履约费用（元）',
	`new_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '新客履约应付总金额（元）',
	`new_delivery_real_total_amt` DECIMAL(38,18) COMMENT '新客履约实付总金额（元）',
	`new_delivery_cust_cnt` BIGINT COMMENT '新客履约活跃客户数，取值范围：0-120',
	`new_delivery_real_profit` DECIMAL(38,18) COMMENT '新客履约实付毛利润（元）',
	`old_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '老客履约应付总金额（元）',
	`old_delivery_real_total_amt` DECIMAL(38,18) COMMENT '老客履约实付总金额（元）',
	`old_delivery_cust_cnt` BIGINT COMMENT '老客履约活跃客户数，取值范围：0-2526',
	`old_delivery_real_profit` DECIMAL(38,18) COMMENT '老客履约实付毛利润（元）',
	`order_sku_cnt` BIGINT COMMENT '交易SKU款数，取值范围：1-356',
	`order_sku_weight` DECIMAL(38,18) COMMENT '交易SKU重量（KG）',
	`delivery_sku_cnt` BIGINT COMMENT '履约SKU款数，取值范围：0-359',
	`delivery_sku_weight` DECIMAL(38,18) COMMENT '履约SKU重量（KG）'
) 
COMMENT '销售KPI指标汇总表，按大区和品类维度统计交易和履约相关指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：YYYYMMDD（年月日）') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='销售大区品类KPI交易指标汇总表，包含交易金额、客户数、订单数、履约指标、SKU指标等核心业务指标') 
LIFECYCLE 30;