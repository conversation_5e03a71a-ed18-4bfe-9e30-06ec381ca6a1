CREATE TABLE IF NOT EXISTS app_sku_delivery_mi(
	`month` STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
	`sku_id` STRING COMMENT 'SKU ID，商品最小库存单位标识',
	`spu_name` STRING COMMENT '商品名称，标准产品单元名称',
	`sku_disc` STRING COMMENT '商品描述，包含规格包装信息',
	`category1` STRING COMMENT '一级类目，枚举值：乳制品、其他',
	`cust_cnt` BIGINT COMMENT '人群数量，统计范围内的客户总数，取值范围：2-7922',
	`fruit_dlv_origin_total_amt` DECIMAL(38,18) COMMENT '鲜果履约应付金额，原始应付金额总计',
	`fruit_dlv_cust_cnt` BIGINT COMMENT '鲜果履约客户数，购买鲜果类商品的客户数量，取值范围：0-4601',
	`dairy_dlv_origin_total_amt` DECIMAL(38,18) COMMENT '乳制品履约应付金额，原始应付金额总计',
	`dairy_dlv_cust_cnt` BIGINT COMMENT '乳制品履约客户数，购买乳制品类商品的客户数量，取值范围：1-7922',
	`other_dlv_origin_total_amt` DECIMAL(38,18) COMMENT '其他履约应付金额，原始应付金额总计',
	`other_dlv_cust_cnt` BIGINT COMMENT '其他履约客户数，购买其他类商品的客户数量，取值范围：2-4776'
) 
COMMENT '人群品类偏好分析表，统计不同品类商品的客户偏好和履约情况'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='人群品类偏好分析表，基于月份和SKU维度统计各品类商品的客户偏好、履约金额和客户数量') 
LIFECYCLE 30;