CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_area_nohonour_cost_wi` (
  `friday` STRING COMMENT '周五日期，格式为yyyyMMdd，表示每周的周五日期',
  `saturday` STRING COMMENT '周六日期，格式为yyyyMMdd，表示每周的周六日期',
  `service_area` STRING COMMENT '服务区域名称，枚举值包括：贵阳、华北、昆明、广西、华南等',
  `allocate_amt` DECIMAL(38,18) COMMENT '调拨费用，单位为元',
  `purchase_car_amt` DECIMAL(38,18) COMMENT '采购用车费用，单位为元',
  `big_cust_amt` DECIMAL(38,18) COMMENT '大客户费用，单位为元',
  `purchase_store_amt` DECIMAL(38,18) COMMENT '采购仓储费用，单位为元',
  `total_nohonour_amt` DECIMAL(38,18) COMMENT '非履约费用总额，单位为元',
  `deliver_total_amt` DECIMAL(38,18) COMMENT '配送费用总额，单位为元'
) 
COMMENT '区域维度非履约成本周表，按周统计各区域的非履约相关成本费用'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '区域维度非履约成本周表，包含各区域的调拨费用、采购用车费用、大客户费用、采购仓储费用、非履约费用总额和配送费用等成本数据',
  'lifecycle' = '30'
);