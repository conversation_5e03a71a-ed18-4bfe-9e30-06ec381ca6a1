```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_cust_sku_area_value_mi` (
  `month` STRING COMMENT '月份，格式为yyyyMM，如202509',
  `sku_id` STRING COMMENT 'SKU编号，商品唯一标识',
  `spu_name` STRING COMMENT '商品名称',
  `sku_disc` STRING COMMENT '商品描述，包含规格、等级等信息',
  `cust_id` BIGINT COMMENT '客户ID，唯一标识客户',
  `cust_name` STRING COMMENT '客户名称',
  `cust_type` STRING COMMENT '客户业态，枚举值：面包蛋糕、西餐等',
  `large_area` STRING COMMENT '运营大区，如杭州大区',
  `origin_total_amt` DECIMAL(38,18) COMMENT '交易应付GMV，原始订单金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '交易实付GMV，实际支付金额',
  `sku_cnt` BIGINT COMMENT '交易销量，商品销售数量',
  `order_cnt` BIGINT COMMENT '交易频次（天），订单数量',
  `nearly_order_time` DATETIME COMMENT '最近一次下单时间，格式为年月日时分秒',
  `dlv_total_origin_amt` DECIMAL(38,18) COMMENT '履约应付GMV，履约原始金额',
  `dlv_total_real_amt` DECIMAL(38,18) COMMENT '履约实付GMV，履约实际金额',
  `cost_amt` DECIMAL(38,18) COMMENT '成本金额',
  `dlv_sku_cnt` BIGINT COMMENT '履约销量，履约商品数量',
  `dlv_order_cnt` BIGINT COMMENT '履约频次（天），履约订单数量',
  `nearly_deliver_time` DATETIME COMMENT '最近一次履约时间，格式为年月日时分秒',
  `is_last_cust` STRING COMMENT '是否上月留存客户，枚举值：是、否',
  `cust_label` STRING COMMENT '新/老客标识，枚举值：新客、老客'
)
COMMENT '客户品类价值表，记录客户在不同品类商品上的交易和履约价值数据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，如20250917'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '客户品类价值分析表，包含交易和履约相关的GMV、销量、频次等指标',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```