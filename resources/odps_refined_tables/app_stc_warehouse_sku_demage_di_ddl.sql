CREATE TABLE IF NOT EXISTS app_stc_warehouse_sku_demage_di(
	`warehouse_no` BIGINT COMMENT '库存仓编号，数值型标识',
	`warehouse_name` STRING COMMENT '库存仓名称',
	`purchaser` STRING COMMENT '采购人姓名',
	`batch_no` STRING COMMENT '批次编号，用于标识商品批次',
	`sku_id` STRING COMMENT 'SKU编号，商品最小库存单位标识',
	`category` STRING COMMENT '商品分类：鲜果、标品、其他、乳制品等',
	`spu_name` STRING COMMENT '商品名称，标准产品单元名称',
	`sku_disc` STRING COMMENT '商品描述，包含包装规格等信息',
	`weight` STRING COMMENT '规格重量描述',
	`sku_type` STRING COMMENT 'SKU类型：自营-平台自营商品，代仓-第三方仓库商品',
	`sku_property` STRING COMMENT 'SKU属性：核心-重要商品，非核心-一般商品',
	`unit_cost` DECIMAL(38,18) COMMENT '单价，商品单位成本，精度保留18位小数',
	`damage_cnt` BIGINT COMMENT '货损数量，商品损坏数量统计',
	`damage_amt` DECIMAL(38,18) COMMENT '货损总金额，损坏商品总价值，精度保留18位小数',
	`sale_cnt` BIGINT COMMENT '销售出库数量，当日销售商品数量',
	`sale_amt` DECIMAL(38,18) COMMENT '销售出库金额，当日销售总金额，精度保留18位小数',
	`store_quantity` BIGINT COMMENT '当日库存数量，截止统计时点的库存数量'
) 
COMMENT '仓库+SKU货损数据表，记录各仓库商品损坏情况及销售库存数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='仓库SKU货损明细表，包含商品损坏、销售和库存等关键业务指标') 
LIFECYCLE 30;