CREATE TABLE IF NOT EXISTS app_after_sale_delivery_sku_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示年月日',
	`sku_id` STRING COMMENT '商品SKU，唯一标识商品的库存单位',
	`spu_id` BIGINT COMMENT '商品pd_id，标准产品单元ID，取值范围从14到18805',
	`spu_name` STRING COMMENT '商品名称，如海南小金桔、四川尤力克黄柠檬等',
	`sku_disc` STRING COMMENT '商品规格，描述商品的包装规格和等级信息',
	`sku_type` STRING COMMENT '商品类型；取值范围：自营、代仓、代售',
	`after_sale_amt` DECIMAL(38,18) COMMENT '已到货售后金额，表示已完成的售后交易金额',
	`delivery_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付金额，表示订单原始应付总金额',
	`delivery_real_total_amt` DECIMAL(38,18) COMMENT '履约实付金额，表示实际支付的履约金额',
	`coupon_amt` DECIMAL(38,18) COMMENT '优惠券金额，表示使用的优惠券抵扣金额'
) 
COMMENT 'SKU粒度配送售后GMV表，记录商品SKU级别的配送售后相关金额指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='SKU粒度配送售后GMV分析表，包含商品售后金额、履约金额和优惠券金额等关键指标') 
LIFECYCLE 30;