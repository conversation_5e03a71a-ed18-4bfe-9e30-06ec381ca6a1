CREATE TABLE IF NOT EXISTS app_kpi_trade_wi(
	year STRING COMMENT '年份，格式：yyyy',
	week_of_year BIGINT COMMENT '周数，取值范围：1-53',
	monday STRING COMMENT '周一日期，格式：yyyyMMdd，表示年月日',
	sunday STRING COMMENT '周日日期，格式：yyyyMMdd，表示年月日',
	origin_total_amt DECIMAL(38,18) COMMENT '应付总金额',
	real_total_amt DECIMAL(38,18) COMMENT '实付总金额',
	cust_cnt BIGINT COMMENT '客户数',
	cust_arpu DECIMAL(38,18) COMMENT 'ARPU(应付总金额/客户数)',
	order_cnt BIGINT COMMENT '订单数',
	order_avg DECIMAL(38,18) COMMENT '订单均价(应付总金额/订单数)',
	after_sale_noreceived_amt DECIMAL(38,18) COMMENT '未到货售后总金额',
	after_sale_rate DECIMAL(38,18) COMMENT '退货率(未到货售后总金额/应付总金额)',
	dire_origin_total_amt DECIMAL(38,18) COMMENT '直发采购应付总金额',
	delivery_amt DECIMAL(38,18) COMMENT '运费（运费+超时加单费）',
	timing_origin_total_amt DECIMAL(38,18) COMMENT '省心送应付总金额'
) 
COMMENT '交易口径KPI指标周汇总表，包含每周的交易相关关键绩效指标数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，yyyyMMdd格式，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='交易口径KPI指标周汇总表，用于存储每周的交易相关业务指标数据') 
LIFECYCLE 30;