CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_crm_merchant_future_day_gmv_di_extra_h` (
  `cust_id` BIGINT COMMENT '商户ID，唯一标识商户',
  `contact_id` BIGINT COMMENT '联系地址ID，唯一标识联系地址',
  `bd_id` BIGINT COMMENT '归属BD ID，公海商户为0',
  `delivery_time` DATETIME COMMENT '计划配送时间，格式为年月日时分秒',
  `area_no` BIGINT COMMENT '商户所在运营区域编号',
  `distribution_gmv` DECIMAL(38,18) COMMENT '配送GMV（总商品价值）',
  `fruit_gmv` DECIMAL(38,18) COMMENT '鲜果品类GMV',
  `dairy_gmv` DECIMAL(38,18) COMMENT '乳制品品类GMV',
  `non_dairy_gmv` DECIMAL(38,18) COMMENT '非乳制品品类GMV',
  `brand_gmv` DECIMAL(38,18) COMMENT '自营品牌GMV',
  `reward_gmv` DECIMAL(38,18) COMMENT '固定奖励SKU的GMV',
  `spu_num` BIGINT COMMENT 'SPU数量（去重统计）',
  `estimated_income` DECIMAL(38,18) COMMENT '预估收益',
  `min_income_after` DECIMAL(38,18) COMMENT '达标后预估最低收益',
  `max_income_after` DECIMAL(38,18) COMMENT '达标后预估最高收益',
  `delivery_up_to_standard` BIGINT COMMENT '配送是否达标：0-未达标，1-已达标',
  `province` STRING COMMENT '省份名称',
  `city` STRING COMMENT '城市名称',
  `area` STRING COMMENT '区域名称'
)
COMMENT '商户当日及未来配送GMV明细表，包含商户配送计划、GMV分品类统计、收益预估和地域信息'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment'='商户配送GMV分析表，用于商户配送业绩分析和收益预测',
  'columnar.nested.type'='true'
)
LIFECYCLE 30;