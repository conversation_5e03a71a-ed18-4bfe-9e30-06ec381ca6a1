CREATE TABLE IF NOT EXISTS app_crm_merchant_today_gmv_di(
	cust_id BIGINT COMMENT '商户ID，唯一标识商户',
	cust_name STRING COMMENT '商户名称',
	merchant_total_gmv DECIMAL(38,18) COMMENT '商户总GMV（剔除N001S01R005、N001S01R002后的总销售额）',
	fruit_gmv DECIMAL(38,18) COMMENT '鲜果品类GMV',
	dairy_gmv DECIMAL(38,18) COMMENT '乳制品品类GMV',
	non_dairy_gmv DECIMAL(38,18) COMMENT '非乳制品品类GMV',
	brand_gmv DECIMAL(38,18) COMMENT '自营品牌GMV',
	reward_gmv DECIMAL(38,18) COMMENT '奖励SKU的GMV',
	distribution_gmv DECIMAL(38,18) COMMENT '配送GMV（第二天计划配送的订单销售额）',
	spu_average DECIMAL(38,18) COMMENT '配送SPU均值'
) 
PARTITIONED BY (ds STRING COMMENT '分区字段，yyyyMMdd格式，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='商户当日GMV统计表，记录商户各品类的销售额数据，用于业务分析和报表展示') 
LIFECYCLE 30;