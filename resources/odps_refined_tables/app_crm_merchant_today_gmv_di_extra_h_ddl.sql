CREATE TABLE IF NOT EXISTS app_crm_merchant_today_gmv_di_extra_h(
	cust_id BIGINT COMMENT '商户ID，唯一标识商户',
	cust_name STRING COMMENT '商户名称',
	merchant_total_gmv DECIMAL(38,18) COMMENT '商户总GMV，已剔除N001S01R005、N001S01R002',
	fruit_gmv DECIMAL(38,18) COMMENT '鲜果品类GMV',
	dairy_gmv DECIMAL(38,18) COMMENT '乳制品品类GMV',
	non_dairy_gmv DECIMAL(38,18) COMMENT '非乳制品品类GMV',
	brand_gmv DECIMAL(38,18) COMMENT '自营品牌GMV',
	reward_gmv DECIMAL(38,18) COMMENT '奖励SKU的GMV',
	distribution_gmv DECIMAL(38,18) COMMENT '配送GMV，第二天计划配送的订单',
	spu_average DECIMAL(38,18) COMMENT '配送SPU均值'
) 
PARTITIONED BY (ds STRING COMMENT '分区字段，yyyyMMdd格式，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='商户当日GMV统计表，按小时级别更新，包含各品类GMV分布和配送相关指标') 
LIFECYCLE 30;