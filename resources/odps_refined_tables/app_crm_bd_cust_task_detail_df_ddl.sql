CREATE TABLE IF NOT EXISTS app_crm_bd_cust_task_detail_df(
	job_id BIGINT COMMENT '任务ID，唯一标识一个任务',
	cust_id BIGINT COMMENT '客户ID，标识客户身份',
	completion_status BIGINT COMMENT '完成状态：0-未完成，1-已完成',
	job_real_total_amt DECIMAL(38,18) COMMENT '任务期间累计下单实付金额，保留18位小数精度'
)
COMMENT '任务客户完成情况表，记录BD客户任务完成状态和下单金额数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='BD客户任务完成情况明细表，包含任务完成状态和实付金额信息') 
LIFECYCLE 30;