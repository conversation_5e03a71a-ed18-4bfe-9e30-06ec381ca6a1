CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_kpi_operate_large_category_delivery_di` (
  `date` STRING COMMENT '月份，格式为yyyyMMdd',
  `large_area_name` STRING COMMENT '运营服务大区名称',
  `category` STRING COMMENT '商品品类：鲜果，乳制品，其他',
  `origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '履约实付GMV金额',
  `marketing_amt` DECIMAL(38,18) COMMENT '营销费用金额',
  `cost_amt` DECIMAL(38,18) COMMENT '成本金额',
  `origin_gross` DECIMAL(38,18) COMMENT '应付毛利润金额',
  `real_gross` DECIMAL(38,18) COMMENT '实付毛利润金额',
  `origin_gross_margin` DECIMAL(38,18) COMMENT '应付毛利率',
  `real_gross_margin` DECIMAL(38,18) COMMENT '实付毛利率',
  `cust_cnt` BIGINT COMMENT '履约客户数量',
  `point_cnt` BIGINT COMMENT '履约点位数',
  `origin_pre_cust_price` DECIMAL(38,18) COMMENT '应付客单价',
  `real_pre_cust_price` DECIMAL(38,18) COMMENT '实付客单价',
  `timing_origin_amt` DECIMAL(38,18) COMMENT '省心送业务应付GMV金额',
  `timing_real_amt` DECIMAL(38,18) COMMENT '省心送业务实付GMV金额',
  `consign_origin_amt` DECIMAL(38,18) COMMENT '代售业务应付GMV金额',
  `consign_real_amt` DECIMAL(38,18) COMMENT '代售业务实付GMV金额',
  `consign_marketing_amt` DECIMAL(38,18) COMMENT '代售业务营销费用金额',
  `consign_origin_gross` DECIMAL(38,18) COMMENT '代售业务应付毛利润金额',
  `consign_real_gross` DECIMAL(38,18) COMMENT '代售业务实付毛利润金额',
  `consign_cust_cnt` BIGINT COMMENT '代售业务履约客户数量',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储费用金额',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '单点干线费用金额',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提费用金额',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨费用金额',
  `other_amt` DECIMAL(38,18) COMMENT '其他费用金额',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送费用金额'
)
COMMENT '运营履约KPI指标表（平台客户），包含各品类在各运营大区的履约相关财务和运营指标数据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '运营履约KPI指标表（平台客户），包含各品类在各运营大区的履约相关财务和运营指标数据',
  'lifecycle' = '30'
)