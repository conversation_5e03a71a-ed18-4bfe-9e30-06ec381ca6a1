CREATE TABLE IF NOT EXISTS app_xianmu_sale_purchase_back_df(
    return_no STRING COMMENT '退货编号，唯一标识退货单',
    launch_time DATETIME COMMENT '发起时间，格式为年月日时分秒（yyyy-MM-dd HH:mm:ss）',
    audit_status STRING COMMENT '审核状态，取值范围：审核成功、审核中、审核失败等',
    return_type STRING COMMENT '退货类型，取值范围：已入库退货、未入库退货等',
    purchase_no STRING COMMENT '采购单号，关联采购单表的单号',
    order_no STRING COMMENT '销售单号，关联销售单表的单号',
    warehouse_name STRING COMMENT '退货仓库名称',
    stock_task_storage_id BIGINT COMMENT '退货任务号，数字标识',
    stock_task_storage_type STRING COMMENT '退货任务类型，取值范围：13-拒收入库，19-退货入库，22-拦截入库，23-多出入库，20-缺货入库',
    company_entity STRING COMMENT '公司主体名称'
)
COMMENT '采购退货单表，记录采购退货的相关信息，包括退货编号、发起时间、审核状态、退货类型、关联单号、仓库信息等'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd')
STORED AS ALIORC
TBLPROPERTIES ('comment'='采购退货单表，用于存储和管理采购退货业务数据')
LIFECYCLE 30;