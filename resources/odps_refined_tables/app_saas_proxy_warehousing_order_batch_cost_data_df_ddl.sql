CREATE TABLE IF NOT EXISTS app_saas_proxy_warehousing_order_batch_cost_data_df(
	tenant_id BIGINT COMMENT '租户ID，取值范围：1-95',
	create_time STRING COMMENT '出库任务创建时间，格式：yyyy-MM-dd HH:mm:ss',
	out_order_no STRING COMMENT '订单编号',
	warehouse_no STRING COMMENT '出库仓库编号',
	sku STRING COMMENT 'SKU编码',
	batch STRING COMMENT '出库批次，格式：年月日时分秒毫秒组合',
	quantity_sum BIGINT COMMENT '总出库数量，取值范围：1-200，平均值：1.70，标准差：2.82',
	unit_cost DECIMAL(38,18) COMMENT 'SKU该批次成本单价，单位：元',
	total_cost DECIMAL(38,18) COMMENT '总成本金额，单位：元'
)
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd')
STORED AS ALIORC
TBLPROPERTIES ('comment'='SaaS代理仓储订单批次成本数据表，记录各租户的出库订单成本明细')
LIFECYCLE 365;