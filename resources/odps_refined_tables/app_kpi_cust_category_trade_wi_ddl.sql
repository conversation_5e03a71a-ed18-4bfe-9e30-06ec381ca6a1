CREATE TABLE IF NOT EXISTS app_kpi_cust_category_trade_wi(
	year STRING COMMENT '年份，格式：yyyy',
	week_of_year BIGINT COMMENT '周数，一年中的第几周',
	monday STRING COMMENT '周一日期，格式：yyyyMMdd，表示年月日',
	sunday STRING COMMENT '周日日期，格式：yyyyMMdd，表示年月日',
	cust_team STRING COMMENT '客户团队类型，取值范围：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户',
	category STRING COMMENT '品类，取值范围：鲜果、乳制品、其他',
	origin_total_amt DECIMAL(38,18) COMMENT '应付总金额，交易应付总金额',
	real_total_amt DECIMAL(38,18) COMMENT '实付总金额，交易实付总金额',
	cust_cnt BIGINT COMMENT '客户数，统计周期内的客户数量',
	cust_arpu DECIMAL(38,18) COMMENT 'ARPU值，计算公式：应付总金额/客户数',
	order_cnt BIGINT COMMENT '订单数，统计周期内的订单数量',
	order_avg DECIMAL(38,18) COMMENT '订单均价，计算公式：应付总金额/订单数',
	after_sale_noreceived_amt DECIMAL(38,18) COMMENT '未到货售后总金额，未到货的售后金额总和',
	after_sale_rate DECIMAL(38,18) COMMENT '退货率，计算公式：未到货售后总金额/应付总金额',
	dire_origin_total_amt DECIMAL(38,18) COMMENT '直发采购应付总金额，直发采购模式的应付金额',
	delivery_amt DECIMAL(38,18) COMMENT '运费，包含运费和超时加单费的总和',
	timing_origin_total_amt DECIMAL(38,18) COMMENT '省心送应付总金额，省心送模式的应付金额'
) 
COMMENT '交易口径KPI指标周汇总表，按周统计的交易相关KPI指标数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='交易口径KPI指标周汇总表，包含客户团队、品类维度的交易指标数据') 
LIFECYCLE 30;