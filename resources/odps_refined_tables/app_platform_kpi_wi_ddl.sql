```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_platform_kpi_wi` (
  `year` STRING COMMENT '年份，格式：YYYY',
  `week_of_year` BIGINT COMMENT '周数，取值范围：1-53',
  `monday` STRING COMMENT '周一日期，格式：YYYYMMDD',
  `sunday` STRING COMMENT '周日日期，格式：YYYYMMDD',
  `origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额，单位：元',
  `real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额，单位：元',
  `cust_cnt` BIGINT COMMENT '履约客户数',
  `preferential_amt` DECIMAL(38,18) COMMENT '营销金额，单位：元',
  `timing_origin_amt` DECIMAL(38,18) COMMENT '省心送履约应付GMV，单位：元',
  `timing_cust_cnt` BIGINT COMMENT '省心送履约客户数',
  `timing_ratio` DECIMAL(38,18) COMMENT '省心送合单率，取值范围：0-1',
  `login_uv` BIGINT COMMENT '登陆UV（独立访客数）',
  `order_uv` BIGINT COMMENT '交易客户数（下单独立访客数）',
  `activity_uv` BIGINT COMMENT '特价点击UV（特价活动独立访客数）',
  `activity_order_uv` BIGINT COMMENT '特价下单人数（特价活动下单独立访客数）',
  `exchange_uv` BIGINT COMMENT '换购点击UV（换购活动独立访客数）',
  `exchange_order_uv` BIGINT COMMENT '换购下单人数（换购活动下单独立访客数）',
  `expand_uv` BIGINT COMMENT '拓展购买点击UV（拓展购买活动独立访客数）',
  `expand_order_uv` BIGINT COMMENT '拓展购买下单人数（拓展购买活动下单独立访客数）',
  `meeting_uv` BIGINT COMMENT '会场活动页点击UV（会场活动独立访客数）',
  `meeting_order_uv` BIGINT COMMENT '会场活动页下单人数（会场活动下单独立访客数）',
  `other_uv` BIGINT COMMENT '其他点击UV（其他活动独立访客数）',
  `other_order_uv` BIGINT COMMENT '其他下单人数（其他活动下单独立访客数）'
) 
COMMENT '平台KPI周度统计表，包含平台各项核心指标数据，如GMV、客户数、营销金额、各类活动UV和下单人数等'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：YYYYMMDD，表示数据日期'
)
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
               'comment'='平台KPI周度统计表，用于监控和分析平台业务表现') 
LIFECYCLE 30;
```