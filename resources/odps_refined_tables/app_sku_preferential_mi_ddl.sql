```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_sku_preferential_mi` (
  `month` STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
  `sku_id` STRING COMMENT 'SKU编码，商品最小库存单位的唯一标识',
  `spu_name` STRING COMMENT '商品名称，标准产品单元的名称',
  `sku_disc` STRING COMMENT '商品描述，包含规格包装信息，如"1L*12盒"',
  `category1` STRING COMMENT '一级类目，商品分类：乳制品、其他',
  `preferential_type` STRING COMMENT '活动类型，取值范围：特价活动、满减活动、优惠券、限时折扣、买赠活动、组合优惠、新人专享、会员专享、积分兑换、秒杀活动、拼团活动、预售活动、老客回馈、节日促销、清仓处理、新品推广、品牌活动、其他',
  `coupon_amt_label` DECIMAL(38,18) COMMENT '营销费用，单位为元，表示该SKU在活动中的优惠金额'
) 
COMMENT '人群营销结构表，记录商品在不同营销活动中的优惠信息和分类数据'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC  
TBLPROPERTIES ('comment'='人群营销结构分析表，用于分析商品营销活动和优惠策略效果') 
LIFECYCLE 30;
```