CREATE TABLE IF NOT EXISTS app_area_nohonour_cost_mi(
	month STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
	service_area STRING COMMENT '服务区域，枚举值包括：华西、华中、华东、广西、昆明等',
	allocate_amt DECIMAL(38,18) COMMENT '调拨费用，单位为元',
	purchase_car_amt DECIMAL(38,18) COMMENT '采购用车费用，单位为元',
	big_cust_amt DECIMAL(38,18) COMMENT '大客户费用，单位为元',
	purchase_store_amt DECIMAL(38,18) COMMENT '采购仓储费用，单位为元',
	total_nohonour_amt DECIMAL(38,18) COMMENT '非履约费用总额，单位为元',
	deliver_total_amt DECIMAL(38,18) COMMENT '配送费用总额，单位为元'
) 
COMMENT '区域维度非履约成本月表，按区域统计每月的非履约相关成本费用'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMM，表示年月分区') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='区域维度非履约成本月表，包含各区域的调拨费用、采购用车费用、大客户费用、采购仓储费用、非履约费用和配送费用等成本数据') 
LIFECYCLE 30;