CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_kpi_trade_mi` (
  `month` STRING COMMENT '月份，格式为yyyyMM',
  `origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额（元）',
  `real_total_amt` DECIMAL(38,18) COMMENT '实付总金额（元）',
  `cust_cnt` BIGINT COMMENT '客户数',
  `cust_arpu` DECIMAL(38,18) COMMENT 'ARPU值（应付总金额/客户数，单位：元/客户）',
  `order_cnt` BIGINT COMMENT '订单数',
  `order_avg` DECIMAL(38,18) COMMENT '订单均价（应付总金额/订单数，单位：元/订单）',
  `after_sale_noreceived_amt` DECIMAL(38,18) COMMENT '未到货售后总金额（元）',
  `after_sale_rate` DECIMAL(38,18) COMMENT '退货率（未到货售后总金额/应付总金额，取值范围：0-1）',
  `target_origin_total_amt` DECIMAL(38,18) COMMENT '目标应付总金额（元）',
  `target_cust_cnt` BIGINT COMMENT '目标客户数',
  `target_cust_arpu` DECIMAL(38,18) COMMENT '目标ARPU值（元/客户）',
  `target_order_cnt` BIGINT COMMENT '目标订单数',
  `target_after_sale_rate` DECIMAL(38,18) COMMENT '目标退货率（取值范围：0-1）',
  `dire_origin_total_amt` DECIMAL(38,18) COMMENT '直发采购应付总金额（元）',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费（运费+超时加单费，单位：元）',
  `timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送应付总金额（元）'
) 
COMMENT '交易口径KPI指标月汇总表，包含月度交易相关的关键绩效指标数据'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '交易口径KPI指标月汇总表，用于存储月度交易相关的关键绩效指标数据',
  'lifecycle' = '365'
);