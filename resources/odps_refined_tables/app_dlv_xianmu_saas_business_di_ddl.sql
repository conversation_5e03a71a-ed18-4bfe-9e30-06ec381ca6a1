```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_dlv_xianmu_saas_business_di`(
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务数据发生的日期',
  `business_type` STRING COMMENT '业务类型：批发,自营,代仓,代售，SAAS鲜沐自营，SAAS鲜沐代仓，SAAS品牌方自营',
  `origin_total_amt` DECIMAL(38,18) COMMENT '原始总金额，未经过任何调整的业务总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '实际总金额，经过调整后的实际业务总金额',
  `cost_amt` DECIMAL(38,18) COMMENT '成本金额，业务发生的总成本',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费金额，配送相关的费用',
  `point_cnt` BIGINT COMMENT '点位数，业务覆盖的网点或门店数量',
  `sku_cnt` BIGINT COMMENT 'SKU数量，涉及的商品品类数量',
  `total_sku_weight` DECIMAL(38,18) COMMENT '履约总重量（KG），所有商品的总重量',
  `after_sale_received_order_cnt` BIGINT COMMENT '已到货售后总订单数，已完成收货的售后订单数量',
  `after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后总金额，已完成收货的售后订单金额',
  `inventory_loss_amt` DECIMAL(38,18) COMMENT '盘亏总金额，库存盘点亏损金额',
  `inventory_profit_amt` DECIMAL(38,18) COMMENT '盘盈总金额，库存盘点盈余金额',
  `damage_amt` DECIMAL(38,18) COMMENT '货损总金额，货物损坏造成的损失金额',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储成本，仓库存储相关费用',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本，主要运输线路费用',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送成本，末端配送费用',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提成本，自提相关的费用',
  `other_amt` DECIMAL(38,18) COMMENT '其他成本，未分类的其他费用',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨成本，库存调拨相关费用',
  `heytea_storage_amt` DECIMAL(38,18) COMMENT '喜茶仓储成本，喜茶业务专属仓储费用',
  `heytea_arterial_roads_amt` DECIMAL(38,18) COMMENT '喜茶干线成本，喜茶业务专属干线运输费用',
  `heytea_deliver_amt` DECIMAL(38,18) COMMENT '喜茶配送成本，喜茶业务专属配送费用',
  `heytea_way_deliver_amt` DECIMAL(38,18) COMMENT '喜茶专配成本，喜茶业务专属配送服务费用'
) 
COMMENT '各业务线数据大盘，包含鲜沐SAAS业务各类型（批发、自营、代仓、代售等）的财务和运营核心指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据加载日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='鲜沐SAAS业务数据汇总表，用于各业务线数据分析和监控') 
LIFECYCLE 30;
```