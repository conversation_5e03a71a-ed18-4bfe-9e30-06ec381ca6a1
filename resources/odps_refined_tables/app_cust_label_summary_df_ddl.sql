```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_cust_label_summary_df` (
  `date` STRING COMMENT '日期字段，格式为yyyyMMdd，表示数据统计的日期',
  `cust_team` STRING COMMENT '客户团队类型，枚举值：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户',
  `cust_cnt` BIGINT COMMENT '总门店数',
  `order_origin_amt_365d` DECIMAL(38,18) COMMENT '应付GMV（近365天），单位：元',
  `audit_cust_cnt` BIGINT COMMENT '审核中门店数',
  `audit_order_origin_amt_365d` DECIMAL(38,18) COMMENT '审核中门店应付GMV（近365天），单位：元',
  `audit_failed_cust_cnt` BIGINT COMMENT '审核未通过门店数',
  `audit_failed_order_origin_amt_365d` DECIMAL(38,18) COMMENT '审核未通过门店应付GMV（近365天），单位：元',
  `blacklist_cust_cnt` BIGINT COMMENT '拉黑门店数',
  `blacklist_order_origin_amt_365d` DECIMAL(38,18) COMMENT '拉黑门店应付GMV（近365天），单位：元',
  `close_cust_cnt` BIGINT COMMENT '审核通过但倒闭门店数',
  `close_order_origin_amt_365d` DECIMAL(38,18) COMMENT '审核通过但倒闭门店应付GMV（近365天），单位：元',
  `normal_cust_cnt` BIGINT COMMENT '审核通过非闭店门店数',
  `normal_order_origin_amt_365d` DECIMAL(38,18) COMMENT '审核通过非闭店门店应付GMV（近365天），单位：元',
  `normal_order_real_amt_365d` DECIMAL(38,18) COMMENT '审核通过非闭店门店实付GMV（近365天），单位：元',
  `normal_delivery_real_amt_365d` DECIMAL(38,18) COMMENT '审核通过非闭店门店履约实付GMV（近365天），单位：元',
  `normal_delivery_profit_365d` DECIMAL(38,18) COMMENT '审核通过非闭店门店履约实付毛利润（近365天），单位：元',
  `normal_disbaled_private_cust_cnt` BIGINT COMMENT '审核通过非闭店离职销售锁定门店数',
  `normal_private_cust_cnt` BIGINT COMMENT '审核通过非闭店私海门店数',
  `normal_bd_cnt` BIGINT COMMENT '审核通过非闭店分配BD数',
  `bd_cnt` BIGINT COMMENT '总BD数',
  `login_cust_cnt_30d` BIGINT COMMENT '近30日登录客户数',
  `sku_cl_cust_cnt_30d` BIGINT COMMENT '近30日点击客户数',
  `order_cust_cnt_30d` BIGINT COMMENT '近30日下单客户数',
  `login_cust_cnt_1d` BIGINT COMMENT '当日登录客户数',
  `sku_cl_cust_cnt_1d` BIGINT COMMENT '当日点击客户数',
  `order_cust_cnt_1d` BIGINT COMMENT '当日下单客户数',
  `follow_cust_cnt_1d` BIGINT COMMENT '当日拜访客户数',
  `blacklist_back_cust_cnt` BIGINT COMMENT '拉黑回流门店数',
  `new_cust_cnt` BIGINT COMMENT '新客户数',
  `new_order_cust_cnt_30d` BIGINT COMMENT '新注册客户在近30日下单客户数',
  `register_cust_cnt_1d` BIGINT COMMENT '当日新注册客户数',
  `register_order_cust_cnt_1d` BIGINT COMMENT '当日新注册下单客户数',
  `register_cust_cnt_30d` BIGINT COMMENT '近30日新注册客户数',
  `register_order_cust_cnt_30d` BIGINT COMMENT '近30日新注册在近30日下单客户数',
  `new_order2_cust_cnt_30d` BIGINT COMMENT '近30日首单且订单>=2客户数',
  `new_order_real_amt_avg_30d` DECIMAL(38,18) COMMENT '近30日首单客户的平均实付金额，单位：元',
  `old_cust_cnt` BIGINT COMMENT '老客客户数',
  `old_order_cust_cnt_30d` BIGINT COMMENT '近30日老客下单数',
  `old_order_real_amt_avg_30d` DECIMAL(38,18) COMMENT '近30日老客的平均实付金额，单位：元'
) 
COMMENT '客户标签汇总表，按客户团队类型统计各类门店数量、GMV指标、客户行为指标等核心业务数据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据加载日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '客户标签汇总表，用于客户分层分析和业务监控',
  'lifecycle' = '30'
)
```