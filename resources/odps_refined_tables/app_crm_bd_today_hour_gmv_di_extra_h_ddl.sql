```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_crm_bd_today_hour_gmv_di_extra_h` (
  `date_tag` STRING COMMENT '时间标签，格式为yyyyMMdd，表示数据所属日期',
  `bd_id` BIGINT COMMENT 'BD ID，销售人员的唯一标识',
  `bd_name` STRING COMMENT 'BD名称，销售人员的姓名',
  `is_same_city` STRING COMMENT '是否同城市：是/否（BD归属的M1和客户注册行政市区所属的M1是否一致）',
  `origin_total_gmv` DECIMAL(38,18) COMMENT '交易应付GMV，订单原始金额',
  `real_total_gmv` DECIMAL(38,18) COMMENT '交易实付GMV，实际支付金额',
  `cust_cnt` BIGINT COMMENT '交易客户数，发生交易的客户数量',
  `categories_origin_total_gmv` DECIMAL(38,18) COMMENT '全品类交易应付GMV，所有品类订单原始金额',
  `categories_real_total_gmv` DECIMAL(38,18) COMMENT '全品类交易实付GMV，所有品类实际支付金额',
  `categories_cust_cnt` BIGINT COMMENT '全品类交易客户数，购买所有品类的客户数量',
  `fruit_origin_total_gmv` DECIMAL(38,18) COMMENT '鲜果交易应付GMV，鲜果品类订单原始金额',
  `fruit_real_total_gmv` DECIMAL(38,18) COMMENT '鲜果交易实付GMV，鲜果品类实际支付金额',
  `fruit_cust_cnt` BIGINT COMMENT '鲜果交易客户数，购买鲜果的客户数量',
  `ancho_origin_total_gmv` DECIMAL(38,18) COMMENT '安佳铁塔交易应付GMV，安佳铁塔品类订单原始金额',
  `ancho_real_total_gmv` DECIMAL(38,18) COMMENT '安佳铁塔交易实付GMV，安佳铁塔品类实际支付金额',
  `ancho_cust_cnt` BIGINT COMMENT '安佳铁塔交易客户数，购买安佳铁塔的客户数量',
  `dairy_origin_total_gmv` DECIMAL(38,18) COMMENT '乳制品（不含AT）交易应付GMV，乳制品（不含安佳铁塔）订单原始金额',
  `dairy_real_total_gmv` DECIMAL(38,18) COMMENT '乳制品（不含AT）交易实付GMV，乳制品（不含安佳铁塔）实际支付金额',
  `dairy_cust_cnt` BIGINT COMMENT '乳制品（不含AT）交易客户数，购买乳制品（不含安佳铁塔）的客户数量',
  `other_origin_total_gmv` DECIMAL(38,18) COMMENT '其他交易应付GMV，其他品类订单原始金额',
  `other_real_total_gmv` DECIMAL(38,18) COMMENT '其他交易实付GMV，其他品类实际支付金额',
  `other_cust_cnt` BIGINT COMMENT '其他交易客户数，购买其他品类的客户数量',
  `no_anchor_origin_total_gmv` DECIMAL(38,18) COMMENT '非AT交易应付GMV，非安佳铁塔品类订单原始金额',
  `no_anchor_real_total_gmv` DECIMAL(38,18) COMMENT '非AT交易实付GMV，非安佳铁塔品类实际支付金额',
  `no_anchor_cust_cnt` BIGINT COMMENT '非AT交易客户数，购买非安佳铁塔品类的客户数量',
  `ordinary_num` BIGINT COMMENT '拜访数，BD的拜访客户次数',
  `dlv_real_total_gmv` DECIMAL(38,18) COMMENT '履约实付GMV，已履约订单的实际支付金额',
  `dlv_timing_real_total_gmv` DECIMAL(38,18) COMMENT '省心送明日履约实付GMV，省心送明日达履约订单的实际支付金额',
  `dlv_cust_cnt` BIGINT COMMENT '履约客户数，已履约订单的客户数量',
  `dlv_categories_real_total_gmv` DECIMAL(38,18) COMMENT '全品类履约实付GMV，所有品类已履约订单的实际支付金额',
  `dlv_categories_cust_cnt` BIGINT COMMENT '全品类履约客户数，购买所有品类且已履约的客户数量',
  `dlv_fruit_real_total_gmv` DECIMAL(38,18) COMMENT '鲜果履约实付GMV，鲜果品类已履约订单的实际支付金额',
  `dlv_fruit_cust_cnt` BIGINT COMMENT '鲜果履约客户数，购买鲜果且已履约的客户数量',
  `dlv_ancho_real_total_gmv` DECIMAL(38,18) COMMENT '安佳铁塔履约实付GMV，安佳铁塔品类已履约订单的实际支付金额',
  `dlv_timing_ancho_real_total_gmv` DECIMAL(38,18) COMMENT '安佳铁塔省心送履约实付GMV，安佳铁塔省心送明日达履约订单的实际支付金额',
  `dlv_ancho_cust_cnt` BIGINT COMMENT '安佳铁塔履约客户数，购买安佳铁塔且已履约的客户数量',
  `dlv_dairy_real_total_gmv` DECIMAL(38,18) COMMENT '乳制品（不含AT）履约实付GMV，乳制品（不含安佳铁塔）已履约订单的实际支付金额',
  `dlv_timing_dairy_real_total_gmv` DECIMAL(38,18) COMMENT '乳制品（不含AT）省心送履约实付GMV，乳制品（不含安佳铁塔）省心送明日达履约订单的实际支付金额',
  `dlv_dairy_cust_cnt` BIGINT COMMENT '乳制品（不含AT）履约客户数，购买乳制品（不含安佳铁塔）且已履约的客户数量',
  `dlv_other_real_total_gmv` DECIMAL(38,18) COMMENT '其他履约实付GMV，其他品类已履约订单的实际支付金额',
  `dlv_timing_other_real_total_gmv` DECIMAL(38,18) COMMENT '其他省心送履约实付GMV，其他品类省心送明日达履约订单的实际支付金额',
  `dlv_other_cust_cnt` BIGINT COMMENT '其他履约客户数，购买其他品类且已履约的客户数量',
  `dlv_no_anchor_real_total_gmv` DECIMAL(38,18) COMMENT '非AT履约实付GMV，非安佳铁塔品类已履约订单的实际支付金额',
  `dlv_no_anchor_cust_cnt` BIGINT COMMENT '非AT履约客户数，购买非安佳铁塔品类且已履约的客户数量',
  `dlv_timing_no_anchor_real_total_gmv` DECIMAL(38,18) COMMENT '非AT省心送实付GMV，非安佳铁塔省心送明日达履约订单的实际支付金额',
  `no_anchor_categories_kpi_gmv` DECIMAL(38,18) COMMENT '非AT履约实付GMV+全品类交易实付GMV，KPI考核指标'
)
COMMENT 'BD销售数据小时粒度统计表，包含BD的交易GMV、履约GMV、客户数等核心指标，按小时粒度聚合'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据所属日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = 'BD销售数据小时粒度统计表，用于监控和分析BD的销售业绩和履约情况',
  'lifecycle' = '30'
);
```