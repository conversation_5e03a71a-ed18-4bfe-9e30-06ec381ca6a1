```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_saas_product_sales_overview_di` (
  `tenant_id` BIGINT COMMENT '租户ID，取值范围：2-32',
  `type` BIGINT COMMENT '时间标签类型：1-日，2-周，3-月',
  `time_tag` STRING COMMENT '时间标签，格式：yyyyMMdd（年月日）',
  `category_id` BIGINT COMMENT '三级类目ID，取值范围：526-1129',
  `store_type` BIGINT COMMENT '门店类型：0-直营店，1-加盟店，2-托管店',
  `store_id` BIGINT COMMENT '门店ID，取值范围：2-542067',
  `store_name` STRING COMMENT '门店名称',
  `province` STRING COMMENT '省份名称',
  `city` STRING COMMENT '城市名称',
  `address` STRING COMMENT '详细地址（省份+城市）',
  `pay_success_num` BIGINT COMMENT '支付成功商品件数，取值范围：0-3000',
  `pay_success_price` DECIMAL(38,18) COMMENT '支付成功金额',
  `refund_num` BIGINT COMMENT '退款件数，取值范围：0-17216',
  `refund_price` DECIMAL(38,18) COMMENT '退款金额',
  `last_pay_success_num` BIGINT COMMENT '上周期支付成功商品件数，取值范围：0-2000',
  `last_pay_success_price` DECIMAL(38,18) COMMENT '上周期支付成功金额',
  `last_refund_num` BIGINT COMMENT '上周期退款件数，取值范围：0-21697',
  `last_refund_price` DECIMAL(38,18) COMMENT '上周期退款金额',
  `warehouse_type` BIGINT COMMENT '归属类型：0-自营品，1-三方品',
  `delivery_type` BIGINT COMMENT '配送方式：0-品牌方配送，1-三方配送',
  `item_id` BIGINT COMMENT '商品编码，取值范围：1-44829',
  `title` STRING COMMENT '商品名称',
  `goods_type` BIGINT COMMENT '商品类型：0-无货商品，1-报价货品，2-自营货品'
) 
COMMENT 'SaaS商品销售概况表，包含商品销售数据、退款数据、门店信息等维度的统计分析'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：yyyyMMdd（年月日）'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = 'SaaS商品销售概况表，用于分析商品销售趋势和门店经营情况',
  'lifecycle' = '30'
);
```