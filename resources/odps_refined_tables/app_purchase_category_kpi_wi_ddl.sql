```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_purchase_category_kpi_wi` (
  `year` STRING COMMENT '年份，格式：YYYY',
  `week_of_year` BIGINT COMMENT '周数，取值范围：1-53',
  `monday` STRING COMMENT '周一日期，格式：YYYYMMDD',
  `sunday` STRING COMMENT '周日日期，格式：YYYYMMDD',
  `category` STRING COMMENT '商品品类，枚举值：鲜果、乳制品、其他',
  `origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额（元）',
  `real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额（元）',
  `cost_amt` DECIMAL(38,18) COMMENT '商品成本（元）',
  `store_cost_amt` DECIMAL(38,18) COMMENT '期末库存成本（元）',
  `sale_amt` DECIMAL(38,18) COMMENT '销售出库成本（元）',
  `on_sale_sku_cnt` BIGINT COMMENT '有销售及自提出库的SKU数量',
  `init_sku_cnt` BIGINT COMMENT '有期初库存的SKU数量',
  `sale_out_time` DECIMAL(38,18) COMMENT '售罄时长（小时）',
  `on_sale_time` DECIMAL(38,18) COMMENT '上架时长（小时）',
  `check_sku_cnt` BIGINT COMMENT '抽检SKU数量',
  `qualified_cnt` BIGINT COMMENT '合格SKU数量',
  `after_sale_amt` DECIMAL(38,18) COMMENT '采购责售后金额（元）'
) 
COMMENT '采购KPI统计表，按周维度统计各品类的采购关键绩效指标，包括金额、成本、库存、SKU数量、时长等维度数据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：YYYYMMDD，表示数据日期'
)
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
               'comment'='采购KPI周度统计表，用于采购业务分析和绩效评估') 
LIFECYCLE 30;
```