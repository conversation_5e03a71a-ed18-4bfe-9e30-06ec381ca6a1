CREATE TABLE IF NOT EXISTS app_dlv_wholesale_delivery_df(
    `finish_date` STRING COMMENT '履约完成日期，格式：yyyyMMdd',
    `order_date` STRING COMMENT '客户下单日期，格式：yyyyMMdd',
    `order_no` STRING COMMENT '订单编号，唯一标识一笔订单',
    `cust_id` BIGINT COMMENT '客户唯一标识ID',
    `cust_name` STRING COMMENT '客户公司/个人名称',
    `cust_group` STRING COMMENT '客户分组类型：大客户、平台客户、批发',
    `point_id` BIGINT COMMENT '配送点位ID，唯一标识一个配送地址',
    `delivery_province` STRING COMMENT '配送地址-省份',
    `delivery_city` STRING COMMENT '配送地址-城市',
    `delivery_area` STRING COMMENT '配送地址-区县',
    `delivery_address` STRING COMMENT '详细配送地址',
    `bd_id` BIGINT COMMENT '销售人员ID，-1表示无归属销售',
    `bd_name` STRING COMMENT '销售人员姓名',
    `m1_name` STRING COMMENT 'M1管理者（销售主管）姓名',
    `m2_name` STRING COMMENT 'M2管理者（销售经理）姓名',
    `m3_name` STRING COMMENT 'M3管理者（销售总监）姓名',
    `sale_area` STRING COMMENT '销售区域名称',
    `origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV（原价总金额）',
    `real_total_amt` DECIMAL(38,18) COMMENT '履约实付GMV（实际支付金额）'
)
COMMENT '批发客户履约数据表，记录批发客户的订单履约信息，包括配送地址、销售层级、交易金额等核心业务数据'
PARTITIONED BY (
    `ds` STRING COMMENT '数据分区字段，格式：yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = '批发客户履约数据表',
    'lifecycle' = '30'
);