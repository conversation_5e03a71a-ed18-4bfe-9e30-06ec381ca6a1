CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_kpi_category_wholesale_wi` (
  `year` STRING COMMENT '年份，格式：YYYY',
  `week_of_year` BIGINT COMMENT '周数，取值范围：1-53',
  `monday` STRING COMMENT '周一日期，格式：YYYYMMDD',
  `sunday` STRING COMMENT '周日日期，格式：YYYYMMDD',
  `sku_type` STRING COMMENT '商品类型；枚举值：自营/代仓',
  `category` STRING COMMENT '品类；枚举值：鲜果/乳制品/其他',
  `order_gmv_amt` DECIMAL(38,18) COMMENT '交易应付总金额（元）',
  `deliver_gmv_amt` DECIMAL(38,18) COMMENT '履约应付总金额（元）',
  `deliver_cust_cnt` BIGINT COMMENT '履约客户数',
  `deliver_cust_arpu` DECIMAL(38,18) COMMENT '履约ARPU（元/客户），计算公式：应付总金额/客户数',
  `deliver_order_cnt` BIGINT COMMENT '履约订单数',
  `deliver_order_avg` DECIMAL(38,18) COMMENT '履约订单均价（元/订单），计算公式：应付总金额/订单数',
  `deliver_cost_amt` DECIMAL(38,18) COMMENT '履约总成本（元）',
  `deliver_gross_profit` DECIMAL(38,18) COMMENT '履约毛利润（元），计算公式：应付总金额-总成本',
  `deliver_gross_profit_rate` DECIMAL(38,18) COMMENT '履约毛利率，计算公式：毛利润/应付总金额'
) 
COMMENT '交易口径KPI指标日汇总表，按商品类型和品类统计批发业务的交易和履约相关指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment'='交易口径KPI指标日汇总表，包含按周维度的商品类型和品类维度的交易和履约指标统计',
  'lifecycle'='30'
);