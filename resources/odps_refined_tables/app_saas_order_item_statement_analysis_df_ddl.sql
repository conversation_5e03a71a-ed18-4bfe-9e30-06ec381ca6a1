```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_saas_order_item_statement_analysis_df` (
  `tenant_id` BIGINT COMMENT '租户ID',
  `order_no` STRING COMMENT '订单编号',
  `order_source` BIGINT COMMENT '订单来源：0-门店下单；1-openapi调用；2-总部代下单',
  `store_id` BIGINT COMMENT '门店ID',
  `store_no` STRING COMMENT '门店编号',
  `store_name` STRING COMMENT '门店名称',
  `store_type` BIGINT COMMENT '门店类型：0-直营店；1-加盟店；2-托管店',
  `store_group_id` BIGINT COMMENT '门店分组ID',
  `store_group_name` STRING COMMENT '门店分组名称',
  `order_time` DATETIME COMMENT '下单时间，格式：年月日时分秒',
  `pay_time` DATETIME COMMENT '支付时间，格式：年月日时分秒',
  `delivery_time` DATETIME COMMENT '配送时间，格式：年月日时分秒',
  `finished_time` DATETIME COMMENT '完成时间，格式：年月日时分秒',
  `payable_price` DECIMAL(38,18) COMMENT '应付价格（应付总额）',
  `delivery_fee` DECIMAL(38,18) COMMENT '配送费',
  `total_price` DECIMAL(38,18) COMMENT '总金额（实收金额）',
  `pay_type` BIGINT COMMENT '支付方式：1-微信支付；2-账期；3-余额支付；4-支付宝支付；5-无需支付',
  `online_pay_channel` BIGINT COMMENT '支付渠道：0-微信；1-汇付',
  `payment_no` STRING COMMENT '支付单号（支付平台交易订单号）',
  `transaction_id` STRING COMMENT '交易流水号（银行流水号）',
  `pay_total_price` DECIMAL(38,18) COMMENT '支付金额',
  `warehouse_type` BIGINT COMMENT '配送仓类型：0-无仓；1-三方仓；2-自营仓',
  `warehouse_no` STRING COMMENT '仓库编号',
  `warehouse_name` STRING COMMENT '配送仓名称',
  `warehouse_service_name` STRING COMMENT '库存仓服务商名称',
  `outbound_batch_no` STRING COMMENT '出库批次号',
  `supplier_id` BIGINT COMMENT '供应商ID',
  `supplier_name` STRING COMMENT '供应商名称',
  `item_id` BIGINT COMMENT '商品ID',
  `item_title` STRING COMMENT '商品名称',
  `item_code` STRING COMMENT '自有编码',
  `goods_type` BIGINT COMMENT '商品类型：0-无货商品；1-报价货品；2-自营货品',
  `specification_unit` STRING COMMENT '规格单位',
  `specification` STRING COMMENT '规格',
  `first_classification_name` STRING COMMENT '一级分类名称',
  `second_classification_name` STRING COMMENT '二级分类名称',
  `sku_id` BIGINT COMMENT '货品ID',
  `sku_code` STRING COMMENT '货品SKU编码',
  `goods_title` STRING COMMENT '货品名称',
  `first_category` STRING COMMENT '一级类目',
  `second_category` STRING COMMENT '二级类目',
  `third_category` STRING COMMENT '三级类目',
  `item_payable_price` DECIMAL(38,18) COMMENT '商品单价',
  `item_amount` BIGINT COMMENT '商品数量',
  `item_total_price` DECIMAL(38,18) COMMENT '商品总价',
  `supply_price` DECIMAL(38,18) COMMENT '供应价',
  `total_supply_price` DECIMAL(38,18) COMMENT '供应总价',
  `gross_margin_ratio` DECIMAL(38,18) COMMENT '毛利率（百分比）',
  `item_refund_price` DECIMAL(38,18) COMMENT '商品退款总金额',
  `outbound_time` DATETIME COMMENT '出库时间，格式：年月日时分秒',
  `outbound_amount` DECIMAL(38,18) COMMENT '出库数量'
)
COMMENT 'SaaS订单对账分析表，包含订单明细、支付信息、商品信息、仓储信息等对账相关数据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = 'SaaS订单对账分析表',
  'lifecycle' = '30'
)
```