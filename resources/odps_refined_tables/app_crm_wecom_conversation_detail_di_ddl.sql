CREATE TABLE IF NOT EXISTS app_crm_wecom_conversation_detail_di(
	conversation_group STRING COMMENT '对话分组ID，用于唯一标识不同的会话组，格式为"BD姓名-客户ID"',
	conversation_list STRING COMMENT '对话消息列表，包含该会话中的所有消息记录，格式为JSON数组',
	cust_id STRING COMMENT '客户ID，用于唯一标识客户：优先取merchant.m_id，如果匹配不到m_id，则取值用户的微信ID',
	cust_name STRING COMMENT '客户名称：优先取merchant.mname，如果匹配不到m_id，则取值用户的微信ID',
	conversation_start_time STRING COMMENT '对话开始时间，格式为yyyy-MM-dd HH:mm:ss，记录会话的第一条消息时间',
	conversation_end_time STRING COMMENT '对话结束时间，格式为yyyy-MM-dd HH:mm:ss，记录会话的最后一条消息时间',
	bd_wecom_id STRING COMMENT 'BD的企业微信ID，即销售人员的企微账号',
	create_time STRING COMMENT '记录创建时间，格式为yyyy-MM-dd HH:mm:ss，用于跟踪数据入库时间'
)
PARTITIONED BY (ds STRING COMMENT '数据分区字段，格式为yyyyMMdd，表示数据所属日期')
STORED AS ALIORC
TBLPROPERTIES ('comment'='企业微信会话明细表，记录销售与客户在企业微信中的完整对话记录，包含文本、图片、语音等多种消息类型')
LIFECYCLE 1000;