```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_finance_cost_di` (
  `date` STRING COMMENT '业务日期，格式：yyyyMMdd，表示数据发生的具体日期',
  `service_area` STRING COMMENT '大区名称，如：云南、华东等',
  `warehouse_no` BIGINT COMMENT '库存仓唯一标识ID，取值范围：2-155',
  `warehouse_name` STRING COMMENT '库存仓完整名称，如：昆明总仓、上海总仓等',
  `category1` STRING COMMENT '商品一级类目，枚举值：鲜果/乳制品/其他',
  `sell_cost_amt` DECIMAL(38,18) COMMENT '含税销售成本金额，单位：元',
  `damage_cost_amt` DECIMAL(38,18) COMMENT '含税货损成本金额，单位：元',
  `sample_cost_amt` DECIMAL(38,18) COMMENT '含税出样成本金额，单位：元',
  `stocktake_cost_amt` DECIMAL(38,18) COMMENT '含税盘盈盘亏成本金额，单位：元',
  `sell_cost_amt_notax` DECIMAL(38,18) COMMENT '不含税销售成本金额，单位：元',
  `damage_cost_amt_notax` DECIMAL(38,18) COMMENT '不含税货损成本金额，单位：元',
  `sample_cost_amt_notax` DECIMAL(38,18) COMMENT '不含税出样成本金额，单位：元',
  `stocktake_cost_amt_notax` DECIMAL(38,18) COMMENT '不含税盘盈盘亏成本金额，单位：元'
) 
COMMENT '财务口径收入数据表，记录各库存仓按商品类目划分的销售成本、货损成本、出样成本和盘盈盘亏成本等财务数据，包含含税和不含税金额'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：yyyyMMdd，用于数据管理和查询优化'
)
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
               'comment'='财务成本数据明细表，支持按日期、大区、仓库、商品类目等多维度分析') 
LIFECYCLE 30;
```