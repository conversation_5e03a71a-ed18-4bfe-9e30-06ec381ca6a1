CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_cust_label_summary_detail_df` (
  `date` STRING COMMENT '日期，格式：yyyyMMdd',
  `cust_team` STRING COMMENT '客户团队类型，枚举：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户',
  `invite_type` STRING COMMENT '来源类型，枚举：bd、driver、cust、自然注册',
  `life_cycle` STRING COMMENT '生命周期标签（粗粒度）',
  `life_cycle_detail` STRING COMMENT '生命周期标签（细粒度）',
  `cust_type` STRING COMMENT '客户类型，枚举：面包蛋糕,西餐,咖啡,甜品冰淇淋,茶饮,其他',
  `register_province` STRING COMMENT '注册时省份',
  `register_city` STRING COMMENT '注册时城市',
  `cust_value` STRING COMMENT '用户价值标签',
  `r_value` STRING COMMENT 'R价值标签，枚举：L-低价值，M-中价值，H-高价值',
  `f_value` STRING COMMENT 'F价值标签，枚举：L-低价值，M-中价值，H-高价值',
  `m_value` STRING COMMENT 'M价值标签，枚举：L-低价值，M-中价值，H-高价值',
  `is_first_register_30d` BIGINT COMMENT '是否在近30日是首次注册客户：0-否，1-是',
  `cust_cnt` BIGINT COMMENT '客户数',
  `private_cust_cnt` BIGINT COMMENT '私海客户数（分配但未离职）',
  `disbaled_private_cust_cnt` BIGINT COMMENT '私海锁定客户数（进行分配但离职）',
  `order_real_amt_60d` DECIMAL(38,18) COMMENT '近60天交易实付金额',
  `delivery_real_amt_60d` DECIMAL(38,18) COMMENT '近60天履约实付金额',
  `delivery_profit_60d` DECIMAL(38,18) COMMENT '近60天履约实付毛利润',
  `private_order_real_amt_60d` DECIMAL(38,18) COMMENT '近60天私海客户数（分配但未离职）交易实付金额',
  `private_delivery_real_amt_60d` DECIMAL(38,18) COMMENT '近60天私海客户数（分配但未离职）履约实付金额',
  `private_delivery_profit_60d` DECIMAL(38,18) COMMENT '近60天私海客户数（分配但未离职）履约实付毛利润',
  `open_order_real_amt_60d` DECIMAL(38,18) COMMENT '近60天公海交易实付金额',
  `open_delivery_real_amt_60d` DECIMAL(38,18) COMMENT '近60天公海履约实付金额',
  `open_delivery_profit_60d` DECIMAL(38,18) COMMENT '近60天公海履约实付毛利润',
  `order_dairy_real_amt_60d` DECIMAL(38,18) COMMENT '近60天乳制品交易实付金额',
  `order_fruit_real_amt_60d` DECIMAL(38,18) COMMENT '近60天鲜果交易实付金额',
  `order_other_real_amt_60d` DECIMAL(38,18) COMMENT '近60天其他交易实付金额',
  `order_real_amt_365d` DECIMAL(38,18) COMMENT '近365天交易实付金额',
  `delivery_real_amt_365d` DECIMAL(38,18) COMMENT '近365天履约实付金额',
  `delivery_profit_365d` DECIMAL(38,18) COMMENT '近365天履约实付毛利润',
  `register_audit_duration_avg` DECIMAL(38,18) COMMENT '平均审核通过时长（首次注册-首次审核）（小时）',
  `audit_relation_duration_avg` DECIMAL(38,18) COMMENT '平均审核分配时长（首次审核-首次分配）（小时）',
  `relation_follow_duration_avg` DECIMAL(38,18) COMMENT '平均拜访时长（首次分配-首次拜访）（小时）',
  `follow_order_duration_avg` DECIMAL(38,18) COMMENT '平均下单时长（首次拜访-首次下单）（小时）',
  `follow_efficient_cnt_30d_avg` DECIMAL(38,18) COMMENT '近30日平均有效拜访次数（有效与上门）',
  `login_cust_cnt_30d` BIGINT COMMENT '近30日登录客户数',
  `sku_cl_cust_cnt_30d` BIGINT COMMENT '近30日点击客户数',
  `login_private_cust_cnt_30d` BIGINT COMMENT '近30日登录私海客户数（分配但未离职）',
  `blacklist_back_cust_cnt` BIGINT COMMENT '拉黑回流门店数',
  `bd_cnt` BIGINT COMMENT '跟进bd数（非离职）',
  `order_real_amt_30d` DECIMAL(38,18) COMMENT '近30天交易实付金额'
)
COMMENT '客户标签汇总明细表，包含客户的多维度标签信息和业务指标统计'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式：yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '客户标签汇总明细表，用于客户画像分析和业务决策支持',
  'lifecycle' = '30'
);