CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_cust_brand_alias_performance_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务日期',
  `brand_alias` STRING COMMENT '品牌别名，用于标识不同品牌',
  `order_origin_amt` DECIMAL(38,18) COMMENT '交易应付总金额，订单原始金额',
  `order_real_amt` DECIMAL(38,18) COMMENT '交易实付总金额，订单实际支付金额',
  `delivery_origin_amt` DECIMAL(38,18) COMMENT '履约应付总金额，履约原始金额',
  `delivery_real_amt` DECIMAL(38,18) COMMENT '履约实付总金额，履约实际支付金额',
  `delivery_cash_real_amt` DECIMAL(38,18) COMMENT '履约现结实付总金额，现金支付部分',
  `delivery_bill_real_amt` DECIMAL(38,18) COMMENT '履约账期实付总金额，账期支付部分',
  `delivery_cost_amt` DECIMAL(38,18) COMMENT '履约商品成本金额，商品成本',
  `delivery_real_gross` DECIMAL(38,18) COMMENT '履约实付毛利润，实际支付毛利润',
  `delivery_real_gross_rate` DECIMAL(38,18) COMMENT '履约实付毛利率，实际支付毛利率',
  `delivery_cust_cnt` BIGINT COMMENT '履约客户数，取值范围：0-152',
  `delivery_point_cnt` BIGINT COMMENT '履约累计点位数，取值范围：0-163',
  `after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后总金额，售后已到账金额',
  `after_sale_rate` DECIMAL(38,18) COMMENT '售后比例，计算公式：已到货售后金额/履约实付GMV'
)
COMMENT '大客户品牌粒度监控表，用于监控大客户品牌维度的业务表现指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据分区日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment'='大客户品牌粒度监控表，包含交易、履约、售后等关键业务指标',
  'lifecycle'='30'
);