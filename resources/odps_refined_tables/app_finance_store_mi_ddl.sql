CREATE TABLE IF NOT EXISTS app_finance_store_mi(
	month STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
	service_area STRING COMMENT '大区名称，如云南、华东等',
	warehouse_no BIGINT COMMENT '库存仓ID，数值型标识，取值范围2-155',
	warehouse_name STRING COMMENT '库存仓名称',
	category1 STRING COMMENT '商品一级类目，枚举类型：鲜果/乳制品/其他',
	store_amt DECIMAL(38,18) COMMENT '含税在库金额，单位：元',
	store_amt_notax DECIMAL(38,18) COMMENT '不含税在库金额，单位：元',
	road_amt DECIMAL(38,18) COMMENT '含税在途金额，单位：元',
	road_amt_notax DECIMAL(38,18) COMMENT '不含税在途金额，单位：元'
) 
PARTITIONED BY (ds STRING COMMENT '分区字段，yyyyMMdd格式的日期，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='财务口径收入数据表，包含各仓库的商品库存和在途金额的财务数据') 
LIFECYCLE 30;