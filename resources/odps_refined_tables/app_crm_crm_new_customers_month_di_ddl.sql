CREATE TABLE IF NOT EXISTS app_crm_crm_new_customers_month_di(
	`m_id` BIGINT COMMENT '商户ID，唯一标识一个商户',
	`bd_id` BIGINT COMMENT '销售ID，唯一标识一个销售人员',
	`day_tag` STRING COMMENT '拉新日期，格式为yyyyMMdd，表示客户被拉新的具体日期'
)
COMMENT '月度拉新客户表，记录每月新增客户信息，包含商户、销售和拉新日期等维度'
PARTITIONED BY (
	`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
	'comment' = '月度拉新客户事实表，用于分析每月新增客户情况和销售业绩'
)
LIFECYCLE 30;