```sql
CREATE TABLE IF NOT EXISTS app_stc_damage_detail_report_df(
    `outbound_date` DATETIME COMMENT '出库日期，格式：年月日时分秒',
    `damage_no` STRING COMMENT '货损批次编号',
    `warehouse_no` BIGINT COMMENT '仓库编号，唯一标识仓库',
    `warehouse_name` STRING COMMENT '仓库名称',
    `sku_id` BIGINT COMMENT 'SKU ID，商品最小库存单位标识',
    `spu_id` BIGINT COMMENT 'SPU ID，商品标准产品单位标识',
    `xianmu_sku` STRING COMMENT '鲜沐SKU编码，鲜沐系统内的商品编码',
    `xianmu_spu_id` BIGINT COMMENT '鲜沐SPU ID，鲜沐系统内的产品单位标识',
    `name` STRING COMMENT '商品名称',
    `specification` STRING COMMENT '商品规格描述',
    `unit` STRING COMMENT '计量单位，如：筐、盒、包、箱、组、罐等',
    `purchaser` STRING COMMENT '采购人员姓名',
    `damage_type` STRING COMMENT '货损类型，枚举值：变质货损、过期货损、仓-破损、仓-其它、运营部-滞销过期、其他',
    `credentials` STRING COMMENT '货损凭证，图片路径或文字说明',
    `damage_quantity` BIGINT COMMENT '货损数量，单位：个',
    `damage_amount` DECIMAL(38,18) COMMENT '货损金额，单位：元',
    `tenant_id` BIGINT COMMENT '租户ID，标识业务租户',
    `category_id` BIGINT COMMENT '一级类目ID，商品分类标识',
    `time_tag` STRING COMMENT '时间标签，格式：yyyyMMdd（年月日）',
    `warehouse_service_provider` STRING COMMENT '仓库服务商名称',
    `price` DECIMAL(38,18) COMMENT '采购单价，单位：元'
)
COMMENT 'SaaS货损明细表，记录商品货损的详细信息，包括货损类型、数量、金额等'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式：yyyyMMdd（年月日）'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment'='SaaS货损明细分析表，用于货损数据统计和分析',
    'columnar.nested.type'='true'
)
LIFECYCLE 30;
```