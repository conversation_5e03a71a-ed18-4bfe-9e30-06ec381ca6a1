```sql
CREATE TABLE IF NOT EXISTS app_log_purchase_main_di(
    `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
    `type` STRING COMMENT '实验分组：取值范围为V3、V4、对照组，表示不同的实验版本',
    `uv` BIGINT COMMENT '首页曝光客户数，即独立访客数量',
    `experimental_uv` BIGINT COMMENT '实验客户首次曝光uv，实验组客户的首次曝光独立访客数',
    `no_first_uv` BIGINT COMMENT '非首次曝光uv，非首次曝光的独立访客数',
    `classification_uv` BIGINT COMMENT '分类模块曝光uv，分类模块的独立访客数',
    `search_uv` BIGINT COMMENT '搜索模块曝光uv，搜索模块的独立访客数',
    `purchase_assistant_uv` BIGINT COMMENT '采购助手曝光uv，采购助手模块的独立访客数',
    `purchase_assistant_first_uv` BIGINT COMMENT '采购助手首次曝光uv，采购助手模块首次曝光的独立访客数',
    `purchase_assistant_nofirst_uv` BIGINT COMMENT '采购助手非首次曝光uv，采购助手模块非首次曝光的独立访客数',
    `pv` BIGINT COMMENT '首页曝光次数，即页面浏览量',
    `experimental_pv` BIGINT COMMENT '实验客户首次曝光pv，实验组客户的首次曝光页面浏览量',
    `no_main_pv` BIGINT COMMENT '非首次曝光pv，非首次曝光的页面浏览量',
    `classification_pv` BIGINT COMMENT '分类模块曝光pv，分类模块的页面浏览量',
    `search_pv` BIGINT COMMENT '搜索模块曝光pv，搜索模块的页面浏览量',
    `purchase_assistant_pv` BIGINT COMMENT '采购助手曝光pv，采购助手模块的页面浏览量',
    `purchase_assistant_first_pv` BIGINT COMMENT '采购助手首次曝光pv，采购助手模块首次曝光的页面浏览量',
    `purchase_assistant_nofirst_pv` BIGINT COMMENT '采购助手非首次曝光pv，采购助手模块非首次曝光的页面浏览量'
) 
COMMENT '采购助手首页流量统计表，记录采购助手首页各模块的曝光UV和PV数据，用于分析实验效果和用户行为'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据入库日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
    'comment'='采购助手首页流量统计表，包含各实验分组的UV/PV数据') 
LIFECYCLE 30;
```