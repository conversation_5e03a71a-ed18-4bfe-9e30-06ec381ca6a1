CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_log_channel_sku_wi` (
  `year` STRING COMMENT '年份，格式：YYYY',
  `week_of_year` STRING COMMENT '周数，格式：1-53',
  `monday` STRING COMMENT '周一日期，格式：YYYYMMDD',
  `sunday` STRING COMMENT '周日日期，格式：YYYYMMDD',
  `sku_id` STRING COMMENT 'SKU编号，商品唯一标识',
  `category1` STRING COMMENT '一级类目，商品分类层级1',
  `category2` STRING COMMENT '二级类目，商品分类层级2',
  `category3` STRING COMMENT '三级类目，商品分类层级3',
  `category4` STRING COMMENT '四级类目，商品分类层级4',
  `sku_type` STRING COMMENT 'SKU类型：1-自营，2-代仓，3-代售',
  `spu_name` STRING COMMENT '商品名称',
  `sku_disc` STRING COMMENT '商品描述，包含规格信息',
  `model` STRING COMMENT '渠道类型：banner-横幅广告，search-搜索，recommend-推荐等',
  `uv` BIGINT COMMENT '曝光用户数，去重后的访问用户数量',
  `pv` BIGINT COMMENT '曝光次数，页面或商品被展示的总次数',
  `click_uv` BIGINT COMMENT '点击用户数，去重后的点击用户数量',
  `click_pv` BIGINT COMMENT '点击次数，用户点击行为的总次数',
  `addbug_uv` BIGINT COMMENT '加购用户数，去重后的加入购物车用户数量',
  `addbug_pv` BIGINT COMMENT '加购次数，加入购物车行为的总次数',
  `cust_cnt` BIGINT COMMENT '交易人数，完成购买的去重用户数量'
) 
COMMENT '流量分渠道SKU转化表，记录各渠道SKU级别的流量转化数据，用于分析商品在不同渠道的曝光、点击、加购和交易转化效果'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式：YYYYMMDD，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '流量分渠道SKU转化分析表',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;