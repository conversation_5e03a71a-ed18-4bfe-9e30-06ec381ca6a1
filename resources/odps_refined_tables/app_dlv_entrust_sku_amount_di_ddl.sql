CREATE TABLE IF NOT EXISTS app_dlv_entrust_sku_amount_di(
	`batch_id` BIGINT COMMENT '调度单号，唯一标识一个运输调度任务',
	`use_type` STRING COMMENT '用车类型：0-干线用车、1-调拨用车、2-采购用车、3-大客户用车',
	`amount` DECIMAL(38,18) COMMENT '费用金额，运输总费用',
	`calculate_cost_name` STRING COMMENT '费用名称，如：干线运费',
	`plan_total_distance` DECIMAL(38,18) COMMENT '调度单计划里程，单位：公里',
	`dist_order_id` BIGINT COMMENT '委托单号，唯一标识一个运输委托',
	`entrust_weight` DECIMAL(38,18) COMMENT '委托重量，单位：kg',
	`entrust_weight_fee` DECIMAL(38,18) COMMENT '委托单按重量分摊到的金额',
	`weigt_per_fee` DECIMAL(38,18) COMMENT '单公斤费用，单位：元/kg',
	`dist_order_fee` DECIMAL(38,18) COMMENT '委托单分摊总金额',
	`entrust_quantity` BIGINT COMMENT '委托数量，商品件数',
	`dist_quantity_fee` DECIMAL(38,18) COMMENT '单件分摊费用，单位：元/件',
	`weigt_distance_fee` DECIMAL(38,18) COMMENT '委托单吨公里分摊费用',
	`weight_distance` DECIMAL(38,18) COMMENT '委托单吨公里数',
	`weigt_kilometer_fee` DECIMAL(38,18) COMMENT '吨公里分摊费，单位：元/吨公里',
	`entrust_distance` DECIMAL(38,18) COMMENT '委托实际公里数',
	`begin_site_id` BIGINT COMMENT '起始仓库点位ID',
	`begin_site_name` STRING COMMENT '起始仓库名称',
	`end_site_id` BIGINT COMMENT '目的仓库点位ID',
	`end_site_name` STRING COMMENT '目的仓库名称',
	`sku_id` STRING COMMENT 'SKU编号，商品唯一标识',
	`sku_disc` STRING COMMENT '商品描述，商品详细规格说明',
	`spu_name` STRING COMMENT '商品名称',
	`sku_type` BIGINT COMMENT '商品类型：0-自营、1-代仓、2-代售',
	`sub_type` BIGINT COMMENT '商品二级性质：1-自营代销不入仓、2-自营代销入仓、3-自营经销、4-代仓代仓',
	`sku_cnt` BIGINT COMMENT 'SKU合计件数',
	`sku_weight` DECIMAL(38,18) COMMENT 'SKU合计重量，单位：kg',
	`sku_volume` DECIMAL(38,18) COMMENT 'SKU合计体积，单位：立方米',
	`sku_weight_distance` DECIMAL(38,18) COMMENT 'SKU合计吨公里数',
	`sku_fee_by_unit` DECIMAL(38,18) COMMENT 'SKU费用-按件数分摊',
	`sku_fee_by_weiht_km` DECIMAL(38,18) COMMENT 'SKU费用-按吨公里分摊',
	`sku_fee_by_weiht_per` DECIMAL(38,18) COMMENT 'SKU费用-按重量分摊'
) 
COMMENT '品类干线分摊表，记录商品在干线运输中的费用分摊明细，包括按重量、件数、吨公里等多种分摊方式的计算结果'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='品类干线分摊明细表，用于商品运输成本分摊分析和核算') 
LIFECYCLE 30;