CREATE TABLE IF NOT EXISTS app_cust_performance_comm_mi_extra_h(
	order_source STRING COMMENT '订单来源：SAAS-软件即服务，鲜沐-鲜沐平台',
	cust_id BIGINT COMMENT '客户ID，唯一标识客户',
	cust_name STRING COMMENT '客户名称',
	cust_type STRING COMMENT '客户类型',
	cust_value_lable STRING COMMENT '客户价值标签：高价值-高价值客户，潜力高价值-潜力高价值客户，准高价值-准高价值客户',
	bd_id BIGINT COMMENT 'BD ID，业务发展人员ID',
	bd_name STRING COMMENT '履约当天BD姓名',
	is_test_bd STRING COMMENT '是否测试BD：是-测试BD，否-非测试BD',
	bd_work_zone STRING COMMENT 'BD工作区域',
	bd_region STRING COMMENT 'BD所属大区',
	dlv_real_amt DECIMAL(38,18) COMMENT '履约实付GMV，实际支付的交易总额',
	dlv_real_amt_at DECIMAL(38,18) COMMENT 'AT履约实付金额，AT相关履约实付金额',
	dlv_real_amt_expo DECIMAL(38,18) COMMENT '流量品履约实付金额，流量相关产品履约实付金额',
	dlv_real_amt_profit DECIMAL(38,18) COMMENT '利润品履约实付金额，利润相关产品履约实付金额',
	dlv_real_amt_normal DECIMAL(38,18) COMMENT '常规品履约实付金额，常规产品履约实付金额',
	dlv_real_amt_fruit DECIMAL(38,18) COMMENT '鲜果履约实付金额，鲜果产品履约实付金额',
	total_group_score DECIMAL(38,18) COMMENT 'BD累计利润积分，BD累计的利润相关积分',
	bd_performance_rate DECIMAL(38,18) COMMENT 'BD利润积分系数，BD利润相关的绩效系数',
	cust_comm_amt DECIMAL(38,18) COMMENT '客户佣金：高价值客户25，其余客户0',
	total_comm_amt DECIMAL(38,18) COMMENT '高价值客户佣金汇总，高价值客户佣金总额',
	dlv_spu_cnt BIGINT COMMENT '履约SPU数，履约的标准产品单位数量',
	more_than_spu_cnt BIGINT COMMENT '超额SPU数，超过标准的SPU数量',
	is_more_than_spu STRING COMMENT '是否超额SPU客户：是-超额SPU客户，否-非超额SPU客户',
	more_than_spu_comm DECIMAL(38,18) COMMENT '超额SPU数佣金，超额SPU相关的佣金',
	is_complete_amt STRING COMMENT '月累计履约实付GMV是否满足高价值：是-满足高价值，否-不满足高价值',
	is_complete_spu STRING COMMENT '月累计履约SPU数是否满足高价值：是-满足高价值，否-不满足高价值',
	dlv_real_amt_today DECIMAL(38,18) COMMENT '今日待履约实付金额，今日待履约的实际支付金额',
	dlv_order_amt_today DECIMAL(38,18) COMMENT '今日交易待履约实付金额，今日交易相关的待履约实付金额',
	dlv_other_amt_today DECIMAL(38,18) COMMENT '其余待履约实付金额，其他类型的待履约实付金额',
	dlv_real_spu_cnt_today BIGINT COMMENT '今日待履约SPU数（月去重），今日待履约的SPU数量（按月去重）',
	dlv_order_spu_cnt_today BIGINT COMMENT '今日交易待履约SPU数（月去重），今日交易相关的待履约SPU数量（按月去重）',
	dlv_other_spu_cnt_today BIGINT COMMENT '其余待履约SPU数（月去重），其他类型的待履约SPU数量（按月去重）',
	dlv_month_total_amt DECIMAL(38,18) COMMENT '本月累计履约金额（离线+未来），本月累计的履约金额（包括离线和未来数据）',
	dlv_month_total_spu_cnt BIGINT COMMENT '本月累计履约SPU数（离线+未来去重），本月累计的履约SPU数量（包括离线和未来数据，去重）',
	dlv_month_today_total_spu_cnt BIGINT COMMENT '本月截止今天累计履约SPU数（离线+今日去重），本月截止今天的累计履约SPU数量（包括离线和今日数据，去重）'
) 
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='单客户本月维度绩效表现表，记录客户绩效相关数据和BD绩效指标') 
LIFECYCLE 30;