CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_crm_area_consign_performance_mi` (
  `month` STRING COMMENT '月份，格式：yyyyMM',
  `administrative_city` STRING COMMENT '行政城市名称',
  `area` STRING COMMENT '行政区名称',
  `zone_name` STRING COMMENT '销售所属区域名称',
  `m1` STRING COMMENT '城市负责人（M1）姓名',
  `m2` STRING COMMENT '区域负责人（M2）姓名',
  `m3` STRING COMMENT '部门负责人（M3）姓名',
  `cust_type` STRING COMMENT '客户类型；枚举值：面包蛋糕,西餐,咖啡,甜品冰淇淋,茶饮,其他',
  `order_cust_cnt` BIGINT COMMENT '下单客户数量',
  `order_sku_cnt` BIGINT COMMENT '销售商品数量（销量）',
  `order_cnt` BIGINT COMMENT '订单数量',
  `real_total_amt` DECIMAL(38,18) COMMENT '订单实付金额（元）',
  `drop_in_visit_cust_cnt` BIGINT COMMENT '上门拜访客户数（上门/有效拜访）',
  `visit_cust_cnt` BIGINT COMMENT '总拜访客户数（包含所有拜访类型）'
) 
COMMENT '区域粒度代售业绩报表汇总表，按行政区域和销售区域维度统计代售业务绩效指标（目前只有上海数据）'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式：yyyyMMdd，表示数据日期'
)
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
               'comment'='区域代售业绩统计表，包含客户类型、订单指标、拜访指标等多维度业务数据') 
LIFECYCLE 30;