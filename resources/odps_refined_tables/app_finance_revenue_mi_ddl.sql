CREATE TABLE IF NOT EXISTS app_finance_revenue_mi(
	month STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
	service_area STRING COMMENT '大区名称，如云南、华东等',
	warehouse_no BIGINT COMMENT '库存仓ID，取值范围2-155，唯一标识一个仓库',
	warehouse_name STRING COMMENT '库存仓名称，如昆明总仓、上海总仓等',
	cust_team STRING COMMENT '客户团队类型：平台客户/大客户',
	category1 STRING COMMENT '商品一级类目：鲜果/乳制品/其他',
	cash_revenue_amt DECIMAL(38,18) COMMENT '现结含税收入金额',
	bill_revenue_amt DECIMAL(38,18) COMMENT '账期含税收入金额',
	cash_revenue_amt_notax DECIMAL(38,18) COMMENT '现结不含税收入金额',
	bill_revenue_amt_notax DECIMAL(38,18) COMMENT '账期不含税收入金额',
	revenue_amt DECIMAL(38,18) COMMENT '含税总收入金额（现结+账期）',
	revenue_profit_amt DECIMAL(38,18) COMMENT '含税毛利润金额',
	revenue_amt_notax DECIMAL(38,18) COMMENT '不含税总收入金额（现结+账期）',
	revenue_profit_amt_notax DECIMAL(38,18) COMMENT '不含税毛利润金额'
) 
COMMENT '财务口径收入数据表，包含各仓库、各商品类目的收入和利润数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='财务收入分析表，用于收入统计和利润分析') 
LIFECYCLE 30;