CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_city_trade_selfowened_mi` (
  `month` STRING COMMENT '月份，格式：yyyyMM',
  `register_province` STRING COMMENT '注册省份',
  `register_city` STRING COMMENT '注册城市',
  `register_area` STRING COMMENT '注册区域',
  `cause_type` STRING COMMENT '业务类型：鲜沐,SAAS',
  `cust_type` STRING COMMENT '客户类型；枚举：面包蛋糕,西餐,咖啡,甜品冰淇淋,茶饮,其他',
  `brand_type` STRING COMMENT '大客户类型；枚举：大客户,普通,批发客户',
  `brand_name` STRING COMMENT '品牌名称',
  `category1` STRING COMMENT '一级类目',
  `category2_id` STRING COMMENT '二级类目ID',
  `category2` STRING COMMENT '二级类目名称',
  `category3_id` STRING COMMENT '三级类目ID',
  `category3` STRING COMMENT '三级类目名称',
  `category4_id` STRING COMMENT '四级类目ID',
  `category4` STRING COMMENT '四级类目名称',
  `origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额（元）',
  `real_total_amt` DECIMAL(38,18) COMMENT '实付总金额（元）',
  `cust_cnt` BIGINT COMMENT '客户数量，范围：1-50',
  `new_cust_cnt` BIGINT COMMENT '（历史截止当天）当天新客户数量，范围：0-6',
  `order_time_cnt` DECIMAL(38,18) COMMENT '下单时间间隔之和（分钟）',
  `order_time_avg` DECIMAL(38,18) COMMENT '平均下单时间间隔（分钟）',
  `month_cust_cnt` BIGINT COMMENT 'T-1月T月客户数量，范围：0-37',
  `before_month_cust_cnt` BIGINT COMMENT 'T-1月客户数量，范围：0-84'
)
COMMENT '自营品牌城市整体交易数据月表，包含各城市区域的自营品牌交易统计信息'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式：yyyyMM'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '自营品牌城市交易月粒度统计表',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;