CREATE TABLE IF NOT EXISTS app_saas_brand_city_delivery_wi(
	year STRING COMMENT '年份，格式为yyyy',
	week_of_year BIGINT COMMENT '周数，取值范围：1-53',
	monday STRING COMMENT '周一日期，格式为yyyyMMdd（年月日）',
	sunday STRING COMMENT '周日日期，格式为yyyyMMdd（年月日）',
	brand_alias STRING COMMENT '品牌名称，如：GIGI LUCKY舒芙蕾',
	province STRING COMMENT '省份名称，如：广东',
	city STRING COMMENT '城市名称，如：广州市',
	area STRING COMMENT '区域名称，如：天河区',
	point_cnt BIGINT COMMENT '点位数，取值范围：1-39'
) 
COMMENT 'SaaS履约网络可视化表，展示各品牌在不同城市区域的配送点位分布情况'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd（年月日）') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='SaaS履约网络可视化表，用于分析品牌在各地区的配送网络覆盖情况') 
LIFECYCLE 30;