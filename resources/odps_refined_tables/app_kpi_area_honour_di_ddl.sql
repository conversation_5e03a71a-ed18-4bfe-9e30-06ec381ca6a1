CREATE TABLE IF NOT EXISTS app_kpi_area_honour_di(
	`date` STRING COMMENT '日期，格式：yyyyMMdd',
	`area_no` BIGINT COMMENT '城配仓编号',
	`area_name` STRING COMMENT '城配仓名称',
	`sku_type` STRING COMMENT '商品类型：自营-自营商品，代仓-代仓商品，代售-代售商品',
	`origin_total_amt` DECIMAL(38,18) COMMENT '原始订单总金额',
	`real_total_amt` DECIMAL(38,18) COMMENT '实际支付总金额',
	`cost_amt` DECIMAL(38,18) COMMENT '商品成本金额',
	`preferential_amt` DECIMAL(38,18) COMMENT '营销优惠金额',
	`origin_pay_rate` DECIMAL(38,18) COMMENT '应付毛利率（原始订单金额计算）',
	`real_pay_rate` DECIMAL(38,18) COMMENT '实付毛利率（实际支付金额计算）',
	`refund_amt` DECIMAL(38,18) COMMENT '售后退款总金额',
	`cust_cnt` BIGINT COMMENT '客户数量',
	`cust_unit_amt` DECIMAL(38,18) COMMENT '客户平均单价',
	`order_cnt` BIGINT COMMENT '订单数量',
	`point_cnt` BIGINT COMMENT '配送点位数',
	`storage_amt` DECIMAL(38,18) COMMENT '仓储成本费用',
	`arterial_roads_amt` DECIMAL(38,18) COMMENT '干线运输成本费用',
	`deliver_amt` DECIMAL(38,18) COMMENT '配送成本费用',
	`total_deliver_amt` DECIMAL(38,18) COMMENT '履约总费用（仓储+干线+配送）'
) 
COMMENT '履约KPI城配仓维度日表，按城配仓维度统计每日履约相关关键绩效指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：yyyyMMdd') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='履约KPI城配仓维度日表，包含各城配仓的订单金额、成本费用、客户订单等核心指标数据') 
LIFECYCLE 30;