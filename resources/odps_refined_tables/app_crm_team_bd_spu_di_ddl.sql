CREATE TABLE IF NOT EXISTS app_crm_team_bd_spu_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务发生日期',
	`cust_team` STRING COMMENT '客户团队类型，取值范围：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户',
	`brand_alias` STRING COMMENT '品牌别称，如：茉莉奶白、乐乐茶等',
	`register_province` STRING COMMENT '注册时省份名称',
	`register_city` STRING COMMENT '注册时城市名称',
	`brand_id` BIGINT COMMENT '公司ID，唯一标识一个品牌公司',
	`brand_name` STRING COMMENT '公司全称',
	`bd_id` BIGINT COMMENT '销售BD的ID，唯一标识一个销售人员',
	`bd_name` STRING COMMENT '销售BD的姓名',
	`spu_no` STRING COMMENT 'SPU编号，商品标准单元的唯一标识',
	`spu_name` STRING COMMENT '商品名称，SPU的标准名称',
	`sku_spec` STRING COMMENT '商品规格描述，如：1KG*12包、5斤*1箱等',
	`sku_brand` STRING COMMENT '商品品牌名称',
	`sku_variety` STRING COMMENT '商品品种，如：皇冠梨、人参果等',
	`category_1` STRING COMMENT '一级商品类目',
	`category_2` STRING COMMENT '二级商品类目',
	`category_3` STRING COMMENT '三级商品类目',
	`category_4` STRING COMMENT '四级商品类目',
	`sku_type` BIGINT COMMENT '商品类型：0-自营，1-代仓，2-其他',
	`is_self_owned_brand` BIGINT COMMENT '是否自营品牌：1-是，0-否',
	`origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额，原始订单金额',
	`real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额，实际支付金额',
	`cost_amt` DECIMAL(38,18) COMMENT '履约成本金额',
	`sku_cnt` BIGINT COMMENT '总配送件数，商品配送数量',
	`cust_cnt` BIGINT COMMENT '履约门店数，参与履约的门店数量',
	`after_sale_amt` DECIMAL(38,18) COMMENT '售后金额，退货退款等售后相关金额'
) 
COMMENT '大客户团队商品SPU与客户名称与BD结合维度汇总表，用于分析大客户团队的销售业绩和商品分布情况'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='大客户团队商品SPU维度汇总表，包含客户信息、BD信息、商品信息和履约数据') 
LIFECYCLE 30;