CREATE TABLE IF NOT EXISTS app_stc_warehouse_sku_initquantity_22_di(
	`date` STRING COMMENT '日期，格式：yyyyMMdd',
	`warehouse_no` BIGINT COMMENT '库存仓ID',
	`warehouse_name` STRING COMMENT '库存仓名',
	`batch_no` STRING COMMENT '批次编号',
	`sku_id` STRING COMMENT 'SKU编号',
	`sku_property` STRING COMMENT 'SKU属性：常规,活动,临保,拆包,不卖,破袋',
	`spu_id` BIGINT COMMENT 'SPU ID（产品ID）',
	`spu_no` STRING COMMENT 'SPU编号',
	`spu_name` STRING COMMENT 'SPU名称',
	`sku_disc` STRING COMMENT 'SKU描述（规格信息）',
	`category` STRING COMMENT '一级类目',
	`storage_way` STRING COMMENT '存储方式：冷冻、冷藏、常温等',
	`pack_unit` STRING COMMENT '包装单位：箱、包、盒、袋等',
	`production_date` DATETIME COMMENT '生产日期，格式：yyyy-MM-dd HH:mm:ss',
	`quality_date` DATETIME COMMENT '保质期，格式：yyyy-MM-dd HH:mm:ss',
	`init_quantity` BIGINT COMMENT '期初库存数量',
	`warehouse_sku_quantity` BIGINT COMMENT '仓库库存数量'
) 
COMMENT 'SKU每日期初库存量表，记录每日SKU的期初库存和仓库库存信息'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：yyyyMMdd') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='SKU期初库存明细表，包含SKU基本信息、库存数量、生产日期和保质期等信息') 
LIFECYCLE 30;