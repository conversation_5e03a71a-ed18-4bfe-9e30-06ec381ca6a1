CREATE TABLE IF NOT EXISTS app_kpi_cust_trade_mi(
	month STRING COMMENT '月份，格式：yyyyMM',
	cust_class STRING COMMENT '客户类型枚举：Mars大客户、平台客户、集团大客户（茶百道）、大客户（茶百道）、大客户（非茶百道）、批发、普通（非品牌）、普通（品牌）',
	origin_total_amt DECIMAL(38,18) COMMENT '应付总金额',
	real_total_amt DECIMAL(38,18) COMMENT '实付总金额',
	cust_cnt BIGINT COMMENT '客户数',
	cust_arpu DECIMAL(38,18) COMMENT 'ARPU值（应付总金额/客户数）',
	order_cnt BIGINT COMMENT '订单数',
	order_avg DECIMAL(38,18) COMMENT '订单均价（应付总金额/订单数）',
	after_sale_noreceived_amt DECIMAL(38,18) COMMENT '未到货售后总金额',
	after_sale_rate DECIMAL(38,18) COMMENT '退货率（未到货售后总金额/应付总金额）',
	target_origin_total_amt DECIMAL(38,18) COMMENT '目标应付总金额',
	target_cust_cnt BIGINT COMMENT '目标客户数',
	target_cust_arpu DECIMAL(38,18) COMMENT '目标ARPU值',
	target_order_cnt BIGINT COMMENT '目标订单数',
	target_after_sale_rate DECIMAL(38,18) COMMENT '目标退货率',
	dire_origin_total_amt DECIMAL(38,18) COMMENT '直发采购应付总金额',
	delivery_amt DECIMAL(38,18) COMMENT '运费（运费+超时加单费）',
	timing_origin_total_amt DECIMAL(38,18) COMMENT '省心送应付总金额'
) 
COMMENT '交易口径KPI指标月汇总表，包含各类客户类型的交易指标统计和KPI目标对比数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='交易口径KPI指标月汇总表，用于监控和分析各类客户类型的交易表现和KPI达成情况') 
LIFECYCLE 30;