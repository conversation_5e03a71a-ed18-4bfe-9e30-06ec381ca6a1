CREATE TABLE IF NOT EXISTS app_cust_mtd_category_comm_mi_extra_h(
	bd_id STRING COMMENT 'BD ID，销售人员的唯一标识',
	bd_name STRING COMMENT 'BD姓名，销售人员的姓名',
	bd_region STRING COMMENT '大区，销售人员所属的大区',
	bd_work_zone STRING COMMENT '区域，销售人员所属的工作区域',
	cust_id BIGINT COMMENT '客户ID，客户的唯一标识',
	cust_name STRING COMMENT '客户名称，客户的姓名或公司名称',
	cust_type STRING COMMENT '客户类型：新增-新客户，存量-老客户',
	cust_type_detail STRING COMMENT '客户类型详情：上月履约（交易）-上月有交易，近2月有履约（交易）-近2个月有交易，近3月有履约（交易）-近3个月有交易，近4~6月有履约（交易）-近4-6个月有交易，近7~12月有履约（交易）-近7-12个月有交易，近365天无履约（交易）-近365天无交易',
	category1 STRING COMMENT '一级类目，商品的一级分类',
	spu_group STRING COMMENT '推广品类，推广的商品品类',
	is_dlv_payment BIGINT COMMENT '结算方式：1-履约结算，0-交易结算',
	is_complete BIGINT COMMENT '是否完成：1-是（本月履约件数>0），0-否（本月履约件数=0）',
	dlv_origin_amt DECIMAL(38,18) COMMENT '履约(交易)应付GMV，原始应付金额',
	dlv_real_amt DECIMAL(38,18) COMMENT '履约(交易)实付GMV，实际支付金额',
	item_profit_amt DECIMAL(38,18) COMMENT '自营商品毛利润，商品销售的毛利润',
	dlv_sku_cnt BIGINT COMMENT '履约(交易)件数，履约或交易的商品件数',
	small_sku_cnt DECIMAL(38,18) COMMENT '小规格履约(交易)件数，小规格商品的履约或交易件数',
	big_sku_cnt DECIMAL(38,18) COMMENT '大规格履约(交易)件数，大规格商品的履约或交易件数',
	old_cust_comm DECIMAL(38,18) COMMENT '存量推广佣金，老客户的推广佣金',
	new_cust_comm DECIMAL(38,18) COMMENT '新客推广佣金，新客户的推广佣金',
	category_comm_amt DECIMAL(38,18) COMMENT '品类推广佣金，商品品类的推广佣金',
	dlv_sku_cnt_today DECIMAL(38,18) COMMENT '今日待履约数量，今日待履约的商品件数',
	dlv_order_cnt_today DECIMAL(38,18) COMMENT '今日交易待履约数量，今日交易待履约的商品件数',
	dlv_other_cnt_today DECIMAL(38,18) COMMENT '其余待履约数量，其他待履约的商品件数',
	dlv_real_amt_today DECIMAL(38,18) COMMENT '今日待履约实付金额，今日待履约的实际支付金额',
	dlv_order_amt_today DECIMAL(38,18) COMMENT '今日交易待履约实付金额，今日交易待履约的实际支付金额',
	dlv_other_amt_today DECIMAL(38,18) COMMENT '其余待履约实付金额，其他待履约的实际支付金额',
	old_cust_comm_today DECIMAL(38,18) COMMENT '今日存量推广佣金，今日老客户的推广佣金',
	new_cust_comm_today DECIMAL(38,18) COMMENT '今日新客推广佣金，今日新客户的推广佣金',
	category_comm_amt_today DECIMAL(38,18) COMMENT '今日品类推广佣金，今日商品品类的推广佣金',
	month_old_cust_comm DECIMAL(38,18) COMMENT '本月存量推广佣金（离线+今日），本月老客户的推广佣金（包含离线和今日数据）',
	month_new_cust_comm DECIMAL(38,18) COMMENT '本月新客推广佣金（离线+今日），本月新客户的推广佣金（包含离线和今日数据）',
	month_category_cust_comm DECIMAL(38,18) COMMENT '本月品类推广佣金（离线+今日），本月商品品类的推广佣金（包含离线和今日数据）',
	month_dlv_sku_cnt_today DECIMAL(38,18) COMMENT '本月履约数量（离线+今日），本月履约的商品件数（包含离线和今日数据）',
	month_dlv_real_amt_today DECIMAL(38,18) COMMENT '本月履约实付金额（离线+今日），本月履约的实际支付金额（包含离线和今日数据）'
) 
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='客户MTD维度绩效表现表，记录客户在月度至今（MTD）维度的绩效数据，包括履约、交易、佣金等信息') 
LIFECYCLE 30;