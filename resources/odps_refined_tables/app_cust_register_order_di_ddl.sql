```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_cust_register_order_di` (
  `register_date` STRING COMMENT '注册日期，格式：yyyyMMdd',
  `cust_team` STRING COMMENT '客户团队类型，枚举值：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户、批发',
  `register_cust_cnt` BIGINT COMMENT '当日注册客户数',
  `first_order_cust_cnt_0` BIGINT COMMENT 't0首单客户数（注册当天完成首单）',
  `first_order_cust_cnt_1` BIGINT COMMENT 't1首单客户数（注册后第1天完成首单）',
  `first_order_cust_cnt_2` BIGINT COMMENT 't2首单客户数（注册后第2天完成首单）',
  `first_order_cust_cnt_3` BIGINT COMMENT 't3首单客户数（注册后第3天完成首单）',
  `first_order_cust_cnt_4` BIGINT COMMENT 't4首单客户数（注册后第4天完成首单）',
  `first_order_cust_cnt_5` BIGINT COMMENT 't5首单客户数（注册后第5天完成首单）',
  `first_order_cust_cnt_6` BIGINT COMMENT 't6首单客户数（注册后第6天完成首单）',
  `first_order_cust_cnt_7` BIGINT COMMENT 't7首单客户数（注册后第7天完成首单）',
  `first_order_cust_cnt_8` BIGINT COMMENT 't8首单客户数（注册后第8天完成首单）',
  `first_order_cust_cnt_9` BIGINT COMMENT 't9首单客户数（注册后第9天完成首单）',
  `first_order_cust_cnt_10` BIGINT COMMENT 't10首单客户数（注册后第10天完成首单）',
  `first_order_cust_cnt_11` BIGINT COMMENT 't11首单客户数（注册后第11天完成首单）',
  `first_order_cust_cnt_12` BIGINT COMMENT 't12首单客户数（注册后第12天完成首单）',
  `first_order_cust_cnt_13` BIGINT COMMENT 't13首单客户数（注册后第13天完成首单）',
  `first_order_cust_cnt_14` BIGINT COMMENT 't14首单客户数（注册后第14天完成首单）',
  `first_order_cust_cnt_15` BIGINT COMMENT 't15首单客户数（注册后第15天完成首单）',
  `first_order_cust_cnt_16` BIGINT COMMENT 't16首单客户数（注册后第16天完成首单）',
  `first_order_cust_cnt_17` BIGINT COMMENT 't17首单客户数（注册后第17天完成首单）',
  `first_order_cust_cnt_18` BIGINT COMMENT 't18首单客户数（注册后第18天完成首单）',
  `first_order_cust_cnt_19` BIGINT COMMENT 't19首单客户数（注册后第19天完成首单）',
  `first_order_cust_cnt_20` BIGINT COMMENT 't20首单客户数（注册后第20天完成首单）',
  `first_order_cust_cnt_21` BIGINT COMMENT 't21首单客户数（注册后第21天完成首单）',
  `first_order_cust_cnt_22` BIGINT COMMENT 't22首单客户数（注册后第22天完成首单）',
  `first_order_cust_cnt_23` BIGINT COMMENT 't23首单客户数（注册后第23天完成首单）',
  `first_order_cust_cnt_24` BIGINT COMMENT 't24首单客户数（注册后第24天完成首单）',
  `first_order_cust_cnt_25` BIGINT COMMENT 't25首单客户数（注册后第25天完成首单）',
  `first_order_cust_cnt_26` BIGINT COMMENT 't26首单客户数（注册后第26天完成首单）',
  `first_order_cust_cnt_27` BIGINT COMMENT 't27首单客户数（注册后第27天完成首单）',
  `first_order_cust_cnt_28` BIGINT COMMENT 't28首单客户数（注册后第28天完成首单）',
  `first_order_cust_cnt_29` BIGINT COMMENT 't29首单客户数（注册后第29天完成首单）',
  `first_order_cust_cnt_30` BIGINT COMMENT 't30首单客户数（注册后第30天完成首单）',
  `first_order_cust_cnt_31` BIGINT COMMENT 't31首单客户数（注册后第31天完成首单）'
)
COMMENT '注册用户首购日分布表，统计每日注册客户在不同时间窗口内完成首单的数量分布'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据统计日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '注册用户首购行为分析表，用于分析新注册用户在注册后31天内完成首单的时间分布情况',
  'lifecycle' = '30'
)
```