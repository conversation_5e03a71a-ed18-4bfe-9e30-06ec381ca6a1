```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_saas_merchant_store_detail_purchase_di` (
  `tenant_id` BIGINT COMMENT '租户ID',
  `time_tag` STRING COMMENT '时间标签，格式为yyyyMMdd，表示年月日',
  `store_id` BIGINT COMMENT '门店ID',
  `store_name` STRING COMMENT '门店名称',
  `type` BIGINT COMMENT '门店类型：0-直营店，1-加盟店，2-托管店',
  `purchase_num` BIGINT COMMENT '采购商品数量',
  `purchase_price` DECIMAL(38,18) COMMENT '采购金额',
  `refund_num` BIGINT COMMENT '退款商品数量',
  `refund_price` DECIMAL(38,18) COMMENT '退款金额',
  `contact` STRING COMMENT '门店联系人姓名',
  `phone` STRING COMMENT '店长手机号码',
  `bill_switch` BIGINT COMMENT '账期开关：1-开启，0-关闭',
  `store_no_delete` BIGINT COMMENT '门店编号（待删除字段）',
  `group_name` STRING COMMENT '门店分组名称',
  `store_no` STRING COMMENT '门店编号'
)
COMMENT 'SaaS门店采购数据表，包含门店基本信息、采购数据、退款数据等业务指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = 'SaaS门店采购数据明细表，用于分析门店采购业务情况',
  'lifecycle' = '30'
);
```