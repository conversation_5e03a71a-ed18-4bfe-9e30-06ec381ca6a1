CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_kpi_operate_category_delivery_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务日期',
  `category` STRING COMMENT '商品品类：鲜果，乳制品，其他',
  `origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV，原始总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '履约实付GMV，实际支付总金额',
  `marketing_amt` DECIMAL(38,18) COMMENT '营销费用，用于营销活动的费用',
  `cost_amt` DECIMAL(38,18) COMMENT '成本金额，商品成本',
  `origin_gross` DECIMAL(38,18) COMMENT '应付毛利润，原始毛利润',
  `real_gross` DECIMAL(38,18) COMMENT '实付毛利润，实际毛利润',
  `origin_gross_margin` DECIMAL(38,18) COMMENT '应付毛利率，原始毛利率百分比',
  `real_gross_margin` DECIMAL(38,18) COMMENT '实付毛利率，实际毛利率百分比',
  `cust_cnt` BIGINT COMMENT '履约客户数，完成履约的客户数量',
  `point_cnt` BIGINT COMMENT '点位数，服务网点数量',
  `origin_pre_cust_price` DECIMAL(38,18) COMMENT '应付客单价，原始每个客户平均支付金额',
  `real_pre_cust_price` DECIMAL(38,18) COMMENT '实付客单价，实际每个客户平均支付金额',
  `timing_origin_amt` DECIMAL(38,18) COMMENT '省心送应付GMV，定时配送服务的原始金额',
  `timing_real_amt` DECIMAL(38,18) COMMENT '省心送实付GMV，定时配送服务的实际金额',
  `consign_origin_amt` DECIMAL(38,18) COMMENT '代售应付GMV，代售服务的原始金额',
  `consign_real_amt` DECIMAL(38,18) COMMENT '代售实付GMV，代售服务的实际金额',
  `consign_marketing_amt` DECIMAL(38,18) COMMENT '代售营销费用，代售服务的营销费用',
  `consign_origin_gross` DECIMAL(38,18) COMMENT '代售应付毛利润，代售服务的原始毛利润',
  `consign_real_gross` DECIMAL(38,18) COMMENT '代售实付毛利润，代售服务的实际毛利润',
  `consign_cust_cnt` BIGINT COMMENT '代售履约客户数，使用代售服务的客户数量',
  `turnover_day_cnt` DECIMAL(38,18) COMMENT '周转天数，库存周转所需天数',
  `damage_amt` DECIMAL(38,18) COMMENT '货损金额，商品损坏造成的损失金额',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储费，商品存储费用',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '单点干线费，主要运输路线费用',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提费，客户自提商品产生的费用',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨费，商品调拨产生的费用',
  `other_amt` DECIMAL(38,18) COMMENT '其他费，其他未分类费用',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送费，商品配送服务费用'
)
COMMENT '运营履约KPI表（平台客户），包含商品品类的履约相关关键绩效指标数据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据分区日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '运营履约KPI表，按商品品类统计平台客户的履约相关指标数据',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;