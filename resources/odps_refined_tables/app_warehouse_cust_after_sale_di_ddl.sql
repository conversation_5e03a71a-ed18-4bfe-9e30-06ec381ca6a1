CREATE TABLE IF NOT EXISTS app_warehouse_cust_after_sale_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计日期',
	`register_province` STRING COMMENT '客户注册省份',
	`register_city` STRING COMMENT '客户注册城市',
	`register_area` STRING COMMENT '客户注册区域',
	`cust_class` STRING COMMENT '客户类型：普通（非品牌）、大客户（非茶百道）等',
	`brand_alias` STRING COMMENT '品牌别名：无、楼下酸奶等',
	`order_type` STRING COMMENT '订单类型：省心送、其他',
	`warehouse_no` BIGINT COMMENT '库存仓编号，数值型标识',
	`warehouse_name` STRING COMMENT '库存仓名称',
	`sku_id` STRING COMMENT 'SKU编号，商品唯一标识',
	`sku_disc` STRING COMMENT '商品描述，包含规格、重量等信息',
	`spu_name` STRING COMMENT '商品名称',
	`category_1` STRING COMMENT '一级类目：鲜果、乳制品等',
	`category_4` STRING COMMENT '四级类目：提子、芒果、橙、柿子等',
	`after_sale_sku_cnt` BIGINT COMMENT '已到货售后数量，统计售后商品数量',
	`after_sale_cnt` BIGINT COMMENT '已到货售后次数，统计售后订单次数',
	`after_sale_amt` DECIMAL(38,18) COMMENT '已到货售后金额，精确到小数点后18位',
	`after_sale_short_amt` DECIMAL(38,18) COMMENT '售后缺货金额，精确到小数点后18位',
	`after_sale_reissue_amt` DECIMAL(38,18) COMMENT '售后补发金额，精确到小数点后18位',
	`deliver_total_amt` DECIMAL(38,18) COMMENT '配送实付总金额，精确到小数点后18位',
	`coupon_amt` DECIMAL(38,18) COMMENT '优惠券金额，精确到小数点后18位'
) 
COMMENT '库存仓+客户+SKU售后数据汇总表，包含按仓库、客户、商品维度的售后数据统计'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='库存仓客户SKU售后数据汇总表，用于分析售后业务情况') 
LIFECYCLE 30;