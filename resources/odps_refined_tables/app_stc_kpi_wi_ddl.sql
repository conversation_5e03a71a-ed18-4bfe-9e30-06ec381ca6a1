```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_stc_kpi_wi` (
  `year` STRING COMMENT '年份，格式：YYYY',
  `week_of_year` BIGINT COMMENT '周数，取值范围：1-53',
  `monday` STRING COMMENT '周一日期，格式：YYYYMMDD',
  `sunday` STRING COMMENT '周日日期，格式：YYYYMMDD',
  `check_sku_cnt` BIGINT COMMENT '抽检数量',
  `in_bound_sku_cnt` BIGINT COMMENT '入库数量',
  `check_rate` DECIMAL(38,18) COMMENT '抽检比例 = 抽检数量/入库数量',
  `back_order_cnt` BIGINT COMMENT '退货总单数',
  `finish_order_cnt` BIGINT COMMENT '已完成单数',
  `back_finish_rate` DECIMAL(38,18) COMMENT '退货完结率 = 已完成单数/退货总单数',
  `damage_amt` DECIMAL(38,18) COMMENT '货损金额（总金额）',
  `damage_amt_wah` DECIMAL(38,18) COMMENT '货损金额——仓配责',
  `sale_amt` DECIMAL(38,18) COMMENT '销售金额',
  `error_sku_cnt` BIGINT COMMENT '错误件数（总件数）',
  `error_sku_cnt_wah` BIGINT COMMENT '错误件数_仓配责',
  `error_cust_cnt` BIGINT COMMENT '错误客户数（总客户数）',
  `error_cust_cnt_wah` BIGINT COMMENT '错误客户数_仓配责',
  `cust_cnt` BIGINT COMMENT '活跃客户数',
  `sku_cnt` BIGINT COMMENT '配送件数',
  `total_point_cnt` BIGINT COMMENT '总点位数（包含喜茶）',
  `point_cnt` BIGINT COMMENT '点位数（不含喜茶）',
  `no_in_time_point_cnt` BIGINT COMMENT '不及时点位数（不含喜茶）',
  `delay_time_point_cnt_2` BIGINT COMMENT '前置2小时不及时点位数（不含喜茶）',
  `out_time` DECIMAL(38,18) COMMENT '配送超时时间（不含喜茶），单位：小时',
  `delay_time_2` DECIMAL(38,18) COMMENT '前置2小时配送超时时间（不含喜茶），单位：小时',
  `path_cnt` BIGINT COMMENT '线路数（不含喜茶）',
  `delay_path_cnt` BIGINT COMMENT '出库不及时线路数（不含喜茶）',
  `out_distance_point_cnt` BIGINT COMMENT '超距离点位数（含喜茶）',
  `after_sale_amt` DECIMAL(38,18) COMMENT '售后总金额',
  `after_sale_amt_wah` DECIMAL(38,18) COMMENT '售后金额__仓配责',
  `after_sale_amt_pur` DECIMAL(38,18) COMMENT '售后金额__采购责',
  `after_sale_amt_che` DECIMAL(38,18) COMMENT '售后金额__品控责',
  `after_sale_amt_pur_che` DECIMAL(38,18) COMMENT '售后金额__采购品控责',
  `after_sale_amt_oth` DECIMAL(38,18) COMMENT '售后金额__其他责',
  `delivery_total_amt` DECIMAL(38,18) COMMENT '配送销售金额',
  `coupon_amt` DECIMAL(38,18) COMMENT '优惠券金额',
  `origin_total_amt` DECIMAL(38,18) COMMENT '应付GMV（Gross Merchandise Volume）',
  `real_total_amt` DECIMAL(38,18) COMMENT '实付GMV（Gross Merchandise Volume）',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储成本',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送成本',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提成本',
  `other_amt` DECIMAL(38,18) COMMENT '其他成本',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨成本',
  `cost_amt` DECIMAL(38,18) COMMENT '商品成本'
) 
COMMENT '仓配KPI汇总表，包含仓储配送相关的关键绩效指标数据，按周维度统计'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：yyyyMMdd') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
               'comment'='仓配KPI汇总表，包含仓储配送相关的关键绩效指标数据，按周维度统计') 
LIFECYCLE 30;
```