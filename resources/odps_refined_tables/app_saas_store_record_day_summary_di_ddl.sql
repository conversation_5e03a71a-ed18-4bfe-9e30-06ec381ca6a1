CREATE TABLE IF NOT EXISTS app_saas_store_record_day_summary_di(
	day_tag STRING COMMENT '日期标签，格式为yyyyMMdd，表示业务日期',
	warehouse_no BIGINT COMMENT '库存仓ID，唯一标识一个仓库',
	warehouse_name STRING COMMENT '仓库名称',
	warehouse_provider STRING COMMENT '仓库服务商，枚举值包括：杭州鲜沐科技有限公司等',
	pd_id BIGINT COMMENT '货品编码，唯一标识一个货品',
	sku STRING COMMENT 'SKU编号，商品库存单位编码',
	saas_sku_id BIGINT COMMENT 'SaaS系统SKU ID，取值范围：100039-126024',
	category_id BIGINT COMMENT '类目ID，取值范围：526-1385',
	sku_tenant_id BIGINT COMMENT 'SKU租户ID，取值范围：2-118',
	warehouse_tenant_id BIGINT COMMENT '仓库租户ID，取值范围：1-109',
	opening_quantity BIGINT COMMENT '期初库存数量',
	opening_amount DECIMAL(38,18) COMMENT '期初库存金额',
	ending_quantity BIGINT COMMENT '期末库存数量',
	ending_amount DECIMAL(38,18) COMMENT '期末库存金额',
	allocation_in_quantity BIGINT COMMENT '调拨入库数量，取值范围：0-8',
	allocation_in_amount DECIMAL(38,18) COMMENT '调拨入库金额',
	purchase_in_quantity BIGINT COMMENT '采购入库数量，取值范围：0-200',
	purchase_in_amount DECIMAL(38,18) COMMENT '采购入库金额',
	after_sale_in_quantity BIGINT COMMENT '退货入库数量',
	after_sale_in_amount DECIMAL(38,18) COMMENT '退货入库金额',
	stock_taking_in_quantity BIGINT COMMENT '盘盈入库数量',
	stock_taking_in_amount DECIMAL(38,18) COMMENT '盘盈入库金额',
	transfer_in_quantity BIGINT COMMENT '转换入库数量',
	transfer_in_amount DECIMAL(38,18) COMMENT '转换入库金额',
	allocation_abnormal_in_quantity BIGINT COMMENT '调拨回库数量',
	allocation_abnormal_in_amount DECIMAL(38,18) COMMENT '调拨回库金额',
	other_in_quantity BIGINT COMMENT '其他入库数量，取值范围：0-777',
	other_in_amount DECIMAL(38,18) COMMENT '其他入库金额',
	in_quantity BIGINT COMMENT '入库合计数量，取值范围：0-777',
	in_amount DECIMAL(38,18) COMMENT '入库合计金额',
	allocation_out_quantity BIGINT COMMENT '调拨出库数量，取值范围：0-30',
	allocation_out_amount DECIMAL(38,18) COMMENT '调拨出库金额',
	sale_out_quantity BIGINT COMMENT '销售出库数量，取值范围：0-250',
	sale_out_amount DECIMAL(38,18) COMMENT '销售出库金额',
	damage_out_quantity BIGINT COMMENT '货损出库数量，取值范围：0-1',
	damage_out_amount DECIMAL(38,18) COMMENT '货损出库金额',
	stock_taking_out_quantity BIGINT COMMENT '盘亏出库数量，取值范围：0-3',
	stock_taking_out_amount DECIMAL(38,18) COMMENT '盘亏出库金额',
	transfer_out_quantity BIGINT COMMENT '转换出库数量',
	transfer_out_amount DECIMAL(38,18) COMMENT '转换出库金额',
	purchase_back_out_quantity BIGINT COMMENT '采购退货出库数量',
	purchase_back_out_amount DECIMAL(38,18) COMMENT '采购退货出库金额',
	supply_again_out_quantity BIGINT COMMENT '补货出库数量',
	supply_again_out_amount DECIMAL(38,18) COMMENT '补货出库金额',
	own_self_out_quantity BIGINT COMMENT '自提销售出库数量，取值范围：0-3',
	own_self_out_amount DECIMAL(38,18) COMMENT '自提销售出库金额',
	other_out_quantity BIGINT COMMENT '其他出库数量',
	other_out_amount DECIMAL(38,18) COMMENT '其他出库金额',
	out_quantity BIGINT COMMENT '出库合计数量，取值范围：0-250',
	out_amount DECIMAL(38,18) COMMENT '出库合计金额',
	init_in_quantity BIGINT COMMENT '期初入库数量',
	init_in_amount DECIMAL(38,18) COMMENT '期初入库金额',
	lack_in_quantity BIGINT COMMENT '缺货入库数量',
	lack_in_amount DECIMAL(38,18) COMMENT '缺货入库金额',
	intercept_in_quantity BIGINT COMMENT '拦截入库数量',
	intercept_in_amount DECIMAL(38,18) COMMENT '拦截入库金额',
	out_more_in_quantity BIGINT COMMENT '多出入库数量',
	out_more_in_amount DECIMAL(38,18) COMMENT '多出入库金额',
	allocation_damage_out_quantity BIGINT COMMENT '调拨货损出库数量',
	allocation_damage_out_amount DECIMAL(38,18) COMMENT '调拨货损出库金额'
) 
COMMENT '出入库汇总天表（含自营仓），记录每日各仓库SKU的出入库明细和汇总信息'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='出入库业务汇总表，包含期初期末库存、各类出入库数量金额等核心业务指标') 
LIFECYCLE 30;