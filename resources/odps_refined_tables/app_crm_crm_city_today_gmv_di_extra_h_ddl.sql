CREATE TABLE IF NOT EXISTS app_crm_crm_city_today_gmv_di_extra_h(
	city STRING COMMENT '城市名称，如：上海市、东莞市等',
	area STRING COMMENT '区域名称，如：宝山区、崇明区、杨浦区等，无区域时显示"无"',
	order_gmv DECIMAL(38,18) COMMENT '下单总GMV（总交易额），单位：元',
	order_merchant BIGINT COMMENT '下单客户数，取值范围：0-29，均值3.08，标准差4.54',
	fruit_gmv DECIMAL(38,18) COMMENT '鲜果类商品GMV，单位：元',
	dairy_gmv DECIMAL(38,18) COMMENT '乳制品类商品GMV，单位：元',
	non_dairy_gmv DECIMAL(38,18) COMMENT '非乳制品类商品GMV，单位：元',
	brand_gmv DECIMAL(38,18) COMMENT '自营品牌商品GMV，单位：元',
	agent_gmv DECIMAL(38,18) COMMENT '代售商品GMV，单位：元',
	reward_gmv DECIMAL(38,18) COMMENT '固定奖励SKU的GMV，单位：元',
	pull_new_amount BIGINT COMMENT '拉新客户数量，取值范围：0-1，均值0.007，标准差0.083',
	visit_num BIGINT COMMENT '拜访次数，取值范围：0-3201，均值284.6，标准差441.8',
	delivery_gmv DECIMAL(38,18) COMMENT '配送服务GMV，单位：元'
) 
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='城市当日GMV明细表，按城市和区域统计各类商品的交易额、客户数、拉新数、拜访数等业务指标') 
LIFECYCLE 30;