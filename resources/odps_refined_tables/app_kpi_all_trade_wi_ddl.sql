CREATE TABLE IF NOT EXISTS app_kpi_all_trade_wi(
	`year` STRING COMMENT '年份，格式：yyyy',
	`week_of_year` BIGINT COMMENT '周数，取值范围：1-53',
	`monday` STRING COMMENT '周一日期，格式：yyyyMMdd（年月日）',
	`sunday` STRING COMMENT '周日日期，格式：yyyyMMdd（年月日）',
	`manage_type` STRING COMMENT '业务线类型，枚举值：自营、代仓、代售、批发、SAAS鲜沐自营、SAAS鲜沐代仓、SAAS品牌方自营',
	`origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额，单位：元',
	`real_total_amt` DECIMAL(38,18) COMMENT '实付总金额，单位：元',
	`cust_cnt` BIGINT COMMENT '客户数，取值范围：0-17175',
	`order_cnt` BIGINT COMMENT '订单数，取值范围：40-24557',
	`tenant_cnt` BIGINT COMMENT '租户数（仅SAAS业务线有值），取值范围：0-28',
	`delivery_amt` DECIMAL(38,18) COMMENT '运费（运费+超时加单费），单位：元',
	`cust_arpu` DECIMAL(38,18) COMMENT 'ARPU值（应付总金额/客户数），单位：元/客户',
	`order_avg` DECIMAL(38,18) COMMENT '订单均价（应付总金额/订单数），单位：元/订单',
	`after_sale_noreceived_order_cnt` BIGINT COMMENT '未到货售后订单数，取值范围：0-786',
	`after_sale_noreceived_amt` DECIMAL(38,18) COMMENT '未到货售后总金额，单位：元',
	`after_sale_rate` DECIMAL(38,18) COMMENT '退货率（未到货售后总金额/应付总金额），取值范围：0-1'
) 
COMMENT '交易口径业务线KPI指标周汇总表，包含各业务线每周的交易数据、客户指标和售后指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：yyyyMMdd（年月日）')
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='交易口径各业务线周度KPI指标汇总表，用于业务线经营分析和监控') 
LIFECYCLE 30;