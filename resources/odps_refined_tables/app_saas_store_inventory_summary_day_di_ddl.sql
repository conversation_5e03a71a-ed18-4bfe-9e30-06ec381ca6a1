CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_saas_store_inventory_summary_day_di` (
  `summary_date` DATETIME COMMENT '汇总日期，格式为年月日时分秒（yyyy-MM-dd HH:mm:ss）',
  `tenant_id` BIGINT COMMENT '租户id，取值范围：2-100',
  `store_id` BIGINT COMMENT '门店id，取值范围：4-539049',
  `item_id` BIGINT COMMENT '商品sku id，取值范围：1-44829',
  `specification` STRING COMMENT '商品sku规格，如：1箱*12盒、1包*100个等',
  `main_picture` STRING COMMENT '商品主图URL地址',
  `title` STRING COMMENT '商品sku标题名称',
  `store_inventory_unit` STRING COMMENT '商品库存单位，如：个、瓶、盒、箱等',
  `initial_quantity` DECIMAL(38,18) COMMENT '期初库存数量',
  `initial_amount` DECIMAL(38,18) COMMENT '期初库存金额',
  `initial_unit_price` DECIMAL(38,18) COMMENT '期初库存单位单价',
  `other_in_quantity` DECIMAL(38,18) COMMENT '其他入库数量',
  `other_in_amount` DECIMAL(38,18) COMMENT '其他入库金额',
  `order_in_quantity` DECIMAL(38,18) COMMENT '订货入库数量',
  `order_in_amount` DECIMAL(38,18) COMMENT '订货入库金额',
  `stock_gain_in_quantity` DECIMAL(38,18) COMMENT '盘盈入库数量',
  `stock_gain_in_amount` DECIMAL(38,18) COMMENT '盘盈入库金额',
  `reship_in_quantity` DECIMAL(38,18) COMMENT '补发入库数量',
  `reship_in_amount` DECIMAL(38,18) COMMENT '补发入库金额',
  `total_in_quantity` DECIMAL(38,18) COMMENT '入库合计数量',
  `total_in_amount` DECIMAL(38,18) COMMENT '入库合计金额',
  `other_out_quantity` DECIMAL(38,18) COMMENT '其他出库数量',
  `other_out_amount` DECIMAL(38,18) COMMENT '其他出库金额',
  `sales_out_quantity` DECIMAL(38,18) COMMENT '销售出库数量',
  `sales_out_amount` DECIMAL(38,18) COMMENT '销售出库金额',
  `stock_loss_out_quantity` DECIMAL(38,18) COMMENT '盘亏出库数量',
  `stock_loss_out_amount` DECIMAL(38,18) COMMENT '盘亏出库金额',
  `return_out_quantity` DECIMAL(38,18) COMMENT '退货出库数量',
  `out_loss_amount` DECIMAL(38,18) COMMENT '报损出库金额',
  `out_loss_quantity` DECIMAL(38,18) COMMENT '报损出库数量',
  `return_out_amount` DECIMAL(38,18) COMMENT '退货出库金额',
  `total_out_quantity` DECIMAL(38,18) COMMENT '出库合计数量',
  `total_out_amount` DECIMAL(38,18) COMMENT '出库合计金额',
  `final_quantity` DECIMAL(38,18) COMMENT '期末库存数量',
  `final_amount` DECIMAL(38,18) COMMENT '期末库存金额',
  `final_unit_price` DECIMAL(38,18) COMMENT '期末库存单位单价'
) 
COMMENT '门店出入库汇总日表，记录各门店商品每日的期初库存、入库出库明细、期末库存等库存变动汇总信息'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为年月日（yyyyMMdd）'
)
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='门店库存日汇总表，用于分析门店商品库存变动情况') 
LIFECYCLE 30;