```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_pop_biaoguo_products_df` (
  `category_name` STRING COMMENT '完整类目名称，如：水果-自动化_火龙果_进口红心',
  `category1` STRING COMMENT '一级类目，如：水果-自动化',
  `category2` STRING COMMENT '二级类目，如：火龙果',
  `category3` STRING COMMENT '三级类目，如：进口红心',
  `back_category_name` STRING COMMENT '后台类目名称，如：进口红心',
  `id` STRING COMMENT '商品唯一标识',
  `competitor` STRING COMMENT '竞争对手，枚举值：标果-杭州',
  `sku_code` STRING COMMENT 'SKU编码',
  `goods_name` STRING COMMENT '商品名称',
  `baby_name` STRING COMMENT '商品详情的描述',
  `standard_price` STRING COMMENT '标准价格（单位：元）',
  `final_standard_price` STRING COMMENT '最终标准价格（单位：元）',
  `last_time_standard_price` STRING COMMENT '上次标准价格（单位：元）',
  `final_unit_price_catty` STRING COMMENT '最终市斤价格（单位：元/市斤）',
  `unit_price_catty` STRING COMMENT '单位市斤价格（单位：元/市斤）',
  `goods_type` STRING COMMENT '商品类型，枚举值：ORDINARY-普通商品',
  `specification` STRING COMMENT '规格，如：约31.5斤',
  `unit` STRING COMMENT '单位，枚举值：PIECE-件',
  `gross_weight` STRING COMMENT '毛重（单位：斤）',
  `net_weight` STRING COMMENT '净重（单位：斤）',
  `month_sale` STRING COMMENT '月销量',
  `goods_siphon_commission_rate` STRING COMMENT '商品吸客佣金比例（单位：%）',
  `seller_siphon_commission_rate` STRING COMMENT '卖家吸客佣金比例（单位：%）',
  `seller_name` STRING COMMENT '卖家名称，如：公孙策',
  `goods_prop_detail_list` STRING COMMENT '商品属性详情列表，JSON格式字符串',
  `url` STRING COMMENT '商品链接URL',
  `seven_day_after_sale` STRING COMMENT '七天售后服务标识',
  `spider_fetch_time` STRING COMMENT '爬虫抓取时间，格式：yyyy-MM-dd HH:mm:ss',
  `create_time` STRING COMMENT '商品创建时间(来自标果)，格式：yyyy-MM-dd HH:mm:ss'
)
COMMENT 'POP标果商品详细信息明细表，包含商品基础信息、价格信息、销售信息和属性详情'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '数据日期分区，格式：yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = 'POP标果商品详细信息明细表，包含商品基础信息、价格信息、销售信息和属性详情',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 180;
```