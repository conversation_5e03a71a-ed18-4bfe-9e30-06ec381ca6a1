```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_sale_large_area_kpi_trade_mi` (
  `month` STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
  `large_area_name` STRING COMMENT '运营服务大区名称，枚举值包括：广州大区、武汉大区、贵阳大区、苏南大区、广东一点点快递区域等',
  `order_origin_total_amt` DECIMAL(38,18) COMMENT '交易应付总金额（元），订单原始应付金额汇总',
  `order_real_total_amt` DECIMAL(38,18) COMMENT '交易实付总金额（元），订单实际支付金额汇总',
  `order_cust_cnt` BIGINT COMMENT '交易客户数，参与交易的独立客户数量',
  `order_cust_arpu` DECIMAL(38,18) COMMENT 'ARPU值（元/客户），计算公式：交易应付总金额/交易客户数',
  `order_cnt` BIGINT COMMENT '交易订单数，总订单数量',
  `delivery_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额（元），履约相关原始应付金额汇总',
  `delivery_real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额（元），履约实际支付金额汇总',
  `delivery_cust_cnt` BIGINT COMMENT '履约活跃客户数，参与履约的独立客户数量',
  `delivery_origin_profit` DECIMAL(38,18) COMMENT '履约应付毛利润（元），基于应付金额计算的毛利润',
  `delivery_real_profit` DECIMAL(38,18) COMMENT '履约实付毛利润（元），基于实付金额计算的毛利润',
  `delivery_after_profit` DECIMAL(38,18) COMMENT '履约后毛利润（元），履约完成后的最终毛利润',
  `delivery_days_avg` DECIMAL(38,18) COMMENT '履约频次，平均履约天数',
  `delivery_point_cnt` BIGINT COMMENT '履约点位数，参与履约的网点或点位数量',
  `delivery_amt` DECIMAL(38,18) COMMENT '自营履约费用（元），自营业务的履约相关费用',
  `new_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '新客履约应付总金额（元），新客户履约原始应付金额',
  `new_delivery_real_total_amt` DECIMAL(38,18) COMMENT '新客履约实付总金额（元），新客户履约实际支付金额',
  `new_delivery_cust_cnt` BIGINT COMMENT '新客履约活跃客户数，新客户中参与履约的客户数量',
  `new_delivery_real_profit` DECIMAL(38,18) COMMENT '新客履约实付毛利润（元），新客户履约基于实付金额的毛利润',
  `old_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '老客履约应付总金额（元），老客户履约原始应付金额',
  `old_delivery_real_total_amt` DECIMAL(38,18) COMMENT '老客履约实付总金额（元），老客户履约实际支付金额',
  `old_delivery_cust_cnt` BIGINT COMMENT '老客履约活跃客户数，老客户中参与履约的客户数量',
  `old_delivery_real_profit` DECIMAL(38,18) COMMENT '老客履约实付毛利润（元），老客户履约基于实付金额的毛利润',
  `order_sku_cnt` BIGINT COMMENT '交易SKU款数，交易涉及的商品SKU种类数量',
  `order_sku_weight` DECIMAL(38,18) COMMENT '交易SKU总重量（KG），交易商品的总重量',
  `delivery_sku_cnt` BIGINT COMMENT '履约SKU款数，履约涉及的商品SKU种类数量',
  `delivery_sku_weight` DECIMAL(38,18) COMMENT '履约SKU总重量（KG），履约商品的总重量'
) 
COMMENT '销售KPI指标汇总表，按大区统计交易和履约相关的关键绩效指标，包括金额、客户数、订单数、利润等维度'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，如20250917表示2025年9月17日'
)
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
               'comment'='销售大区KPI交易指标汇总表，用于大区销售绩效分析和监控') 
LIFECYCLE 30;
```