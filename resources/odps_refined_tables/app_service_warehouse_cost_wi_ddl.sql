CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_service_warehouse_cost_wi` (
  `year` STRING COMMENT '年份，格式：YYYY',
  `week_of_year` STRING COMMENT '周数，格式：1-53',
  `monday` STRING COMMENT '周一日期，格式：YYYYMMDD',
  `sunday` STRING COMMENT '周日日期，格式：YYYYMMDD',
  `service_area` STRING COMMENT '服务区域，取值范围：华中、华西、华东、广西、None（未知）',
  `warehouse_no` BIGINT COMMENT '库存仓编号，取值范围：1-155',
  `warehouse_name` STRING COMMENT '库存仓名称',
  `total_storage_amt` DECIMAL(38,18) COMMENT '总仓储费用（元）',
  `self_storage_amt` DECIMAL(38,18) COMMENT '自营总仓储费用（元）',
  `heytea_storage_amt` DECIMAL(38,18) COMMENT '喜茶总仓储费用（元）',
  `warehouse_fixed_amt` DECIMAL(38,18) COMMENT '仓储固资折旧费用（元）',
  `total_out_sku_cnt` BIGINT COMMENT '总出库总件数，取值范围：0-114344',
  `self_out_sku_cnt` BIGINT COMMENT '自营出库总件数，取值范围：0-93441',
  `heytea_out_sku_cnt` BIGINT COMMENT '喜茶出库总件数，取值范围：0',
  `hdl_out_sku_cnt` BIGINT COMMENT '海底捞出库总件数，取值范围：0',
  `hdl_out_sku_wei_cnt` DECIMAL(38,18) COMMENT '海底捞出库总件数（重量折算），取值范围：0',
  `product_loss_amt` DECIMAL(38,18) COMMENT '产品损耗费用（元）',
  `self_person_amt` DECIMAL(38,18) COMMENT '自营人工费用（元）',
  `heytea_person_amt` DECIMAL(38,18) COMMENT '喜茶人工费用（元）',
  `self_rental_amt` DECIMAL(38,18) COMMENT '自营租赁费用（元）',
  `heytea_rental_amt` DECIMAL(38,18) COMMENT '喜茶租赁费用（元）',
  `self_handling_amt` DECIMAL(38,18) COMMENT '自营装卸费用（元）',
  `heytea_handling_amt` DECIMAL(38,18) COMMENT '喜茶装卸费用（元）',
  `self_consumable_amt` DECIMAL(38,18) COMMENT '自营耗材费用（元）',
  `heytea_consumable_amt` DECIMAL(38,18) COMMENT '喜茶耗材费用（元）',
  `self_office_amt` DECIMAL(38,18) COMMENT '自营办公用品费用（元）',
  `heytea_office_amt` DECIMAL(38,18) COMMENT '喜茶办公用品费用（元）',
  `self_utilities_amt` DECIMAL(38,18) COMMENT '自营水电费用（元）',
  `heytea_utilities_amt` DECIMAL(38,18) COMMENT '喜茶水电费用（元）',
  `self_equipment_amt` DECIMAL(38,18) COMMENT '自营设备租赁费用（元）',
  `heytea_equipment_amt` DECIMAL(38,18) COMMENT '喜茶设备租赁费用（元）',
  `self_other_amt` DECIMAL(38,18) COMMENT '自营其他费用（元）',
  `heytea_other_amt` DECIMAL(38,18) COMMENT '喜茶其他费用（元）'
) 
COMMENT '区域库存仓仓储费用明细表，按周统计各库存仓的仓储费用明细，包含自营和喜茶品牌的各项费用明细'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，数据日期，格式：YYYYMMDD'
)
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='区域库存仓仓储费用明细表，按周粒度统计各库存仓的仓储运营成本') 
LIFECYCLE 30;