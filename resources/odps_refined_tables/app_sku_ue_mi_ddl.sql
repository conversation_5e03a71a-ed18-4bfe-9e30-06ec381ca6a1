CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_sku_ue_mi` (
  `month` STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
  `sku_id` STRING COMMENT 'SKU编号，商品唯一标识',
  `sku_type` STRING COMMENT '商品类型：自营-平台自营商品，代售-第三方商家商品，代仓-第三方仓库代发商品',
  `category_1` STRING COMMENT '一级类目：乳制品、鲜果、其他等',
  `spu_name` STRING COMMENT '商品名称',
  `sku_disc` STRING COMMENT '商品规格描述',
  `origin_total_amt` DECIMAL(38,18) COMMENT '配送应付总金额（元）',
  `real_total_amt` DECIMAL(38,18) COMMENT '配送实付总金额（元）',
  `cost_amt` DECIMAL(38,18) COMMENT '总成本金额（元）',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费金额（元）',
  `damage_amt` DECIMAL(38,18) COMMENT '货损赔偿金额（元）',
  `inventory_profit_amt` DECIMAL(38,18) COMMENT '盘盈总金额（元）',
  `inventory_loss_amt` DECIMAL(38,18) COMMENT '盘亏总金额（元）',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储成本金额（元）',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '干线运输成本金额（元）',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送成本金额（元）',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨成本金额（元）',
  `self_picked_amt` DECIMAL(38,18) COMMENT '自提成本金额（元）',
  `other_amt` DECIMAL(38,18) COMMENT '其他成本金额（元）',
  `after_sale_amt` DECIMAL(38,18) COMMENT '售后相关金额（元）'
)
COMMENT 'UE监控SKU商品维度表，用于监控商品级别的用户体验相关财务指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = 'UE监控SKU商品维度表，包含商品基础信息和各类成本费用明细',
  'lifecycle' = '30'
);