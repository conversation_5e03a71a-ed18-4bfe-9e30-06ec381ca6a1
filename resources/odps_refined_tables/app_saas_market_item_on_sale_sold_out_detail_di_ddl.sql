```sql
CREATE TABLE IF NOT EXISTS app_saas_market_item_on_sale_sold_out_detail_di(
    tenant_id BIGINT COMMENT '租户ID，取值范围：2-108',
    time_tag STRING COMMENT '时间标签，格式：yyyyMMdd（年月日）',
    item_id BIGINT COMMENT '商品ID，取值范围：1-44901',
    sale_price DECIMAL(38,18) COMMENT '商品售价，单位：元',
    sold_out_time BIGINT COMMENT '日累计售罄时长，单位：秒，取值范围：0-86400（0秒到24小时）',
    on_sale_time BIGINT COMMENT '日累计上架时长，单位：秒，取值范围：73-86400（约1分钟到24小时）'
)
COMMENT 'SaaS商品上架售罄汇总表，统计各商品每日的上架和售罄时长情况'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd（年月日）') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='SaaS商品上架售罄汇总表，用于分析商品销售状态和库存周转情况') 
LIFECYCLE 30;
```