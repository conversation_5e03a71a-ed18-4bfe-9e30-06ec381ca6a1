CREATE TABLE IF NOT EXISTS app_saas_brand_sku_trade_wi(
	year STRING COMMENT '年份，格式：YYYY',
	week_of_year BIGINT COMMENT '周数，取值范围：1-53',
	monday STRING COMMENT '周一日期，格式：YYYYMMDD，年月日',
	sunday STRING COMMENT '周日日期，格式：YYYYMMDD，年月日',
	brand_alias STRING COMMENT '品牌名称',
	title STRING COMMENT '商品标题',
	specification STRING COMMENT '商品规格',
	category1 STRING COMMENT '后台一级类目',
	sku_type STRING COMMENT '商品类型，枚举值：商城下单、鲜沐自营、代仓、代售、客户自营',
	total_gmv DECIMAL(38,18) COMMENT '总交易GMV',
	sku_cnt BIGINT COMMENT '商品销量'
) 
COMMENT 'SaaS品牌品类结构数据表，包含品牌商品的交易数据和品类信息'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：YYYYMMDD，年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='SaaS品牌品类结构数据表，用于分析品牌商品的交易趋势和品类分布') 
LIFECYCLE 30;