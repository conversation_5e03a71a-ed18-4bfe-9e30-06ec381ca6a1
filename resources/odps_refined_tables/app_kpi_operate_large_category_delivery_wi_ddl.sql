```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_kpi_operate_large_category_delivery_wi` (
  `year` STRING COMMENT '年份，格式：YYYY',
  `week_of_year` STRING COMMENT '周数，格式：1-53',
  `monday` STRING COMMENT '周一日期，格式：YYYYMMDD',
  `sunday` STRING COMMENT '周日日期，格式：YYYYMMDD',
  `large_area_name` STRING COMMENT '运营服务大区名称',
  `category` STRING COMMENT '商品品类：鲜果，乳制品，其他',
  `origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV，原始总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '履约实付GMV，实际支付总金额',
  `marketing_amt` DECIMAL(38,18) COMMENT '营销费用金额',
  `cost_amt` DECIMAL(38,18) COMMENT '成本金额',
  `origin_gross` DECIMAL(38,18) COMMENT '应付毛利润',
  `real_gross` DECIMAL(38,18) COMMENT '实付毛利润',
  `origin_gross_margin` DECIMAL(38,18) COMMENT '应付毛利率',
  `real_gross_margin` DECIMAL(38,18) COMMENT '实付毛利率',
  `cust_cnt` BIGINT COMMENT '履约客户数，范围：1-2632',
  `point_cnt` BIGINT COMMENT '点位数，范围：1-3448',
  `origin_pre_cust_price` DECIMAL(38,18) COMMENT '应付客单价',
  `real_pre_cust_price` DECIMAL(38,18) COMMENT '实付客单价',
  `timing_origin_amt` DECIMAL(38,18) COMMENT '省心送应付GMV',
  `timing_real_amt` DECIMAL(38,18) COMMENT '省心送实付GMV',
  `consign_origin_amt` DECIMAL(38,18) COMMENT '代售应付GMV',
  `consign_real_amt` DECIMAL(38,18) COMMENT '代售实付GMV',
  `consign_marketing_amt` DECIMAL(38,18) COMMENT '代售营销费用',
  `consign_origin_gross` DECIMAL(38,18) COMMENT '代售应付毛利润',
  `consign_real_gross` DECIMAL(38,18) COMMENT '代售实付毛利润',
  `consign_cust_cnt` BIGINT COMMENT '代售履约客户数，范围：0（无代售业务）',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储费',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '单点干线费',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提费',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨费',
  `other_amt` DECIMAL(38,18) COMMENT '其他费用',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送费'
) 
COMMENT '运营履约KPI表（平台客户），包含各品类在各运营大区的周度履约数据，涵盖GMV、毛利率、客户数、费用等核心指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：YYYYMMDD，表示数据日期'
)
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
               'comment'='运营履约KPI周度汇总表，用于分析平台客户在各品类和各运营大区的履约表现') 
LIFECYCLE 30;
```