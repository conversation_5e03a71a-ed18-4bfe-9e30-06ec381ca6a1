CREATE TABLE IF NOT EXISTS app_crm_team_bd_spu_wi(
	`year` STRING COMMENT '年份，格式：YYYY',
	`week_of_year` BIGINT COMMENT '周数，一年中的第几周',
	`monday` STRING COMMENT '周一日期，格式：YYYYMMDD',
	`sunday` STRING COMMENT '周日日期，格式：YYYYMMDD',
	`cust_team` STRING COMMENT '客户团队类型，取值范围：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户',
	`brand_alias` STRING COMMENT '品牌别称',
	`register_province` STRING COMMENT '注册时省份',
	`register_city` STRING COMMENT '注册时城市',
	`brand_id` BIGINT COMMENT '公司ID',
	`brand_name` STRING COMMENT '公司名称',
	`bd_id` BIGINT COMMENT '销售ID',
	`bd_name` STRING COMMENT '销售名称',
	`spu_no` STRING COMMENT 'SPU编号',
	`spu_name` STRING COMMENT '商品名称',
	`sku_spec` STRING COMMENT '规格',
	`sku_brand` STRING COMMENT '商品品牌',
	`sku_variety` STRING COMMENT '品种',
	`category_1` STRING COMMENT '一级类目',
	`category_2` STRING COMMENT '二级类目',
	`category_3` STRING COMMENT '三级类目',
	`category_4` STRING COMMENT '四级类目',
	`sku_type` BIGINT COMMENT '商品类型，取值范围：0-自营，1-代仓，2-其他',
	`is_self_owned_brand` BIGINT COMMENT '是否自营品牌，取值范围：1-是，0-否',
	`origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额',
	`real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额',
	`cost_amt` DECIMAL(38,18) COMMENT '履约成本',
	`sku_cnt` BIGINT COMMENT '总配送件数',
	`cust_cnt` BIGINT COMMENT '履约门店数',
	`after_sale_amt` DECIMAL(38,18) COMMENT '售后金额'
) 
COMMENT '大客户团队商品SPU与客户名称与BD结合维度汇总表，按周统计大客户团队的销售数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='大客户团队商品SPU维度汇总表，包含销售、客户、商品等多维度数据') 
LIFECYCLE 30;