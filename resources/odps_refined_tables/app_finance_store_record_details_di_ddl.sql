```sql
CREATE TABLE IF NOT EXISTS app_finance_store_record_details_di(
    `service_area` STRING COMMENT '大区，如：华东、华北等',
    `warehouse_no` BIGINT COMMENT '库存仓ID，数值型标识',
    `warehouse_name` STRING COMMENT '库存仓名称',
    `type` STRING COMMENT '出入库类型，枚举值：货损出库、销售出库等',
    `sku` STRING COMMENT 'SKU编码，商品唯一标识',
    `pd_name` STRING COMMENT '商品名称',
    `sku_disc` STRING COMMENT '商品规格描述',
    `tax_rate` DECIMAL(38,18) COMMENT '税率，小数表示',
    `batch` STRING COMMENT '批次号，生产批次标识',
    `production_date` DATETIME COMMENT '生产日期，年月日时分秒格式',
    `quality_date` DATETIME COMMENT '保质期截止日期，年月日时分秒格式',
    `supplier` STRING COMMENT '供应商名称',
    `quantity` BIGINT COMMENT '出入库数量，正数表示入库，负数表示出库',
    `cost` DECIMAL(38,18) COMMENT '单件成本金额',
    `cost_amt` DECIMAL(38,18) COMMENT '总成本金额 = 数量 × 单件成本',
    `record_time` DATETIME COMMENT '出入库操作时间，年月日时分秒格式',
    `store_quantity` BIGINT COMMENT '出入库后库存数量',
    `date_flag` STRING COMMENT '日期标识，yyyyMMdd格式',
    `category1` STRING COMMENT '商品一级类目，如：鲜果、乳制品、其他等',
    `cost_amt_notax` DECIMAL(38,18) COMMENT '总成本金额（不含税）',
    `sub_type` BIGINT COMMENT '业务子类型：1-自营代销不入仓、2-自营代销入仓、3-自营经销',
    `settle_type` STRING COMMENT '结算类型',
    `supplier_id` BIGINT COMMENT '供应商ID，数值型标识'
)
COMMENT '财务口径期末库存表，记录商品出入库明细及库存变化情况'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，yyyyMMdd格式日期'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = '财务口径期末库存明细表，包含商品出入库记录、库存变化、成本核算等信息',
    'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```