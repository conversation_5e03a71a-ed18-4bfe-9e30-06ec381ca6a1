CREATE TABLE IF NOT EXISTS app_log_mall_cms_di(
    `date` STRING COMMENT '日期，格式为yyyyMMdd，表示年月日',
    `cust_type` STRING COMMENT '客户行业类型，枚举值：面包蛋糕、西餐、咖啡、甜品冰淇淋、茶饮、其他',
    `large_area_id` BIGINT COMMENT '运营服务大区ID，取值范围1-91',
    `large_area_name` STRING COMMENT '运营服务大区名称',
    `cms_page_id` STRING COMMENT '活动页ID',
    `cms_page_name` STRING COMMENT '活动页名称',
    `enter_pv` BIGINT COMMENT '进入页面浏览量(PV)，取值范围1-572',
    `enter_uv` BIGINT COMMENT '进入页面独立访客数(UV)，取值范围1-30',
    `sku_click_pv` BIGINT COMMENT '商品点击浏览量(PV)，取值范围0-55',
    `sku_click_uv` BIGINT COMMENT '商品点击独立访客数(UV)，取值范围0-11',
    `sku_cart_buy_pv` BIGINT COMMENT '商品加购浏览量(PV)，取值范围0-7',
    `sku_cart_buy_uv` BIGINT COMMENT '商品加购独立访客数(UV)，取值范围0-6',
    `sku_instant_buy_pv` BIGINT COMMENT '商品立即购买浏览量(PV)，取值范围0-4',
    `sku_instant_buy_uv` BIGINT COMMENT '商品立即购买独立访客数(UV)，取值范围0-1',
    `sku_order_order_cnt` BIGINT COMMENT '购买订单次数，取值范围0-4',
    `sku_order_cust_cnt` BIGINT COMMENT '购买客户人数，取值范围0-4'
)
COMMENT '乐高活动页页流量分析表，用于分析活动页的用户行为数据，包括页面访问、商品点击、加购、购买等关键指标'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日'
)
STORED AS ALIORC
TBLPROPERTIES ('comment'='乐高活动页页流量分析表，包含活动页的用户访问、商品交互和购买行为数据')
LIFECYCLE 30;