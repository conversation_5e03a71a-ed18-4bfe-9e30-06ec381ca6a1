```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_log_largearea_home_page_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
  `largearea_no` STRING COMMENT '运营大区ID，唯一标识运营大区的编号',
  `largearea_name` STRING COMMENT '运营大区名称，如：杭州大区',
  `cust_type` STRING COMMENT '客户业态，取值范围：咖啡、烘焙、其他等，表示客户的经营类型',
  `home_page_uv` BIGINT COMMENT '首页独立访客数',
  `home_page_pv` BIGINT COMMENT '首页页面浏览量',
  `search_uv` BIGINT COMMENT '搜索功能独立使用用户数',
  `search_pv` BIGINT COMMENT '搜索功能使用次数',
  `classification_uv` BIGINT COMMENT '分类功能独立使用用户数',
  `classification_pv` BIGINT COMMENT '分类功能使用次数',
  `banner_uv` BIGINT COMMENT 'Banner区域独立访客数',
  `banner_pv` BIGINT COMMENT 'Banner区域点击量',
  `waist_banner_uv` BIGINT COMMENT '腰部Banner独立访客数',
  `waist_banner_pv` BIGINT COMMENT '腰部Banner点击量',
  `carousel_banner_uv` BIGINT COMMENT '轮播Banner独立访客数',
  `carousel_banner_pv` BIGINT COMMENT '轮播Banner点击量',
  `activity_uv` BIGINT COMMENT '活动专区独立访客数',
  `activity_pv` BIGINT COMMENT '活动专区页面浏览量',
  `fresh_artners_uv` BIGINT COMMENT '鲜拍档模块独立访客数',
  `fresh_artners_pv` BIGINT COMMENT '鲜拍档模块页面浏览量',
  `special_uv` BIGINT COMMENT '特价活动独立访客数',
  `special_pv` BIGINT COMMENT '特价活动页面浏览量',
  `temporary_uv` BIGINT COMMENT '临保商品独立访客数',
  `temporary_pv` BIGINT COMMENT '临保商品页面浏览量',
  `boutique_uv` BIGINT COMMENT '精品优选独立访客数',
  `boutique_pv` BIGINT COMMENT '精品优选页面浏览量',
  `common_recommend_uv` BIGINT COMMENT '常用商品推荐独立访客数',
  `common_recommend_pv` BIGINT COMMENT '常用商品推荐页面浏览量',
  `recommend_uv` BIGINT COMMENT '商品推荐独立访客数',
  `recommend_pv` BIGINT COMMENT '商品推荐页面浏览量',
  `cust_flag` STRING COMMENT '是否老客标识，取值范围：是、否',
  `bottom_nav_pv` BIGINT COMMENT '底部导航栏总页面浏览量',
  `bottom_nav_uv` BIGINT COMMENT '底部导航栏总独立访客数',
  `category_pv` BIGINT COMMENT '底部导航栏-分类页面浏览量',
  `procurement_assistant_pv` BIGINT COMMENT '底部导航栏-采购助手页面浏览量',
  `shopping_cart_pv` BIGINT COMMENT '底部导航栏-购物车页面浏览量',
  `personal_center_pv` BIGINT COMMENT '底部导航栏-个人中心页面浏览量',
  `category_uv` BIGINT COMMENT '底部导航栏-分类独立访客数',
  `procurement_assistant_uv` BIGINT COMMENT '底部导航栏-采购助手独立访客数',
  `shopping_cart_uv` BIGINT COMMENT '底部导航栏-购物车独立访客数',
  `personal_center_uv` BIGINT COMMENT '底部导航栏-个人中心独立访客数',
  `category_tankeng_pv` BIGINT COMMENT '分类弹坑总页面浏览量',
  `category_tankeng_uv` BIGINT COMMENT '分类弹坑总独立访客数',
  `fresh_fruit_pv` BIGINT COMMENT '分类弹坑-鲜果页面浏览量',
  `wangyou_tavern_pv` BIGINT COMMENT '分类弹坑-忘忧酒馆页面浏览量',
  `dairy_products_pv` BIGINT COMMENT '分类弹坑-乳制品页面浏览量',
  `coffee_pv` BIGINT COMMENT '分类弹坑-咖啡页面浏览量',
  `baking_supplies_pv` BIGINT COMMENT '分类弹坑-烘培辅料页面浏览量',
  `frozen_cakes_pv` BIGINT COMMENT '分类弹坑-冷冻蛋糕页面浏览量',
  `bar_supplies_pv` BIGINT COMMENT '分类弹坑-水吧辅料页面浏览量',
  `sugar_syrup_pv` BIGINT COMMENT '分类弹坑-糖|糖浆页面浏览量',
  `western_ingredients_pv` BIGINT COMMENT '分类弹坑-西餐辅料页面浏览量',
  `packaging_materials_pv` BIGINT COMMENT '分类弹坑-包材页面浏览量',
  `fresh_fruit_uv` BIGINT COMMENT '分类弹坑-鲜果独立访客数',
  `wangyou_tavern_uv` BIGINT COMMENT '分类弹坑-忘忧酒馆独立访客数',
  `dairy_products_uv` BIGINT COMMENT '分类弹坑-乳制品独立访客数',
  `coffee_uv` BIGINT COMMENT '分类弹坑-咖啡独立访客数',
  `baking_supplies_uv` BIGINT COMMENT '分类弹坑-烘培辅料独立访客数',
  `frozen_cakes_uv` BIGINT COMMENT '分类弹坑-冷冻蛋糕独立访客数',
  `bar_supplies_uv` BIGINT COMMENT '分类弹坑-水吧辅料独立访客数',
  `sugar_syrup_uv` BIGINT COMMENT '分类弹坑-糖|糖浆独立访客数',
  `western_ingredients_uv` BIGINT COMMENT '分类弹坑-西餐辅料独立访客数',
  `packaging_materials_uv` BIGINT COMMENT '分类弹坑-包材独立访客数',
  `recipe_market_pv` BIGINT COMMENT '配方集市页面浏览量',
  `recipe_market_uv` BIGINT COMMENT '配方集市独立访客数',
  `home_page_ai_assistant_pv` BIGINT COMMENT '首页AI助手页面浏览量',
  `home_page_ai_assistant_uv` BIGINT COMMENT '首页AI助手独立访客数'
) 
COMMENT '首页各模块流量统计表，按运营大区、客户业态、日期维度统计首页各功能模块的UV/PV数据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期分区'
)
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
   'comment'='首页流量分析表，用于分析各运营大区不同客户业态的首页各模块用户行为数据') 
LIFECYCLE 30;
```