CREATE TABLE IF NOT EXISTS app_saas_brand_label_df(
	`date` STRING COMMENT '日期，格式为yyyyMMdd',
	`brand_id` BIGINT COMMENT '品牌ID，唯一标识一个品牌',
	`brand_alias` STRING COMMENT '品牌名称，品牌的简称或别名',
	`brand_name` STRING COMMENT '品牌的企业名称，完整的公司注册名称',
	`tenant_id` BIGINT COMMENT '租户ID，唯一标识一个租户',
	`tenant_name` STRING COMMENT '租户名称，租户的显示名称',
	`all_store_cnt` BIGINT COMMENT '门店总注册数，所有类型门店的注册总数',
	`direct_store_cnt` BIGINT COMMENT '直营门店注册数，品牌直接运营的门店数量',
	`join_store_cnt` BIGINT COMMENT '加盟门店注册数，加盟合作的门店数量',
	`managed_store_cnt` BIGINT COMMENT '托管门店注册数，由品牌托管运营的门店数量',
	`last_order_time` DATETIME COMMENT '最近一次交易时间，格式为yyyy-MM-dd HH:mm:ss，表示该品牌最后一次下单的时间',
	`order_store_cnt_30d` BIGINT COMMENT '近30天总交易门店数，最近30天内有过交易的门店总数',
	`order_direct_store_cnt_30d` BIGINT COMMENT '近30天交易直营门店数，最近30天内有过交易的直营门店数量',
	`order_join_store_cnt_30d` BIGINT COMMENT '近30天交易加盟门店数，最近30天内有过交易的加盟门店数量',
	`order_managed_store_cnt_30d` BIGINT COMMENT '近30天交易托管门店数，最近30天内有过交易的托管门店数量',
	`store_order_cnt_30d_avg` DECIMAL(38,18) COMMENT '近30天门店平均交易次数，最近30天内每个交易门店的平均下单次数',
	`order_province_cnt_30d` BIGINT COMMENT '近30天交易省份数，最近30天内有过交易的省份数量',
	`order_city_cnt_30d` BIGINT COMMENT '近30天交易城市数，最近30天内有过交易的城市数量',
	`total_gmv_30d` DECIMAL(38,18) COMMENT '近30天总交易GMV，最近30天内所有交易的总金额',
	`xianmu_gmv_30d` DECIMAL(38,18) COMMENT '近30天鲜沐自营交易GMV，最近30天内鲜沐自营业务的交易金额',
	`xianmu_gmv_30d_rate` DECIMAL(38,18) COMMENT '近30天鲜沐自营交易GMV占比，鲜沐自营GMV占总GMV的比例，取值范围0-1',
	`xianmu_store_cnt_30d` BIGINT COMMENT '近30天鲜沐自营交易门店数，最近30天内使用鲜沐自营业务的门店数量',
	`dc_gmv_30d` DECIMAL(38,18) COMMENT '近30天代仓交易GMV，最近30天内代仓业务的交易金额',
	`dc_gmv_30d_rate` DECIMAL(38,18) COMMENT '近30天代仓交易GMV占比，代仓GMV占总GMV的比例，取值范围0-1',
	`dc_store_cnt_30d` BIGINT COMMENT '近30天代仓交易门店数，最近30天内使用代仓业务的门店数量',
	`ds_gmv_30d` DECIMAL(38,18) COMMENT '近30天代售交易GMV，最近30天内代售业务的交易金额',
	`ds_gmv_30d_rate` DECIMAL(38,18) COMMENT '近30天代售交易GMV占比，代售GMV占总GMV的比例，取值范围0-1',
	`ds_store_cnt_30d` BIGINT COMMENT '近30天代售交易门店数，最近30天内使用代售业务的门店数量',
	`khzy_gmv_30d` DECIMAL(38,18) COMMENT '近30天客户自营交易GMV（剔除供应商直发），最近30天内客户自营业务的交易金额',
	`khzy_gmv_30d_rate` DECIMAL(38,18) COMMENT '近30天客户自营交易GMV占比（剔除供应商直发），客户自营GMV占总GMV的比例，取值范围0-1',
	`khzy_store_cnt_30d` BIGINT COMMENT '近30天客户自营交易门店数，最近30天内使用客户自营业务的门店数量',
	`supplier_direct_gmv_30d` DECIMAL(38,18) COMMENT '近30天供应商直发交易GMV，最近30天内供应商直发业务的交易金额',
	`supplier_direct_gmv_30d_rate` DECIMAL(38,18) COMMENT '近30天供应商直发交易GMV占比，供应商直发GMV占总GMV的比例，取值范围0-1',
	`supplier_direct_store_cnt_30d` BIGINT COMMENT '近30天供应商直发交易门店数，最近30天内使用供应商直发业务的门店数量',
	`fruit_gmv_30d` DECIMAL(38,18) COMMENT '近30天鲜果交易GMV，最近30天内鲜果类商品的交易金额',
	`not_fruit_gmv_30d` DECIMAL(38,18) COMMENT '近30天标品交易GMV，最近30天内标品类商品的交易金额',
	`fruit_xianmu_gmv_30d` DECIMAL(38,18) COMMENT '近30天鲜沐自营鲜果交易GMV，最近30天内鲜沐自营的鲜果类商品交易金额',
	`fruit_30d_rate` DECIMAL(38,18) COMMENT '近30天鲜果渗透率（鲜沐自营交易鲜果GMV / 鲜果交易GMV），取值范围0-1',
	`dc_30d_rate` DECIMAL(38,18) COMMENT '近30天代仓渗透率（代仓交易门店数 / 门店总注册数)，取值范围0-1',
	`order_store_rate` DECIMAL(38,18) COMMENT '近30天saas使用渗透率（交易门店数 / 门店总注册数)，取值范围0-1'
) 
COMMENT 'SaaS品牌标签表，包含品牌基本信息、门店分布、交易行为和各业务线GMV等核心指标'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='SaaS品牌标签表，用于品牌维度分析和业务监控') 
LIFECYCLE 30;