```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_saas_goods_expiration_summary_di` (
  `time_tag` STRING COMMENT '日期标签，格式为yyyyMMdd，表示数据统计日期',
  `tenant_id` BIGINT COMMENT '租户ID，SKU所属的租户标识',
  `sku_id` BIGINT COMMENT 'SKU ID，SaaS系统中的商品库存单位唯一标识',
  `warehouse_no` BIGINT COMMENT '仓库编号，库存仓库的唯一标识ID',
  `warehouse_name` STRING COMMENT '仓库名称，库存仓库的具体名称',
  `batch` STRING COMMENT '批次号，商品的生产或进货批次标识',
  `expiration_date` DATETIME COMMENT '有效期至，格式为yyyy-MM-dd HH:mm:ss，表示商品过期日期时间',
  `expiration_batch_stock` BIGINT COMMENT '过期批次库存数量，过期时该批次的库存数量',
  `ending_batch_stock` BIGINT COMMENT '期末批次库存数量，统计期末该批次的库存数量',
  `item_id` BIGINT COMMENT '商品ID，基础商品的唯一标识',
  `sale_price` DECIMAL(38,18) COMMENT '销售价格，商品的售价，精度为38位整数和18位小数'
) 
COMMENT 'SaaS近30天货品过期汇总表，统计各租户商品过期情况和库存变化'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期分区'
)
STORED AS ALIORC  
TBLPROPERTIES ('comment'='SaaS货品过期监控汇总表，用于跟踪商品有效期和库存变化趋势') 
LIFECYCLE 30;
```