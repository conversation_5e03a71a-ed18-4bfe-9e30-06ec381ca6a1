CREATE TABLE IF NOT EXISTS app_crm_sku_bd_month_merchant_di(
	month_tag STRING COMMENT '月份标记，格式为yyyyMM，表示年月',
	sku_id STRING COMMENT '商品SKU编号',
	bd_id BIGINT COMMENT '销售ID，取值范围：最小值13，最大值1189140，平均值391989',
	merchant_id_text STRING COMMENT '商户ID列表，使用英文逗号分隔多个商户ID'
) 
COMMENT '每个SKU每月下单商户ID表，记录每个商品SKU在每个月对应的销售人员和下单商户信息'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='每个SKU每月下单商户ID表，用于分析商品销售情况和商户购买行为') 
LIFECYCLE 30;