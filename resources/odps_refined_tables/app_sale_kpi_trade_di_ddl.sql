```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_sale_kpi_trade_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务日期',
  `order_origin_total_amt` DECIMAL(38,18) COMMENT '交易应付总金额，单位为元，包含优惠前的订单金额',
  `order_real_total_amt` DECIMAL(38,18) COMMENT '交易实付总金额，单位为元，用户实际支付的金额',
  `order_cust_cnt` BIGINT COMMENT '交易客户数，当日下单的独立客户数量',
  `order_cust_arpu` DECIMAL(38,18) COMMENT 'ARPU(平均每用户收入)，计算公式：应付总金额/客户数，单位为元',
  `order_cnt` BIGINT COMMENT '交易订单数，当日产生的订单总数',
  `lose_cust_cnt` BIGINT COMMENT '交易流失客户数，90天内活跃用户近60天未下单客户数',
  `lose_cust_ratio` DECIMAL(38,18) COMMENT '交易流失率，计算公式：流失客户数/总客户数',
  `delivery_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额，单位为元，履约订单的应付金额',
  `delivery_real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额，单位为元，履约订单的实际支付金额',
  `delivery_cust_cnt` BIGINT COMMENT '履约活跃客户数，当日有履约行为的独立客户数量',
  `delivery_origin_profit` DECIMAL(38,18) COMMENT '履约应付毛利润，单位为元，基于应付金额计算的毛利润',
  `delivery_real_profit` DECIMAL(38,18) COMMENT '履约实付毛利润，单位为元，基于实付金额计算的毛利润',
  `delivery_after_profit` DECIMAL(38,18) COMMENT '履约后毛利润，单位为元，扣除成本后的净利润',
  `delivery_days_avg` DECIMAL(38,18) COMMENT '履约频次，平均履约天数',
  `delivery_point_cnt` BIGINT COMMENT '履约点位数，参与履约的网点或门店数量',
  `delivery_amt` DECIMAL(38,18) COMMENT '自营履约费用，单位为元，自营业务的履约成本',
  `new_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '新客履约应付总金额，单位为元，新客户的履约应付金额',
  `new_delivery_real_total_amt` DECIMAL(38,18) COMMENT '新客履约实付总金额，单位为元，新客户的履约实付金额',
  `new_delivery_cust_cnt` BIGINT COMMENT '新客履约活跃客户数，当日新客户的履约客户数',
  `new_delivery_real_profit` DECIMAL(38,18) COMMENT '新客履约实付毛利润，单位为元，新客户的履约毛利润',
  `old_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '老客履约应付总金额，单位为元，老客户的履约应付金额',
  `old_delivery_real_total_amt` DECIMAL(38,18) COMMENT '老客履约实付总金额，单位为元，老客户的履约实付金额',
  `old_delivery_cust_cnt` BIGINT COMMENT '老客履约活跃客户数，当日老客户的履约客户数',
  `old_delivery_real_profit` DECIMAL(38,18) COMMENT '老客履约实付毛利润，单位为元，老客户的履约毛利润',
  `order_sku_cnt` BIGINT COMMENT '交易SKU款数，当日交易涉及的商品SKU数量',
  `order_sku_weight` DECIMAL(38,18) COMMENT '交易SKU重量（KG），当日交易商品的总重量',
  `delivery_sku_cnt` BIGINT COMMENT '履约SKU款数，当日履约涉及的商品SKU数量',
  `delivery_sku_weight` DECIMAL(38,18) COMMENT '履约SKU重量（KG），当日履约商品的总重量'
) 
COMMENT '销售KPI指标汇总表，包含交易和履约相关的核心业务指标，用于销售绩效分析和业务监控'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '销售KPI指标汇总表，每日更新，包含交易金额、客户数、订单数、履约数据等核心业务指标',
  'lifecycle' = '365'
)
LIFECYCLE 365;
```