CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_saas_order_after_sale_inverted_summary_di`(
	`tenant_id` BIGINT COMMENT '租户ID，唯一标识一个租户',
	`time_tag` STRING COMMENT '时间标签，格式为yyyyMMdd',
	`after_sale_order_no` STRING COMMENT '售后单号，售后业务的唯一标识',
	`order_no` STRING COMMENT '订单编号，原订单的唯一标识',
	`order_item_id` STRING COMMENT '订单项编号，订单中具体商品的唯一标识',
	`apply_time` DATETIME COMMENT '售后申请时间，格式为yyyy-MM-dd HH:mm:ss',
	`audit_time` DATETIME COMMENT '审核时间，格式为yyyy-MM-dd HH:mm:ss',
	`refund_total_amount` DECIMAL(38,18) COMMENT '退款总额，精确到18位小数',
	`responsibility_type` BIGINT COMMENT '售后责任方：0-供应商，1-品牌方，2-门店',
	`brand_refundable_amout` DECIMAL(38,18) COMMENT '品牌应退金额，精确到18位小数',
	`brand_actual_amount` DECIMAL(38,18) COMMENT '品牌实退金额，精确到18位小数',
	`supplier_refundable_amount` DECIMAL(38,18) COMMENT '供应商应退金额，精确到18位小数',
	`supplier_actual_amount` DECIMAL(38,18) COMMENT '供应商实退金额，精确到18位小数',
	`inverted_amount` DECIMAL(38,18) COMMENT '品牌应付给供应商的差额（供应商实退金额-供应商应退金额），精确到18位小数',
	`supplier_id` BIGINT COMMENT '供应商ID，唯一标识一个供应商'
) 
COMMENT 'SAAS对账单-售后单倒挂明细表，记录售后业务中责任方倒挂的明细数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='SAAS对账单售后单倒挂明细表，用于分析售后业务中的责任方倒挂情况') 
LIFECYCLE 30;