```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_kpi_whole_trade_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务日期',
  `cust_group` STRING COMMENT '客户类型，取值范围：大客户、平台客户、批发客户、ALL（表示所有客户类型的汇总）',
  `sku_type` STRING COMMENT '商品类型，取值范围：自营、代仓、全品类、SAAS客户自营、ALL（表示所有商品类型的汇总）',
  `category` STRING COMMENT '商品类目，取值范围：鲜果、乳制品、其他、ALL（表示所有商品类目的汇总）',
  `origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额，交易订单的原始应付金额总计',
  `real_total_amt` DECIMAL(38,18) COMMENT '实付总金额，交易订单的实际支付金额总计',
  `cust_cnt` BIGINT COMMENT '客户数，去重后的客户数量统计',
  `order_cnt` BIGINT COMMENT '订单数，交易订单的数量统计',
  `timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送应付总金额，省心送服务的应付金额总计',
  `after_sale_noreceived_amt` DECIMAL(38,18) COMMENT '未到货售后总金额，未收到货的售后申请金额总计'
) 
COMMENT '交易KPI金额汇总表，按日期、客户类型、商品类型、商品类目等多维度统计交易关键指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据加载日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '交易KPI金额汇总表，包含应付金额、实付金额、客户数、订单数等核心交易指标的多维度统计数据',
  'lifecycle' = '30'
);
```