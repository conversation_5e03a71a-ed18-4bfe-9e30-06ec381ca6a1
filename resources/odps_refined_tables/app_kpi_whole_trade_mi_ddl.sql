CREATE TABLE IF NOT EXISTS app_kpi_whole_trade_mi(
	month STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
	cust_group STRING COMMENT '客户类型:大客户、平台客户、批发客户、ALL（枚举值：大客户、平台客户、批发客户、ALL）',
	sku_type STRING COMMENT '商品类型:自营、代仓、全品类、SAAS客户自营、ALL（枚举值：自营、代仓、全品类、SAAS客户自营、ALL）',
	category STRING COMMENT '商品类目:鲜果、乳制品、其他、ALL（枚举值：鲜果、乳制品、其他、ALL）',
	origin_total_amt DECIMAL(38,18) COMMENT '应付总金额，交易应付的总金额',
	real_total_amt DECIMAL(38,18) COMMENT '实付总金额，交易实际支付的总金额',
	cust_cnt BIGINT COMMENT '客户数，统计周期内的客户数量',
	order_cnt BIGINT COMMENT '订单数，统计周期内的订单数量',
	timing_origin_total_amt DECIMAL(38,18) COMMENT '省心送应付总金额，省心送服务的应付金额',
	after_sale_noreceived_amt DECIMAL(38,18) COMMENT '未到货售后总金额，未到货售后服务的总金额'
) 
COMMENT '交易KPI金额汇总表，包含各维度的交易金额、客户数、订单数等关键绩效指标'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='交易KPI金额汇总表，用于分析各维度交易表现') 
LIFECYCLE 30;