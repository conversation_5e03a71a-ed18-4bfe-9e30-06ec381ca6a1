CREATE TABLE IF NOT EXISTS app_stc_warehouse_kpi_mi(
	month STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
	warehouse_no BIGINT COMMENT '仓库编号，取值范围：2-155',
	warehouse_name STRING COMMENT '仓库名称',
	check_sku_cnt BIGINT COMMENT '抽检SKU数量，统计周期内抽检的商品种类数',
	in_bound_sku_cnt BIGINT COMMENT '入库SKU数量，统计周期内入库的商品种类数',
	back_order_cnt BIGINT COMMENT '退货订单总数，统计周期内发生的退货订单数量',
	finish_order_cnt BIGINT COMMENT '已完成订单数，统计周期内已完成的订单数量',
	damage_amt DECIMAL(38,18) COMMENT '货损总金额，统计周期内发生的货损金额总和',
	damage_amt_wah DECIMAL(38,18) COMMENT '仓配责任货损金额，统计周期内由仓配责任导致的货损金额',
	sale_amt DECIMAL(38,18) COMMENT '销售金额，统计周期内的商品销售总额',
	after_sale_amt DECIMAL(38,18) COMMENT '售后总金额，统计周期内发生的所有售后相关金额总和',
	after_sale_amt_wah DECIMAL(38,18) COMMENT '仓配责任售后金额，统计周期内由仓配责任导致的售后金额',
	after_sale_amt_pur DECIMAL(38,18) COMMENT '采购责任售后金额，统计周期内由采购责任导致的售后金额',
	after_sale_amt_che DECIMAL(38,18) COMMENT '品控责任售后金额，统计周期内由品控责任导致的售后金额',
	after_sale_amt_pur_che DECIMAL(38,18) COMMENT '采购品控共同责任售后金额，统计周期内由采购和品控共同责任导致的售后金额',
	after_sale_amt_oth DECIMAL(38,18) COMMENT '其他责任售后金额，统计周期内由其他责任导致的售后金额',
	delivery_total_amt DECIMAL(38,18) COMMENT '配送销售金额，统计周期内通过配送方式完成的销售金额',
	coupon_amt DECIMAL(38,18) COMMENT '优惠券金额，统计周期内使用的优惠券金额总和',
	origin_total_amt DECIMAL(38,18) COMMENT '应付GMV，统计周期内应付的商品交易总额',
	real_total_amt DECIMAL(38,18) COMMENT '实付GMV，统计周期内实际支付的商品交易总额',
	storage_amt DECIMAL(38,18) COMMENT '仓储成本，统计周期内发生的仓储相关费用',
	arterial_roads_amt DECIMAL(38,18) COMMENT '干线运输成本，统计周期内发生的干线运输费用',
	deliver_amt DECIMAL(38,18) COMMENT '配送成本，统计周期内发生的配送相关费用',
	self_picked_amt DECIMAL(38,18) COMMENT '采购自提成本，统计周期内发生的采购自提相关费用',
	other_amt DECIMAL(38,18) COMMENT '其他成本，统计周期内发生的其他运营成本',
	allocation_amt DECIMAL(38,18) COMMENT '调拨成本，统计周期内发生的商品调拨相关费用'
) 
COMMENT '仓配KPI库存数据表，包含仓库运营的各项关键绩效指标数据，用于仓库管理和绩效分析'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期，如20250917表示2025年9月17日')
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='仓配KPI库存数据表，按月度和仓库维度统计各项运营指标，包括库存、销售、售后、成本等数据')
LIFECYCLE 30;