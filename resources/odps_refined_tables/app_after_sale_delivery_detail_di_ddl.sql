```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_after_sale_delivery_detail_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd',
  `after_sale_order_id` STRING COMMENT '售后单号，即after_sale_order_no',
  `apply_time` DATETIME COMMENT '售后申请时间，格式为年月日时分秒',
  `finish_time` DATETIME COMMENT '审核完成时间，格式为年月日时分秒',
  `after_sale_sku_cnt` BIGINT COMMENT '售后商品数量',
  `after_sale_unit` STRING COMMENT '售后单位，枚举值：g-克',
  `after_sale_origin_amt` DECIMAL(38,18) COMMENT '售后应退金额',
  `after_sale_amt` DECIMAL(38,18) COMMENT '售后实退金额',
  `after_sale_type` STRING COMMENT '售后分类，枚举值：质量,腐烂/发霉/变质/黑斑、质量,过熟等',
  `after_sale_order_type` STRING COMMENT '售后订单类型，枚举值：普通售后',
  `reason` STRING COMMENT '售后原因，枚举值：商品品质问题',
  `handle_type` STRING COMMENT '处理方式，枚举值：返券',
  `after_sale_remark` STRING COMMENT '发起备注',
  `review_remark` STRING COMMENT '客服备注',
  `detail_review_remark` STRING COMMENT '审核备注',
  `reviewer` STRING COMMENT '审核人',
  `approver` STRING COMMENT '审批人',
  `order_no` STRING COMMENT '订单号',
  `order_type` STRING COMMENT '订单类型，枚举值：省心送、其他',
  `order_date` STRING COMMENT '下单日期，格式为yyyyMMdd',
  `real_unit_amt` DECIMAL(38,18) COMMENT '配送实付单价',
  `origin_unit_amt` DECIMAL(38,18) COMMENT '配送应付单价',
  `sku_cnt` BIGINT COMMENT '配送商品数量',
  `deliver_time` DATETIME COMMENT '配送日期，格式为年月日时分秒',
  `store_no` BIGINT COMMENT '配送仓编号',
  `store_name` STRING COMMENT '配送仓名称',
  `warehouse_no` BIGINT COMMENT '库存仓编号',
  `warehouse_name` STRING COMMENT '库存仓名称',
  `path` STRING COMMENT '线路',
  `driver` STRING COMMENT '司机',
  `sku_id` STRING COMMENT '商品SKU',
  `spu_id` BIGINT COMMENT '商品SPU；即pd_id',
  `spu_no` STRING COMMENT 'spu编号',
  `spu_name` STRING COMMENT '商品名',
  `sku_disc` STRING COMMENT 'sku描述；即weight',
  `sku_type` STRING COMMENT '商品类型，枚举值：自营、代仓、代售',
  `sku_brand` STRING COMMENT '品牌',
  `category1` STRING COMMENT '一级分类，枚举值：鲜果',
  `category2` STRING COMMENT '二级分类，枚举值：新鲜水果',
  `category3` STRING COMMENT '三级分类，枚举值：核果类、浆果类、瓠果类',
  `category4` STRING COMMENT '四级分类，枚举值：芒果、草莓、火龙果、西瓜',
  `cust_id` BIGINT COMMENT '售后用户,即客户ID',
  `cust_name` STRING COMMENT '客户名',
  `cust_type` STRING COMMENT '客户类型，枚举值：大客户、普通',
  `cust_team` STRING COMMENT '客户团队类型，枚举值：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户、批发',
  `brand_id` BIGINT COMMENT '品牌ID（原大客户），-1表示无品牌',
  `brand_name` STRING COMMENT '品牌的企业名称',
  `brand_alias` STRING COMMENT '品牌的品牌名称',
  `city_id` BIGINT COMMENT '运营服务ID',
  `city_name` STRING COMMENT '运营服务名称',
  `large_area_id` BIGINT COMMENT '运营服务大区id',
  `large_area_name` STRING COMMENT '运营服务大区name',
  `province` STRING COMMENT '注册时省',
  `city` STRING COMMENT '注册时市',
  `area` STRING COMMENT '注册时区',
  `apply_remark` STRING COMMENT '用户备注',
  `store_method` STRING COMMENT '储存区域，枚举值：冷藏、常温',
  `is_out_time` STRING COMMENT '是否超时售后，枚举值：是、否',
  `purchase_no` STRING COMMENT '采购批次',
  `supplier` STRING COMMENT '批次供应商',
  `only_code` STRING COMMENT '唯一码',
  `failed_rate` DECIMAL(38,18) COMMENT '不合格率',
  `damage_rate` DECIMAL(38,18) COMMENT '货损率',
  `question_desc` STRING COMMENT '货检评价，枚举值：合格入库',
  `question_type` STRING COMMENT '货检描述',
  `receipt_method` STRING COMMENT '收货方式，枚举值：挑选入库、正常入库',
  `origin_purchase_no` STRING COMMENT '原采购单',
  `min_stock_time` DATETIME COMMENT '首次入库日期，格式为年月日时分秒',
  `quality_day` BIGINT COMMENT '保质期（天）',
  `in_stock_day` BIGINT COMMENT '在库天数',
  `origin_sku` STRING COMMENT '原sku',
  `warn_day` BIGINT COMMENT '到期预警天数',
  `shelf_life` BIGINT COMMENT '保鲜天数（鲜果）'
) 
COMMENT '已到货售后配送明细数据表，包含售后订单的配送详情、商品信息、客户信息和质检信息'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '已到货售后配送明细数据表，记录售后订单的完整配送流程和商品质量信息',
  'lifecycle' = '30'
)
```