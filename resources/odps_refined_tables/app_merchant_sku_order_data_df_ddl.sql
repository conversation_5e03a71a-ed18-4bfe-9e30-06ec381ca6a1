CREATE TABLE IF NOT EXISTS app_merchant_sku_order_data_df(
	m_id BIGINT COMMENT '门店ID，唯一标识一个门店',
	sku STRING COMMENT 'SKU编码，商品唯一标识',
	last_order_time DATETIME COMMENT '最近一次下单时间，格式为年月日时分秒',
	last_order_quantity BIGINT COMMENT '最近一次下单数量',
	last_thirty_days_order_count BIGINT COMMENT '最近30天下单次数，取值范围：0-46',
	last_sixty_days_order_count BIGINT COMMENT '最近60天下单次数，取值范围：0-52',
	last_two_years_order_count BIGINT COMMENT '最近2年下单次数，取值范围：1-434',
	delete_time_order_count BIGINT COMMENT '上次删除至今下单次数，取值范围：0-3',
	day_tag STRING COMMENT '数据同步日期，格式为YYYYMMDD'
) 
COMMENT '采购助手-门店常购清单订单行为数据，记录门店对SKU的订单行为统计信息'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='采购助手-门店常购清单订单行为数据表，包含门店SKU级别的订单行为统计指标') 
LIFECYCLE 30;