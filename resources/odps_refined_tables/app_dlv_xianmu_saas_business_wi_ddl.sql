```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_dlv_xianmu_saas_business_wi`(
  `year` STRING COMMENT '年份，格式：YYYY',
  `week_of_year` STRING COMMENT '周数，一年中的第几周，范围：1-53',
  `monday` STRING COMMENT '周一日期，格式：YYYYMMDD，表示年月日',
  `sunday` STRING COMMENT '周日日期，格式：YYYYMMDD，表示年月日',
  `business_type` STRING COMMENT '业务类型，枚举值：批发,自营,代仓,代售,SAAS鲜沐自营,SAAS鲜沐代仓,SAAS品牌方自营',
  `origin_total_amt` DECIMAL(38,18) COMMENT '原始总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '实际总金额',
  `cost_amt` DECIMAL(38,18) COMMENT '成本金额',
  `delivery_amt` DECIMAL(38,18) COMMENT '运费金额',
  `point_cnt` BIGINT COMMENT '点位数',
  `sku_cnt` BIGINT COMMENT 'SKU数量',
  `total_sku_weight` DECIMAL(38,18) COMMENT '履约重量（KG）',
  `after_sale_received_order_cnt` BIGINT COMMENT '已到货售后总订单数',
  `after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后总金额',
  `inventory_loss_amt` DECIMAL(38,18) COMMENT '盘亏总金额',
  `inventory_profit_amt` DECIMAL(38,18) COMMENT '盘盈总金额',
  `damage_amt` DECIMAL(38,18) COMMENT '货损总金额',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储成本',
  `arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本',
  `deliver_amt` DECIMAL(38,18) COMMENT '配送成本',
  `self_picked_amt` DECIMAL(38,18) COMMENT '采购自提成本',
  `other_amt` DECIMAL(38,18) COMMENT '其他成本',
  `allocation_amt` DECIMAL(38,18) COMMENT '调拨成本',
  `heytea_storage_amt` DECIMAL(38,18) COMMENT '喜茶仓储成本',
  `heytea_arterial_roads_amt` DECIMAL(38,18) COMMENT '喜茶调拨成本',
  `heytea_deliver_amt` DECIMAL(38,18) COMMENT '喜茶配送成本',
  `heytea_way_deliver_amt` DECIMAL(38,18) COMMENT '喜茶专配成本'
)
COMMENT '各业务线数据大盘，包含各业务线的财务、运营、成本等核心指标数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式：YYYYMMDD，表示年月日')
STORED AS ALIORC
TBLPROPERTIES ('comment'='各业务线数据大盘表，用于业务数据分析和监控')
LIFECYCLE 30;
```