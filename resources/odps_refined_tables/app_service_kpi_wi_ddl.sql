CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_service_kpi_wi` (
  `year` STRING COMMENT '年份，格式：YYYY',
  `week_of_year` BIGINT COMMENT '周数，取值范围：1-53',
  `monday` STRING COMMENT '周一日期，格式：YYYYMMDD',
  `sunday` STRING COMMENT '周日日期，格式：YYYYMMDD',
  `cust_team` STRING COMMENT '客户类型，枚举值：全量客户，平台客户',
  `channel_type` STRING COMMENT '渠道类型，枚举值：鲜沐，SAAS',
  `after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后总金额（单位：元）',
  `after_sale_received_quality_amt` DECIMAL(38,18) COMMENT '已到货售后总金额（质量问题，单位：元）',
  `after_sale_received_warehouse_amt` DECIMAL(38,18) COMMENT '已到货售后总金额（仓配问题，单位：元）',
  `after_sale_received_other_amt` DECIMAL(38,18) COMMENT '已到货售后总金额（其他问题，单位：元）',
  `delivery_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额（单位：元）',
  `after_sale_received_ratio` DECIMAL(38,18) COMMENT '已到货售后率（小数形式，如0.005表示0.5%）',
  `delivery_evaluation_low_cnt` BIGINT COMMENT '司机评价差评数（3星以下）',
  `delivery_evaluation_cnt` BIGINT COMMENT '司机评价总数'
) 
COMMENT '客服KPI指标表，包含售后金额、履约金额、售后率、司机评价等关键绩效指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据日期'
)
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='客服KPI周度统计表，按客户类型和渠道类型维度统计售后相关指标') 
LIFECYCLE 30;