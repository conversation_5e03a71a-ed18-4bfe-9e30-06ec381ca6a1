CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_sku_order_coupon_mi` (
  `month` STRING COMMENT '月份，格式为yyyyMM，例如：202509表示2025年9月',
  `sku_id` STRING COMMENT 'SKU编码，商品最小库存单位的唯一标识',
  `spu_name` STRING COMMENT '商品名称，标准产品单元的名称',
  `sku_disc` STRING COMMENT '商品描述，包含规格信息，例如：10KG*1箱',
  `category1` STRING COMMENT '一级类目，商品所属的大类，例如：乳制品',
  `coupon_amt_label` DECIMAL(38,18) COMMENT '营销费用占比，取值范围：0-100，表示营销费用占商品价格的比例',
  `cust_cnt` BIGINT COMMENT '客户数，购买该SKU的客户数量，取值范围：0-1704'
)
COMMENT '人群营销结构表，记录商品营销费用占比和客户购买情况的统计分析'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示年月日，例如：20250917表示2025年9月17日'
)
STORED AS ALIORC
TBLPROPERTIES ('comment'='人群营销结构分析表，用于分析商品营销效果和客户购买行为')
LIFECYCLE 30;