CREATE TABLE IF NOT EXISTS app_service_area_trunk_cost_wi(
	`year` STRING COMMENT '年份，格式：YYYY',
	`week_of_year` STRING COMMENT '周数，取值范围：1-53',
	`monday` STRING COMMENT '周一日期，格式：YYYYMMDD',
	`sunday` STRING COMMENT '周日日期，格式：YYYYMMDD',
	`service_area` STRING COMMENT '服务区域，枚举值：华东、华南等',
	`end_place_no` BIGINT COMMENT '目的仓编号，取值范围：1-155',
	`end_place_name` STRING COMMENT '目的仓名称',
	`path_name` STRING COMMENT '线路名称，描述运输路径',
	`trunk_fixed_amt` DECIMAL(38,18) COMMENT '干线固资折旧费（元）',
	`self_amt` DECIMAL(38,18) COMMENT '自提费用（元）',
	`allocate_amt` DECIMAL(38,18) COMMENT '调拨费用（元）',
	`nodelivery_amt` DECIMAL(38,18) COMMENT '非履约费用（元）',
	`trunk_total_amt` DECIMAL(38,18) COMMENT '干线总费用（元）',
	`self_trunk_amt` DECIMAL(38,18) COMMENT '自营干线费用（元）',
	`heytea_trunk_amt` DECIMAL(38,18) COMMENT '喜茶干线费用（元）',
	`self_trunk_km_cnt` DECIMAL(38,18) COMMENT '自营干线总公里数（公里）',
	`big_cust_total_amt` DECIMAL(38,18) COMMENT '大客户用车总金额（元）'
) 
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：YYYYMMDD') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='服务区域目的仓干线明细表，记录各服务区域到目的仓的干线运输费用明细，包含固资折旧、自提、调拨、非履约等各类费用') 
LIFECYCLE 30;