CREATE TABLE IF NOT EXISTS app_warehouse_estimated_consumption_df(
    pd_id BIGINT COMMENT '产品ID，取值范围：100-16711',
    sku_id STRING COMMENT 'SKU编码，商品唯一标识',
    warehouse_no BIGINT COMMENT '仓库编号，取值范围：2-169',
    view_date DATETIME COMMENT '查看日期，格式：年月日时分秒',
    estimated_sales BIGINT COMMENT '预估销量，取值范围：0-1',
    estimated_transfer_out BIGINT COMMENT '预估调拨量，取值范围：0',
    consumption BIGINT COMMENT '预估消耗量，取值范围：0-1',
    sale_cnt DECIMAL(38,18) COMMENT '预销出库量(不取整)，取值范围：0',
    allocation_cnt DECIMAL(38,18) COMMENT '预调出库量(不取整)，取值范围：0',
    consumption_a DECIMAL(38,18) COMMENT '预估消耗量(不取整)，取值范围：0',
    transfer_order_plan_out_quantity DECIMAL(38,18) COMMENT '调拨计划出数量，取值范围：0'
)
COMMENT '库存仓预估消耗表，包含各仓库产品的预估销售、调拨和消耗数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，yyyyMMdd格式')
STORED AS ALIORC
TBLPROPERTIES ('columnar.nested.type'='true',
    'comment'='库存仓预估消耗分析表，用于预测仓库产品的销售、调拨和消耗情况')
LIFECYCLE 30;