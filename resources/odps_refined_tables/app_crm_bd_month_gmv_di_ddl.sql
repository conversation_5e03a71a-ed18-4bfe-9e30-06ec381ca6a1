CREATE TABLE IF NOT EXISTS app_crm_bd_month_gmv_di(
    `month` STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
    `bd_id` BIGINT COMMENT 'BD_ID，业务发展人员唯一标识，取值范围：-1表示未知或汇总，正数表示具体BD人员',
    `bd_name` STRING COMMENT 'BD名称，业务发展人员姓名',
    `city` STRING COMMENT 'bd归属城市，BD所属城市名称',
    `m1_name` STRING COMMENT 'M1管理者姓名，即BD的直接上级',
    `m2_name` STRING COMMENT 'M2管理者姓名，即M1的直接上级',
    `m3_name` STRING COMMENT 'M3管理者姓名，即M2的直接上级',
    `is_same_city` STRING COMMENT '是否同城，枚举值：是-同城，否-不同城',
    `total_amt` DECIMAL(38,18) COMMENT '总交易实付GMV，所有渠道的总交易金额',
    `categoryies_total_amt` DECIMAL(38,18) COMMENT '全品类交易实付GMV，全品类商品交易金额',
    `fruit_total_amt` DECIMAL(38,18) COMMENT '鲜果交易实付GMV（不含全品类），鲜果类商品交易金额',
    `ancho_total_amt` DECIMAL(38,18) COMMENT '安佳铁塔交易实付GMV，安佳铁塔品牌商品交易金额',
    `self_total_amt` DECIMAL(38,18) COMMENT '自营品交易实付GMV，自营商品交易金额',
    `dairy_total_amt` DECIMAL(38,18) COMMENT '乳制品交易实付GMV（不含AT、全品类），乳制品类商品交易金额',
    `other_total_amt` DECIMAL(38,18) COMMENT '其他品交易实付GMV（不含全品类），其他品类商品交易金额',
    `xm_total_amt` DECIMAL(38,18) COMMENT '鲜沐交易实付GMV，鲜沐渠道总交易金额',
    `xm_categories_total_amt` DECIMAL(38,18) COMMENT '鲜沐全品类交易实付GMV，鲜沐渠道全品类商品交易金额',
    `xm_self_total_amt` DECIMAL(38,18) COMMENT '鲜沐自营品交易实付GMV，鲜沐渠道自营商品交易金额',
    `xm_fruit_total_amt` DECIMAL(38,18) COMMENT '鲜沐鲜果交易实付GMV，鲜沐渠道鲜果类商品交易金额',
    `xm_ancho_total_amt` DECIMAL(38,18) COMMENT '鲜沐安佳铁塔交易实付GMV，鲜沐渠道安佳铁塔品牌商品交易金额',
    `xm_dairy_total_amt` DECIMAL(38,18) COMMENT '鲜沐乳制品交易实付GMV（不含AT、全品类），鲜沐渠道乳制品类商品交易金额',
    `xm_other_total_amt` DECIMAL(38,18) COMMENT '鲜沐其他品交易实付GMV（不含全品类），鲜沐渠道其他品类商品交易金额',
    `xm_cust_cnt` DECIMAL(38,18) COMMENT '鲜沐交易客户数，鲜沐渠道交易客户数量',
    `xm_categories_cust_cnt` DECIMAL(38,18) COMMENT '鲜沐全品类交易客户数，鲜沐渠道全品类商品交易客户数量',
    `xm_self_cust_cnt` DECIMAL(38,18) COMMENT '鲜沐自营品交易客户数，鲜沐渠道自营商品交易客户数量',
    `xm_fruit_cust_cnt` DECIMAL(38,18) COMMENT '鲜沐鲜果交易客户数，鲜沐渠道鲜果类商品交易客户数量',
    `xm_ancho_cust_cnt` DECIMAL(38,18) COMMENT '鲜沐安佳铁塔交易客户数，鲜沐渠道安佳铁塔品牌商品交易客户数量',
    `xm_dairy_cust_cnt` DECIMAL(38,18) COMMENT '鲜沐乳制品交易客户数（不含AT、全品类），鲜沐渠道乳制品类商品交易客户数量',
    `xm_other_cust_cnt` DECIMAL(38,18) COMMENT '鲜沐其他品交易客户数（不含全品类），鲜沐渠道其他品类商品交易客户数量',
    `saas_total_amt` DECIMAL(38,18) COMMENT 'SaaS交易实付GMV，SaaS渠道总交易金额',
    `saas_categories_total_amt` DECIMAL(38,18) COMMENT 'SaaS全品类交易实付GMV，SaaS渠道全品类商品交易金额',
    `saas_self_total_amt` DECIMAL(38,18) COMMENT 'SaaS自营品交易实付GMV，SaaS渠道自营商品交易金额',
    `saas_fruit_total_amt` DECIMAL(38,18) COMMENT 'SaaS鲜果交易实付GMV，SaaS渠道鲜果类商品交易金额',
    `saas_ancho_total_amt` DECIMAL(38,18) COMMENT 'SaaS安佳铁塔交易实付GMV，SaaS渠道安佳铁塔品牌商品交易金额',
    `saas_dairy_total_amt` DECIMAL(38,18) COMMENT 'SaaS乳制品交易实付GMV（不含AT、全品类），SaaS渠道乳制品类商品交易金额',
    `saas_other_total_amt` DECIMAL(38,18) COMMENT 'SaaS其他品交易实付GMV（不含全品类），SaaS渠道其他品类商品交易金额',
    `saas_cust_cnt` DECIMAL(38,18) COMMENT 'SaaS交易客户数，SaaS渠道交易客户数量',
    `saas_categories_cust_cnt` DECIMAL(38,18) COMMENT 'SaaS全品类交易客户数，SaaS渠道全品类商品交易客户数量',
    `saas_self_cust_cnt` DECIMAL(38,18) COMMENT 'SaaS自营品交易客户数，SaaS渠道自营商品交易客户数量',
    `saas_fruit_cust_cnt` DECIMAL(38,18) COMMENT 'SaaS鲜果交易客户数，SaaS渠道鲜果类商品交易客户数量',
    `saas_ancho_cust_cnt` DECIMAL(38,18) COMMENT 'SaaS安佳铁塔交易客户数，SaaS渠道安佳铁塔品牌商品交易客户数量',
    `saas_dairy_cust_cnt` DECIMAL(38,18) COMMENT 'SaaS乳制品交易客户数，SaaS渠道乳制品类商品交易客户数量',
    `saas_other_cust_cnt` DECIMAL(38,18) COMMENT 'SaaS其他品交易客户数，SaaS渠道其他品类商品交易客户数量'
)
COMMENT 'BD维度业绩表，按业务发展人员维度统计的月度GMV业绩数据，包含鲜沐和SaaS两个渠道的细分品类交易数据'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期年月日')
STORED AS ALIORC
TBLPROPERTIES ('comment'='BD月度业绩统计表，用于业务发展人员的业绩分析和追踪')
LIFECYCLE 30;