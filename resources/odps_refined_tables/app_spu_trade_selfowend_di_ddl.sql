CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_spu_trade_selfowend_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务日期',
  `register_province` STRING COMMENT '注册省份名称',
  `register_city` STRING COMMENT '注册城市名称',
  `register_area` STRING COMMENT '注册区域名称',
  `spu_id` STRING COMMENT 'SPU ID，商品唯一标识',
  `spu_name` STRING COMMENT 'SPU名称，商品标准名称',
  `cust_type` STRING COMMENT '客户类型；枚举值：面包蛋糕,西餐,咖啡,甜品冰淇淋,茶饮,其他',
  `brand_type` STRING COMMENT '大客户类型；枚举值：大客户,普通,批发客户（注：历史枚举值单店,批发大客户,普通大客户,KA大客户已弃用）',
  `brand_name` STRING COMMENT '品牌名称',
  `category1` STRING COMMENT '一级类目名称',
  `category2_id` STRING COMMENT '二级类目ID',
  `category2` STRING COMMENT '二级类目名称',
  `category3_id` STRING COMMENT '三级类目ID',
  `category3` STRING COMMENT '三级类目名称',
  `category4_id` STRING COMMENT '四级类目ID',
  `category4` STRING COMMENT '四级类目名称',
  `origin_total_amt` DECIMAL(38,18) COMMENT '实付总金额，原始订单金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '应付总金额，实际支付金额',
  `cust_cnt` BIGINT COMMENT '客户数，去重后的客户数量',
  `new_cust_cnt` BIGINT COMMENT '新客户数，历史截止当天首次下单的客户数量',
  `order_time_cnt` DECIMAL(38,18) COMMENT '客户下单时间间隔之和，单位为分钟',
  `order_time_avg` DECIMAL(38,18) COMMENT '平均下单时间间隔，单位为分钟'
)
COMMENT '城市整体交易数据日表，按城市维度统计SPU级别的交易数据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '城市整体交易数据日表，包含SPU级别的交易统计信息',
  'lifecycle' = '30'
);