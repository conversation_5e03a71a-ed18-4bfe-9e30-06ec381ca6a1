CREATE TABLE IF NOT EXISTS app_finance_saas_revenue_details_di(
	tenant_id BIGINT COMMENT '租户ID',
	tenant_name STRING COMMENT '租户名称',
	company_name STRING COMMENT '公司名称',
	order_no STRING COMMENT '订单编号',
	order_item_id STRING COMMENT '订单项编号',
	store_id BIGINT COMMENT '店铺ID',
	store_name STRING COMMENT '门店名称',
	store_type STRING COMMENT '门店类型：直营店、加盟店、托管店',
	province STRING COMMENT '省份',
	city STRING COMMENT '城市',
	area STRING COMMENT '区域',
	pay_time DATETIME COMMENT '支付时间，格式：年月日时分秒',
	pay_type STRING COMMENT '支付方式：微信支付、账期、余额支付、支付宝支付',
	delivery_time DATETIME COMMENT '配送时间，格式：年月日时分秒',
	finished_time DATETIME COMMENT '确认收货时间，格式：年月日时分秒',
	item_id BIGINT COMMENT '商品item_id',
	sku STRING COMMENT '鲜沐SKU',
	pd_name STRING COMMENT '货品名称',
	category1 STRING COMMENT '商品一级类目',
	tax_rate DECIMAL(38,18) COMMENT '税率',
	order_sku_cnt BIGINT COMMENT '商城售卖数量',
	goods_supply_price DECIMAL(38,18) COMMENT '货品采购单价',
	goods_supply_total_price DECIMAL(38,18) COMMENT '货品采购总价',
	goods_delivery_fee DECIMAL(38,18) COMMENT '供应商配送费',
	revenue_amt_notax DECIMAL(38,18) COMMENT '确认收入金额（不含税）',
	tax_amt DECIMAL(38,18) COMMENT '税额',
	unit_cost DECIMAL(38,18) COMMENT '成本单价（含税）',
	unit_cost_notax DECIMAL(38,18) COMMENT '成本单价（不含税）',
	cost DECIMAL(38,18) COMMENT '成本（含税）',
	cost_notax DECIMAL(38,18) COMMENT '成本（不含税）',
	cust_group STRING COMMENT '客户团队类型：平台客户、大客户',
	sub_type STRING COMMENT '商品二级性质：代销不入仓、代销入仓、经销',
	store_no BIGINT COMMENT '配送仓ID',
	warehouse_no BIGINT COMMENT '库存仓ID',
	service_area STRING COMMENT '大区',
	settle_type STRING COMMENT '结算类型'
) 
COMMENT 'SaaS业财一体化收入明细表，包含订单、支付、配送、商品、成本、收入等完整的财务业务数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，yyyyMMdd格式，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='SaaS业财一体化收入明细事实表，用于财务分析和业务报表') 
LIFECYCLE 30;