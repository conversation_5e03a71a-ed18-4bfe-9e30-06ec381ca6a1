CREATE TABLE IF NOT EXISTS app_dlv_pop_buyer_supplier_final_di(
	`date` STRING COMMENT '业务日期，格式为yyyyMMdd，表示数据统计的日期',
	`supplier` STRING COMMENT '供应商名称',
	`buyer` STRING COMMENT '买手名称',
	`sku_id` STRING COMMENT '商品SKU编号，唯一标识一个商品',
	`order_sku_cnt` BIGINT COMMENT '下单商品数量，取值范围：≥0',
	`order_sku_weight` DECIMAL(38,18) COMMENT '下单商品总毛重（单位：斤），取值范围：≥0',
	`order_total_amt` DECIMAL(38,18) COMMENT '供货总金额（单位：元），取值范围：≥0',
	`take_total_amt` DECIMAL(38,18) COMMENT '提货费用（单位：元），取值范围：≥0',
	`after_sale_short_sku_cnt` BIGINT COMMENT '售后缺货商品数量，取值范围：≥0',
	`after_sale_short_sku_weight` DECIMAL(38,18) COMMENT '售后缺货商品总毛重（单位：斤），取值范围：≥0',
	`after_sale_short_total_amt` DECIMAL(38,18) COMMENT '售后缺货总金额（单位：元），取值范围：≥0',
	`after_sale_quality_sku_cnt` BIGINT COMMENT '售后质量问题商品数量，取值范围：≥0',
	`after_sale_quality_sku_weight` DECIMAL(38,18) COMMENT '售后质量问题商品总毛重（单位：斤），取值范围：≥0',
	`after_sale_quality_total_amt` DECIMAL(38,18) COMMENT '售后质量问题总金额（单位：元），取值范围：≥0',
	`commission_after_sale_amt` DECIMAL(38,18) COMMENT '售后缺货金额/(1+佣金比例)，用于计算实际佣金',
	`commission_after_sale_quality_amt` DECIMAL(38,18) COMMENT '售后质量金额/(1+佣金比例)，用于计算实际佣金'
) 
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据加载日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='供应商买手对账汇总表，记录供应商与买手之间的交易对账数据，包括订单信息、售后情况和佣金计算') 
LIFECYCLE 30;