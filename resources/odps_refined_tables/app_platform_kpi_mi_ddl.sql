CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_platform_kpi_mi` (
  `month` STRING COMMENT '月份，格式为yyyyMM',
  `origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额',
  `cust_cnt` BIGINT COMMENT '履约客户数',
  `preferential_amt` DECIMAL(38,18) COMMENT '营销金额',
  `timing_origin_amt` DECIMAL(38,18) COMMENT '省心送履约应付GMV',
  `timing_cust_cnt` BIGINT COMMENT '省心送履约客户数',
  `timing_ratio` DECIMAL(38,18) COMMENT '省心送合单率，取值范围0-1',
  `login_uv` BIGINT COMMENT '登陆UV',
  `order_uv` BIGINT COMMENT '交易客户数',
  `activity_uv` BIGINT COMMENT '特价点击UV',
  `activity_order_uv` BIGINT COMMENT '特价下单人数',
  `exchange_uv` BIGINT COMMENT '换购点击UV',
  `exchange_order_uv` BIGINT COMMENT '换购下单人数',
  `expand_uv` BIGINT COMMENT '拓展购买点击UV',
  `expand_order_uv` BIGINT COMMENT '拓展购买下单人数',
  `meeting_uv` BIGINT COMMENT '会场活动页点击UV',
  `meeting_order_uv` BIGINT COMMENT '会场活动页下单人数',
  `other_uv` BIGINT COMMENT '其他点击UV',
  `other_order_uv` BIGINT COMMENT '其他下单人数'
)
COMMENT '平台KPI指标表，包含履约金额、客户数、营销金额、省心送相关指标、各类UV和下单人数等核心业务指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，yyyyMMdd格式，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES ('comment'='平台KPI指标表，用于监控和分析平台核心业务表现')
LIFECYCLE 30;