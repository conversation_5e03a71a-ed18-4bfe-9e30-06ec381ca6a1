CREATE TABLE IF NOT EXISTS app_saas_safe_stock_warning_day_df(
    `day_tag` STRING COMMENT '日期标签，格式为yyyyMMdd，表示数据所属的日期',
    `warehouse_no` BIGINT COMMENT '库存仓ID，唯一标识一个仓库',
    `warehouse_name` STRING COMMENT '仓库名称',
    `warehouse_provider` STRING COMMENT '仓库服务商名称',
    `pd_id` BIGINT COMMENT '货品编码，唯一标识一个货品',
    `sku` STRING COMMENT 'SKU编号，库存量单位编码',
    `saas_sku_id` BIGINT COMMENT 'SaaS系统中的SKU ID',
    `category_id` BIGINT COMMENT '商品类目ID',
    `sku_tenant_id` BIGINT COMMENT 'SKU所属租户ID',
    `warehouse_tenant_id` BIGINT COMMENT '仓库所属租户ID',
    `quantity` BIGINT COMMENT '期末库存数量',
    `stock_level_minimum` BIGINT COMMENT '安全库存下限阈值',
    `stock_level_maximum` BIGINT COMMENT '安全库存上限阈值',
    `status` BIGINT COMMENT '库存状态：1-正常（库存量在安全范围内）、2-低库存（库存量低于安全下限）、3-高库存（库存量高于安全上限）',
    `use_flag` BIGINT COMMENT '使用标志：0-停用、1-启用'
)
COMMENT '按天汇总的SaaS安全库存预警数据表，用于监控和管理库存安全水平'
PARTITIONED BY (
    `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据处理的日期'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = 'SaaS安全库存预警日汇总表，包含库存状态监控和预警信息',
    'columnar.nested.type' = 'true'
)
LIFECYCLE 30;