CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_sale_city_kpi_trade_wi` (
  `year` STRING COMMENT '年份，格式：YYYY',
  `week_of_year` BIGINT COMMENT '周数，取值范围：1-53',
  `monday` STRING COMMENT '周一日期，格式：YYYYMMDD（年月日）',
  `sunday` STRING COMMENT '周日日期，格式：YYYYMMDD（年月日）',
  `administrative_city` STRING COMMENT '注册行政城市',
  `zone_name` STRING COMMENT '销售区域名称',
  `m1` STRING COMMENT '城市负责人（M1）',
  `m2` STRING COMMENT '区域负责人（M2）',
  `m3` STRING COMMENT '部门负责人（M3）',
  `order_origin_total_amt` DECIMAL(38,18) COMMENT '交易应付总金额',
  `order_real_total_amt` DECIMAL(38,18) COMMENT '交易实付总金额',
  `order_cust_cnt` BIGINT COMMENT '交易客户数',
  `order_cust_arpu` DECIMAL(38,18) COMMENT 'ARPU(应付总金额/客户数)',
  `order_cnt` BIGINT COMMENT '交易订单数',
  `delivery_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额',
  `delivery_real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额',
  `delivery_cust_cnt` BIGINT COMMENT '履约活跃客户数',
  `delivery_origin_profit` DECIMAL(38,18) COMMENT '履约应付毛利润',
  `delivery_real_profit` DECIMAL(38,18) COMMENT '履约实付毛利润',
  `delivery_after_profit` DECIMAL(38,18) COMMENT '履约后毛利润',
  `delivery_days_avg` DECIMAL(38,18) COMMENT '履约频次',
  `delivery_point_cnt` BIGINT COMMENT '履约点位数',
  `delivery_amt` DECIMAL(38,18) COMMENT '自营履约费用',
  `new_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '新客履约应付总金额',
  `new_delivery_real_total_amt` DECIMAL(38,18) COMMENT '新客履约实付总金额',
  `new_delivery_cust_cnt` BIGINT COMMENT '新客履约活跃客户数',
  `new_delivery_real_profit` DECIMAL(38,18) COMMENT '新客履约实付毛利润',
  `old_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '老客履约应付总金额',
  `old_delivery_real_total_amt` DECIMAL(38,18) COMMENT '老客履约实付总金额',
  `old_delivery_cust_cnt` BIGINT COMMENT '老客履约活跃客户数',
  `old_delivery_real_profit` DECIMAL(38,18) COMMENT '老客履约实付毛利润',
  `at_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '(乳品)安佳铁塔履约应付总金额',
  `at_delivery_real_total_amt` DECIMAL(38,18) COMMENT '(乳品)安佳铁塔履约实付总金额',
  `at_delivery_cust_cnt` BIGINT COMMENT '(乳品)安佳铁塔履约活跃客户数',
  `at_delivery_real_profit` DECIMAL(38,18) COMMENT '(乳品)安佳铁塔履约实付毛利润',
  `noat_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '(乳品)非安佳铁塔履约应付总金额',
  `noat_delivery_real_total_amt` DECIMAL(38,18) COMMENT '(乳品)非安佳铁塔履约实付总金额',
  `noat_delivery_cust_cnt` BIGINT COMMENT '(乳品)非安佳铁塔履约活跃客户数',
  `noat_delivery_real_profit` DECIMAL(38,18) COMMENT '(乳品)非安佳铁塔履约实付毛利润',
  `timing_delivery_origin_total_amt` DECIMAL(38,18) COMMENT '省心送履约应付总金额',
  `timing_delivery_real_total_amt` DECIMAL(38,18) COMMENT '省心送履约实付总金额',
  `timing_delivery_cust_cnt` BIGINT COMMENT '省心送履约活跃客户数',
  `timing_delivery_real_profit` DECIMAL(38,18) COMMENT '省心送履约实付毛利润',
  `order_sku_cnt` BIGINT COMMENT '交易SKU款数',
  `order_sku_weight` DECIMAL(38,18) COMMENT '交易SKU重量（KG）',
  `delivery_sku_cnt` BIGINT COMMENT '履约SKU款数',
  `delivery_sku_weight` DECIMAL(38,18) COMMENT '履约SKU重量（KG）'
)
COMMENT '销售KPI指标汇总表，按城市、周维度统计交易和履约相关指标，包含新老客、乳品品类、省心送等细分维度数据'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：YYYYMMDD（年月日）'
)
STORED AS ALIORC
TBLPROPERTIES ('comment'='销售KPI指标汇总表，用于销售业绩分析和监控')
LIFECYCLE 30;