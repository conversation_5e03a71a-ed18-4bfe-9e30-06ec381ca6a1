CREATE TABLE IF NOT EXISTS app_m1_sales_performance_mi(
	months STRING COMMENT '月份，格式为yyyyMM，如202509表示2025年9月',
	m1 STRING COMMENT 'M1管理者姓名，即销售主管',
	m2 STRING COMMENT 'M2管理者姓名，即销售经理，M1的直接上级',
	m3 STRING COMMENT 'M3管理者姓名，即销售总监，M2的直接上级',
	zone_name STRING COMMENT '销售区域名称，如东莞、佛山、厦门等',
	region STRING COMMENT '销售大区名称，如华南大区、闽桂大区、西南大区等',
	category STRING COMMENT '商品类目，取值范围：鲜果、蔬菜、其他',
	gmv_target DECIMAL(38,18) COMMENT '实付GMV目标金额，单位：元',
	real_total_amt DECIMAL(38,18) COMMENT '累计实付GMV金额，单位：元',
	real_total_amt_last_1d DECIMAL(38,18) COMMENT '当天实付GMV金额，单位：元',
	real_total_amt_last_2d DECIMAL(38,18) COMMENT '前一天实付GMV金额，单位：元',
	time_coming_rate DECIMAL(38,18) COMMENT '时间进度完成率，取值范围0-1的小数'
) 
COMMENT '平台销售M1维度达成表现表，记录各M1管理者的销售业绩达成情况'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='M1销售业绩达成表，包含M1管理者的销售目标、实际完成情况、时间进度等指标') 
LIFECYCLE 30;