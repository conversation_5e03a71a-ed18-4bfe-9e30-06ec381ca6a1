```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_area_warehouse_cost_di` (
  `service_area` STRING COMMENT '服务区域，枚举值：华东、贵阳、华西、华中',
  `warehosue_no` BIGINT COMMENT '仓库编号，唯一标识每个仓库',
  `warehosue_name` STRING COMMENT '仓库名称',
  `in_quality` BIGINT COMMENT '入库件数，统计周期内入库的商品数量',
  `out_quality` BIGINT COMMENT '出库件数，统计周期内出库的商品数量',
  `on_quality` BIGINT COMMENT '在库件数，统计时点在库的商品数量',
  `storage_amt` DECIMAL(38,18) COMMENT '仓储成本，仓库运营产生的存储费用（元）',
  `delivery_amt` DECIMAL(38,18) COMMENT '配送GMV，通过该仓库配送产生的商品交易总额（元）'
) 
COMMENT '库存仓维度仓储成本表，按区域和仓库统计仓储运营成本和配送交易额'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment'='按区域和仓库维度统计的仓储成本与配送GMV数据表',
  'lifecycle'='30'
);
```