CREATE TABLE IF NOT EXISTS app_stc_kpi_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd',
	`check_sku_cnt` BIGINT COMMENT '抽检数量，取值范围：非负整数',
	`in_bound_sku_cnt` BIGINT COMMENT '入库数量，取值范围：非负整数',
	`check_rate` DECIMAL(38,18) COMMENT '抽检比例，取值范围：0-1之间的小数',
	`back_order_cnt` BIGINT COMMENT '退货总单数，取值范围：非负整数',
	`finish_order_cnt` BIGINT COMMENT '已完成单数，取值范围：非负整数',
	`back_finish_rate` DECIMAL(38,18) COMMENT '退货完结率，取值范围：0-1之间的小数',
	`damage_amt` DECIMAL(38,18) COMMENT '货损金额（元），取值范围：非负数',
	`damage_amt_wah` DECIMAL(38,18) COMMENT '货损金额——仓配责（元），取值范围：非负数',
	`sale_amt` DECIMAL(38,18) COMMENT '销售金额（元），取值范围：非负数',
	`error_sku_cnt` BIGINT COMMENT '错误件数，取值范围：非负整数',
	`error_sku_cnt_wah` BIGINT COMMENT '错误件数_仓配责，取值范围：非负整数',
	`error_cust_cnt` BIGINT COMMENT '错误客户数，取值范围：非负整数',
	`error_cust_cnt_wah` BIGINT COMMENT '错误客户数_仓配责，取值范围：非负整数',
	`cust_cnt` BIGINT COMMENT '活跃客户数，取值范围：非负整数',
	`sku_cnt` BIGINT COMMENT '配送件数，取值范围：非负整数',
	`total_point_cnt` BIGINT COMMENT '总点位数，取值范围：非负整数',
	`point_cnt` BIGINT COMMENT '点位数（不含喜茶），取值范围：非负整数',
	`no_in_time_point_cnt` BIGINT COMMENT '不及时点位数（不含喜茶），取值范围：非负整数',
	`delay_time_point_cnt_2` BIGINT COMMENT '前置2小时不及时点位数（不含喜茶），取值范围：非负整数',
	`out_time` DECIMAL(38,18) COMMENT '配送超时时间（不含喜茶，小时），取值范围：非负数',
	`delay_time_2` DECIMAL(38,18) COMMENT '前置2小时配送超时时间（不含喜茶，小时），取值范围：非负数',
	`path_cnt` BIGINT COMMENT '线路数（不含喜茶），取值范围：非负整数',
	`delay_path_cnt` BIGINT COMMENT '出库不及时线路数（不含喜茶），取值范围：非负整数',
	`out_distance_point_cnt` BIGINT COMMENT '超距离点位数（含喜茶），取值范围：非负整数',
	`after_sale_amt` DECIMAL(38,18) COMMENT '售后总金额（元），取值范围：非负数',
	`after_sale_amt_wah` DECIMAL(38,18) COMMENT '售后金额__仓配责（元），取值范围：非负数',
	`after_sale_amt_pur` DECIMAL(38,18) COMMENT '售后金额__采购责（元），取值范围：非负数',
	`after_sale_amt_che` DECIMAL(38,18) COMMENT '售后金额__品控责（元），取值范围：非负数',
	`after_sale_amt_pur_che` DECIMAL(38,18) COMMENT '售后金额__采购品控责（元），取值范围：非负数',
	`after_sale_amt_oth` DECIMAL(38,18) COMMENT '售后金额__其他责（元），取值范围：非负数',
	`delivery_total_amt` DECIMAL(38,18) COMMENT '配送销售金额（元），取值范围：非负数',
	`coupon_amt` DECIMAL(38,18) COMMENT '优惠券金额（元），取值范围：非负数',
	`origin_total_amt` DECIMAL(38,18) COMMENT '应付GMV（元），取值范围：非负数',
	`real_total_amt` DECIMAL(38,18) COMMENT '实付GMV（元），取值范围：非负数',
	`storage_amt` DECIMAL(38,18) COMMENT '仓储成本（元），取值范围：非负数',
	`arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本（元），取值范围：非负数',
	`deliver_amt` DECIMAL(38,18) COMMENT '配送成本（元），取值范围：非负数',
	`self_picked_amt` DECIMAL(38,18) COMMENT '采购自提成本（元），取值范围：非负数',
	`other_amt` DECIMAL(38,18) COMMENT '其他成本（元），取值范围：非负数',
	`allocation_amt` DECIMAL(38,18) COMMENT '调拨成本（元），取值范围：非负数',
	`cost_amt` DECIMAL(38,18) COMMENT '商品成本（元），取值范围：非负数'
) 
COMMENT '仓配KPI汇总表，包含仓储配送相关的关键绩效指标数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='仓配KPI汇总表，用于监控和分析仓储配送业务的各项关键指标') 
LIFECYCLE 30;