```sql
CREATE TABLE IF NOT EXISTS app_pcs_direct_category_warehouse_purchase_kpi_di(
    `date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务发生日期',
    `warehouse_no` BIGINT COMMENT '库存仓号，取值范围：2-155，常见值包括62、125、150、155等',
    `warehouse_name` STRING COMMENT '库存仓名称，如：苏州总仓、南京总仓、武汉总仓、嘉兴水果批发总仓等',
    `category4` STRING COMMENT '四级类目名称，如：西瓜、柚、奇异果丨猕猴桃、橙、恐龙蛋李等水果品类',
    `direct_purchase_amt` DECIMAL(38,18) COMMENT '直采金额，单位：元，精度保留18位小数',
    `purchases_amt` DECIMAL(38,18) COMMENT '采购金额（非直采），单位：元，精度保留18位小数',
    `cost_flow_amt` DECIMAL(38,18) COMMENT '成本浮动金额，单位：元，精度保留18位小数',
    `direct_delivery_origin_amt` DECIMAL(38,18) COMMENT '直采履约应付金额，单位：元，精度保留18位小数',
    `direct_delivery_real_amt` DECIMAL(38,18) COMMENT '直采履约实付金额，单位：元，精度保留18位小数',
    `direct_delivery_market_amt` DECIMAL(38,18) COMMENT '直采营销费用，单位：元，精度保留18位小数',
    `direct_delivery_cost_amt` DECIMAL(38,18) COMMENT '直采成本费用，单位：元，精度保留18位小数',
    `direct_init_amt` DECIMAL(38,18) COMMENT '直采期末库存金额，单位：元，精度保留18位小数',
    `direct_after_sale_pcs_amt` DECIMAL(38,18) COMMENT '直采采购责售后金额，单位：元，精度保留18位小数',
    `direct_damage_pcs_amt` DECIMAL(38,18) COMMENT '直采采购责货损金额，单位：元，精度保留18位小数'
) 
COMMENT '直采KPI指标表，按日期、仓库、四级类目维度统计直采相关的采购金额、履约金额、成本费用、库存金额等关键绩效指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据加载日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
    'comment'='直采业务KPI统计表，用于监控和分析直采业务的财务表现和运营效率') 
LIFECYCLE 30;
```