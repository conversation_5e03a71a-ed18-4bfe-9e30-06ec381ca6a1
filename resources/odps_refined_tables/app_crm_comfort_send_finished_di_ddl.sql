CREATE TABLE IF NOT EXISTS app_crm_comfort_send_finished_di(
	`m_id` BIGINT COMMENT '商户ID，唯一标识商户',
	`order_no` STRING COMMENT '订单编号，唯一标识订单',
	`order_time` DATETIME COMMENT '订单生成时间，格式为年月日时分秒',
	`sku_id` STRING COMMENT 'SKU编码，商品库存单位标识',
	`pd_name` STRING COMMENT '商品名称',
	`pd_weight` STRING COMMENT '商品规格，描述商品重量或尺寸信息',
	`pd_amount` BIGINT COMMENT '订单内商品数量，取值范围：2-240',
	`pay_amount` DECIMAL(38,18) COMMENT '实付总额，订单实际支付金额',
	`area_no` BIGINT COMMENT '商户所在运营区域编号，取值范围：1001-44237',
	`bd_id` BIGINT COMMENT '归属BD ID，标识负责该商户的业务发展人员，公海为0，取值范围：0-1189140',
	`day_tag` STRING COMMENT '数据所在日标记，格式为yyyyMMdd',
	`m_name` STRING COMMENT '商户名称',
	`province` STRING COMMENT '省份名称',
	`city` STRING COMMENT '城市名称',
	`area` STRING COMMENT '区县名称'
) 
COMMENT 'CRM近30天配送完成省心送订单表，记录已完成配送的省心送订单信息'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='CRM近30天配送完成省心送订单表，用于分析省心送订单的配送完成情况') 
LIFECYCLE 30;