```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_dlv_area_kpi_mi` (
  `month` STRING COMMENT '月份，格式：yyyyMM',
  `area_name` STRING COMMENT '城配仓名称',
  `total_point_cnt` BIGINT COMMENT '总点位数（包含所有品牌）',
  `point_cnt` BIGINT COMMENT '点位数（不含喜茶）',
  `no_in_time_point_cnt` BIGINT COMMENT '不及时点位数（不含喜茶）',
  `delay_time_point_cnt_2` BIGINT COMMENT '前置2小时不及时点位数（不含喜茶）',
  `out_time` DECIMAL(38,18) COMMENT '配送超时时间（小时，不含喜茶）',
  `delay_time_2` DECIMAL(38,18) COMMENT '前置2小时配送超时时间（小时，不含喜茶）',
  `path_cnt` BIGINT COMMENT '线路数（不含喜茶）',
  `delay_path_cnt` BIGINT COMMENT '出库不及时线路数（不含喜茶）',
  `out_distance_point_cnt` BIGINT COMMENT '超距离点位数（含喜茶）',
  `after_sale_amt` DECIMAL(38,18) COMMENT '售后总金额（元）',
  `after_sale_amt_wah` DECIMAL(38,18) COMMENT '售后金额__仓配责（元）',
  `after_sale_amt_pur` DECIMAL(38,18) COMMENT '售后金额__采购责（元）',
  `after_sale_amt_che` DECIMAL(38,18) COMMENT '售后金额__品控责（元）',
  `after_sale_amt_pur_che` DECIMAL(38,18) COMMENT '售后金额__采购品控责（元）',
  `after_sale_amt_oth` DECIMAL(38,18) COMMENT '售后金额__其他责（元）',
  `delivery_total_amt` DECIMAL(38,18) COMMENT '配送销售金额（元）',
  `coupon_amt` DECIMAL(38,18) COMMENT '优惠券金额（元）',
  `sku_cnt` BIGINT COMMENT '配送件数',
  `error_sku_cnt` BIGINT COMMENT '错误件数',
  `cust_cnt` BIGINT COMMENT '活跃客户数',
  `error_cust_cnt` BIGINT COMMENT '错误客户数'
)
COMMENT '仓配KPI配送数据汇总表，包含各城配仓的配送时效、售后金额、配送量等关键指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '仓配KPI配送数据汇总表，用于监控和分析各城配仓的配送服务质量',
  'last_data_modified_time' = '2025-09-18 03:01:01'
)
LIFECYCLE 30;
```