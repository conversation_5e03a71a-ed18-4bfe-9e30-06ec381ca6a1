```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_crm_team_performance_wi` (
  `year` STRING COMMENT '年份，格式：YYYY',
  `week_of_year` BIGINT COMMENT '周数，取值范围：1-53',
  `monday` STRING COMMENT '周一日期，格式：YYYYMMDD（年月日）',
  `sunday` STRING COMMENT '周日日期，格式：YYYYMMDD（年月日）',
  `cust_team` STRING COMMENT '客户团队类型，枚举值：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、SAAS大客户',
  `order_origin_total_amt` DECIMAL(38,18) COMMENT '交易应付总金额（元）',
  `order_real_total_amt` DECIMAL(38,18) COMMENT '交易实付总金额（元）',
  `order_brand_cnt` BIGINT COMMENT '交易公司数',
  `order_cust_cnt` BIGINT COMMENT '交易门店数',
  `order_brandalias_cnt` BIGINT COMMENT '交易品牌数',
  `order_order_cnt` BIGINT COMMENT '交易订单数',
  `new_cust_cnt` BIGINT COMMENT '活跃门店数中新增门店数',
  `new_cust_gmv` DECIMAL(38,18) COMMENT '新增活跃门店GMV（元）',
  `close_cust_cnt` BIGINT COMMENT '活跃门店数中倒闭门店数',
  `close_cust_gmv` DECIMAL(38,18) COMMENT '倒闭门店GMV（元）',
  `old_cust_cnt` BIGINT COMMENT '老活跃门店数',
  `old_cust_gmv` DECIMAL(38,18) COMMENT '老活跃门店GMV（元）',
  `new_noactive_cust_cnt` BIGINT COMMENT '拉新门店数（仅注册未下单）',
  `new_active_cust_cnt` BIGINT COMMENT '拉新门店数（注册且下单）',
  `new_active_gmv` DECIMAL(38,18) COMMENT '拉新门店GMV（元）',
  `order_replace_cust_cnt` BIGINT COMMENT '代下单门店数',
  `order_replace_gmv` DECIMAL(38,18) COMMENT '代下单实付金额（元）',
  `origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额（元）',
  `real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额（元）',
  `cost_amt` DECIMAL(38,18) COMMENT '履约成本（元）',
  `order_cnt` BIGINT COMMENT '履约订单数',
  `sku_cnt` BIGINT COMMENT '总配送件数',
  `point_cnt` BIGINT COMMENT '总点位数',
  `cust_cnt` BIGINT COMMENT '履约门店数',
  `brand_cnt` BIGINT COMMENT '履约公司数',
  `brandalias_cnt` BIGINT COMMENT '履约品牌数',
  `self_real_total_amt` DECIMAL(38,18) COMMENT '自营实付总金额（元）',
  `self_cost_amt` DECIMAL(38,18) COMMENT '自营成本（元）',
  `self_cust_cnt` BIGINT COMMENT '自营品牌门店数',
  `self_brand_cnt` BIGINT COMMENT '自营品牌公司数',
  `self_brandalias_cnt` BIGINT COMMENT '自营品牌品牌数',
  `timing_real_total_amt` DECIMAL(38,18) COMMENT '省心送实付总金额（元）',
  `timing_cost_amt` DECIMAL(38,18) COMMENT '省心送成本（元）',
  `timing_cust_cnt` BIGINT COMMENT '省心送门店数',
  `timing_brand_cnt` BIGINT COMMENT '省心送公司数',
  `timing_brandalias_cnt` BIGINT COMMENT '省心送品牌数',
  `fruit_real_total_amt` DECIMAL(38,18) COMMENT '鲜果实付总金额（元）',
  `fruit_cost_amt` DECIMAL(38,18) COMMENT '鲜果成本（元）',
  `fruit_cash_real_total_amt` DECIMAL(38,18) COMMENT '鲜果账期实付金额（元）',
  `fruit_cust_cnt` BIGINT COMMENT '鲜果门店数',
  `dairy_real_total_amt` DECIMAL(38,18) COMMENT '乳制品实付总金额（元）',
  `dairy_cost_amt` DECIMAL(38,18) COMMENT '乳制品成本（元）',
  `dairy_cash_real_cost_amt` DECIMAL(38,18) COMMENT '乳制品账期实付金额（元）',
  `dairy_cust_cnt` BIGINT COMMENT '乳制品门店数',
  `nodairy_real_total_amt` DECIMAL(38,18) COMMENT '非乳制品实付总金额（元）',
  `nodairy_cost_amt` DECIMAL(38,18) COMMENT '非乳制品成本（元）',
  `nodairy_cash_cost_amt` DECIMAL(38,18) COMMENT '非乳制品账期实付金额（元）',
  `nodairy_cust_cnt` BIGINT COMMENT '非乳制品门店数',
  `cash_real_total_amt` DECIMAL(38,18) COMMENT '账期实付金额（元）',
  `nocash_real_total_amt` DECIMAL(38,18) COMMENT '非账期实付金额（元）',
  `replace_origin_total_amt` DECIMAL(38,18) COMMENT '代仓应付总金额（元）',
  `replace_real_total_amt` DECIMAL(38,18) COMMENT '代仓实付总金额（元）',
  `replace_cust_cnt` BIGINT COMMENT '代仓门店数',
  `self_after_sale_amt` DECIMAL(38,18) COMMENT '自营售后金额（元）',
  `replace_after_sale_amt` DECIMAL(38,18) COMMENT '代仓售后金额（元）'
) 
COMMENT '大客户团队周度业绩汇总表，按周统计各客户团队的交易、履约、自营、省心送、鲜果、乳制品、非乳制品等业务指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式：YYYYMMDD（年月日）'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment'='大客户团队业绩分析表，用于团队绩效评估和业务分析',
  'columnar.nested.type'='true'
) 
LIFECYCLE 30;
```