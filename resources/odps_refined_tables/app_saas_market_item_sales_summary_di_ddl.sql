```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_saas_market_item_sales_summary_di`(
  `tenant_id` BIGINT COMMENT '租户ID，取值范围：8-123',
  `time_tag` STRING COMMENT '时间标签，格式：yyyyMMdd，表示年月日',
  `pay_type` BIGINT COMMENT '支付方式：1-微信支付，2-账期支付，3-余额支付，4-其他支付方式',
  `item_id` BIGINT COMMENT '商品ID，取值范围：326-44850',
  `item_code` STRING COMMENT '商品自有编码，可为空或None',
  `item_title` STRING COMMENT '商品标题',
  `item_specification` STRING COMMENT '商品规格描述',
  `total_amount` BIGINT COMMENT '商品销售数量，取值范围：0-203',
  `average_payable_price` DECIMAL(38,18) COMMENT '平均售卖单价（元）',
  `total_price` DECIMAL(38,18) COMMENT '售卖总价（元）',
  `total_refund_price` DECIMAL(38,18) COMMENT '售后总金额（元）',
  `total_price_deducted_refund` DECIMAL(38,18) COMMENT '扣除售后的售卖金额（元）',
  `good_average_supply_price` DECIMAL(38,18) COMMENT '货品平均采购单价（元）',
  `goods_total_supply_price` DECIMAL(38,18) COMMENT '货品采购总价（元）',
  `sales_and_supply_difference_deducted_price` DECIMAL(38,18) COMMENT '销售与采购差额（剔除售后）（元）',
  `goods_refund_price` DECIMAL(38,18) COMMENT '等比换算后采购售后金额（元）',
  `goods_price_deducted_refund` DECIMAL(38,18) COMMENT '扣除售后的采购金额（元）',
  `supplier_id` BIGINT COMMENT '供应商ID，取值范围：0-3404，0表示无供应商'
) 
COMMENT 'SAAS对账单-商品汇总表（近10天数据），包含商品销售、退款、采购等财务汇总信息'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式：yyyyMMdd，表示年月日'
)
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
               'comment'='SAAS对账单-商品汇总表（近10天数据），用于商品维度的销售和财务分析') 
LIFECYCLE 30;
```