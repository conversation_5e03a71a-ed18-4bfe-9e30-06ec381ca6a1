CREATE TABLE IF NOT EXISTS app_kpi_operate_delivery_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务发生日期',
	`origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV，订单原始总金额',
	`real_total_amt` DECIMAL(38,18) COMMENT '履约实付GMV，实际支付总金额',
	`marketing_amt` DECIMAL(38,18) COMMENT '营销费用，用于营销活动的费用支出',
	`cost_amt` DECIMAL(38,18) COMMENT '成本金额，业务运营的直接成本',
	`origin_gross` DECIMAL(38,18) COMMENT '应付毛利润，原始金额计算的毛利润',
	`real_gross` DECIMAL(38,18) COMMENT '实付毛利润，实际支付金额计算的毛利润',
	`origin_gross_margin` DECIMAL(38,18) COMMENT '应付毛利率，原始金额计算的毛利率百分比',
	`real_gross_margin` DECIMAL(38,18) COMMENT '实付毛利率，实际支付金额计算的毛利率百分比',
	`cust_cnt` BIGINT COMMENT '履约客户数，完成履约的客户数量',
	`point_cnt` BIGINT COMMENT '点位数，业务覆盖的服务点数量',
	`origin_pre_cust_price` DECIMAL(38,18) COMMENT '应付客单价，原始金额计算的每客户平均消费',
	`real_pre_cust_price` DECIMAL(38,18) COMMENT '实付客单价，实际支付金额计算的每客户平均消费',
	`timing_origin_amt` DECIMAL(38,18) COMMENT '省心送应付GMV，定时配送服务的原始金额',
	`timing_real_amt` DECIMAL(38,18) COMMENT '省心送实付GMV，定时配送服务的实际支付金额',
	`consign_origin_amt` DECIMAL(38,18) COMMENT '代售应付GMV，代售服务的原始金额',
	`consign_real_amt` DECIMAL(38,18) COMMENT '代售实付GMV，代售服务的实际支付金额',
	`consign_marketing_amt` DECIMAL(38,18) COMMENT '代售营销费用，代售服务的营销费用',
	`consign_origin_gross` DECIMAL(38,18) COMMENT '代售应付毛利润，代售服务的原始毛利润',
	`consign_real_gross` DECIMAL(38,18) COMMENT '代售实付毛利润，代售服务的实际毛利润',
	`consign_cust_cnt` BIGINT COMMENT '代售履约客户数，代售服务完成的客户数量，取值范围：非负整数',
	`turnover_day_cnt` DECIMAL(38,18) COMMENT '周转天数，库存周转的平均天数',
	`damage_amt` DECIMAL(38,18) COMMENT '货损金额，货物损坏造成的损失金额',
	`storage_amt` DECIMAL(38,18) COMMENT '仓储费，仓储服务的费用支出',
	`arterial_roads_amt` DECIMAL(38,18) COMMENT '单点干线费，干线运输的单点费用',
	`self_picked_amt` DECIMAL(38,18) COMMENT '采购自提费，自提服务的费用',
	`allocation_amt` DECIMAL(38,18) COMMENT '调拨费，货物调拨产生的费用',
	`other_amt` DECIMAL(38,18) COMMENT '其他费，其他未分类的费用支出',
	`deliver_amt` DECIMAL(38,18) COMMENT '配送费，配送服务的费用支出'
) 
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据加载日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='运营履约KPI指标表（平台客户），包含履约业务的各项关键绩效指标数据') 
LIFECYCLE 30;