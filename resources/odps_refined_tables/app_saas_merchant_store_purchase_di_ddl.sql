```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_saas_merchant_store_purchase_di`(
  `tenant_id` BIGINT COMMENT '租户ID，唯一标识一个租户',
  `type` BIGINT COMMENT '时间标签类型：1-日 2-周 3-月',
  `time_tag` STRING COMMENT '时间标签，格式为yyyyMMdd，表示年月日',
  `pay_type` BIGINT COMMENT '支付方式：1-现结 2-账期',
  `store_in_operation_num` BIGINT COMMENT '经营中门店总数',
  `direct_store_in_operation_num` BIGINT COMMENT '经营中直营门店数',
  `join_store_in_operation_num` BIGINT COMMENT '经营中加盟门店数',
  `managed_store_in_operation_num` BIGINT COMMENT '经营中托管门店数',
  `purchased_store_num` BIGINT COMMENT '采购门店总数',
  `purchased_direct_store_num` BIGINT COMMENT '采购直营门店数',
  `purchased_join_store_num` BIGINT COMMENT '采购加盟门店数',
  `purchased_managed_store_num` BIGINT COMMENT '采购托管门店数',
  `last_purchased_store_num` BIGINT COMMENT '上周期采购门店总数',
  `purchased_store_chain` DECIMAL(38,18) COMMENT '采购门店数环比增长率，百分比形式',
  `last_purchased_direct_store_num` BIGINT COMMENT '上周期采购直营门店数',
  `purchased_direct_store_chain` DECIMAL(38,18) COMMENT '采购直营门店数环比增长率，百分比形式',
  `last_purchased_join_store_num` BIGINT COMMENT '上周期采购加盟门店数',
  `purchased_join_store_chain` DECIMAL(38,18) COMMENT '采购加盟门店数环比增长率，百分比形式',
  `last_purchased_managed_store_num` BIGINT COMMENT '上周期采购托管门店数',
  `purchased_managed_store_chain` DECIMAL(38,18) COMMENT '采购托管门店数环比增长率，百分比形式'
) 
COMMENT 'SaaS门店采购概况表，统计各租户不同时间维度下的门店采购情况，包括门店类型分布和环比增长分析'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='SaaS门店采购概况表，用于分析门店采购趋势和业务表现') 
LIFECYCLE 30;
```