CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_brand_trade_delivery_after_sale_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd',
  `order_source` STRING COMMENT '订单来源：ALL-全部,鲜沐-鲜沐平台,SaaS-SaaS平台',
  `category` STRING COMMENT '商品品类：ALL-全部,乳制品-乳制品类,鲜果-新鲜水果,其他-其他品类',
  `sku_type` STRING COMMENT '商品类型：ALL-全部商品,自营-自营商品,全品类-全品类商品',
  `brand_id` BIGINT COMMENT '品牌唯一标识ID',
  `brand_alias` STRING COMMENT '品牌别名/品牌名称',
  `is_self_owned_brand` STRING COMMENT '是否自营品牌：是-自营品牌,否-非自营品牌',
  `business_name` STRING COMMENT '企业工商注册名称',
  `cust_group` STRING COMMENT '客户分组类型：大客户-大客户类型,平台客户-平台客户类型,批发客户-批发客户类型',
  `brand_grade` STRING COMMENT '品牌等级：普通-普通品牌,KA1-KA1级别,KA2-KA2级别,KA3-KA3级别,区域销售-区域销售品牌',
  `sale_name` STRING COMMENT '所属销售人员姓名',
  `operate_name` STRING COMMENT '所属运营人员姓名',
  `collaboration_mode` STRING COMMENT '合作模式：账期-账期结算,现结-现款结算,账期&现结-混合结算模式',
  `cooperation_stage` STRING COMMENT '合作阶段：非ka客户-非KA客户,试样-样品测试阶段,报价-报价阶段,试配-试配阶段,合作稳定期-稳定合作期,合作困难期-合作困难期,流失-已流失客户,暂不合作-暂不合作',
  `trd_orign_total_amt` DECIMAL(38,18) COMMENT '交易应付GMV金额（元）',
  `trd_real_total_amt` DECIMAL(38,18) COMMENT '交易实付GMV金额（元）',
  `trd_cust_cnt` BIGINT COMMENT '交易客户数量',
  `trd_sku_cnt` BIGINT COMMENT '交易SKU数量',
  `trd_sale_cnt` BIGINT COMMENT '交易销售数量',
  `trd_sale_weight` DECIMAL(38,18) COMMENT '交易商品重量（kg）',
  `trd_order_cnt` BIGINT COMMENT '交易订单数量',
  `after_sale_noreceived_amt` DECIMAL(38,18) COMMENT '未到货售后金额（元）',
  `after_sale_noreceived_cnt` DECIMAL(38,18) COMMENT '未到货售后数量',
  `after_sale_received_amt` DECIMAL(38,18) COMMENT '到货售后金额（元）',
  `after_sale_received_cnt` DECIMAL(38,18) COMMENT '到货售后数量',
  `dlv_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV金额（元）',
  `dlv_real_total_amt` DECIMAL(38,18) COMMENT '履约实付GMV金额（元）',
  `dlv_cost_amt` DECIMAL(38,18) COMMENT '履约成本金额（元）',
  `dlv_order_cnt` BIGINT COMMENT '履约订单数量',
  `dlv_cust_cnt` BIGINT COMMENT '履约客户数量',
  `dlv_sku_cnt` BIGINT COMMENT '履约SKU数量',
  `dlv_sale_cnt` BIGINT COMMENT '履约销售数量',
  `dlv_sale_weight` DECIMAL(38,18) COMMENT '履约商品重量（kg）',
  `dlv_short_orign_total_amt` DECIMAL(38,18) COMMENT '履约缺货应付GMV金额（元）',
  `dlv_short_real_total_amt` DECIMAL(38,18) COMMENT '履约缺货实付GMV金额（元）',
  `dlv_short_cnt` BIGINT COMMENT '履约缺货次数',
  `dlv_point_cnt` BIGINT COMMENT '履约点位数量',
  `cust_team` STRING COMMENT '客户团队类型：ALL-全部,集团大客户（茶百道）-茶百道集团大客户,集团大客户（喜茶）-喜茶集团大客户,Mars大客户-Mars大客户,平台客户-平台客户,批发-批发客户',
  `after_sale_order_cnt` BIGINT COMMENT '已到货售后订单数量'
)
COMMENT '品牌交易交付售后数据表，包含品牌交易、履约交付和售后服务的完整业务数据'
PARTITIONED BY (
  `ds` STRING COMMENT '业务日期分区字段，格式为yyyyMMdd'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment'='品牌交易交付售后数据表，用于分析品牌维度的交易、履约和售后业务指标',
  'lifecycle'='30'
);