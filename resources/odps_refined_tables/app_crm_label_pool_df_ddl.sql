CREATE TABLE IF NOT EXISTS app_crm_label_pool_df(
    merchant_label STRING COMMENT '标签，枚举值包括：月活老客户、月活新客户、活跃客户、羊场客户、新客N0等',
    group_name STRING COMMENT '标签组，枚举值包括：企微标签组等',
    rank_id BIGINT COMMENT '组内排序，取值范围：1-39，用于标签在组内的显示顺序'
)
COMMENT 'CRM用户标签池，存储用户标签及其分组排序信息'
PARTITIONED BY (ds STRING COMMENT '分区字段，yyyyMMdd格式，表示数据日期')
STORED AS ALIORC
TBLPROPERTIES ('comment'='CRM用户标签池表，包含用户标签、标签组和排序信息')
LIFECYCLE 30;