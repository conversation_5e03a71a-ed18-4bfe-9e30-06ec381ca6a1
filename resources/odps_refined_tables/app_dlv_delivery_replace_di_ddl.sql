CREATE TABLE IF NOT EXISTS app_dlv_delivery_replace_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务发生日期',
	`origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额，单位为元，保留18位小数精度',
	`real_total_amt` DECIMAL(38,18) COMMENT '实付总金额，单位为元，保留18位小数精度',
	`deliver_total_weight` DECIMAL(38,18) COMMENT '配送总重量，单位为千克，保留18位小数精度',
	`brand_cnt` BIGINT COMMENT '客户数（品牌），统计品牌级别的客户数量',
	`cust_cnt` BIGINT COMMENT '门店数，统计门店级别的客户数量',
	`order_cnt` BIGINT COMMENT '订单数，统计订单数量',
	`data_source` STRING COMMENT '数据来源：鲜沐-来自鲜沐系统，SaaS-来自SaaS系统'
) 
COMMENT '履约代仓日汇总表，记录每日代仓履约业务的汇总数据，包括金额、重量、客户、订单等核心指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据加载日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='履约代仓业务日粒度汇总表，用于分析代仓履约业务的各项指标') 
LIFECYCLE 30;