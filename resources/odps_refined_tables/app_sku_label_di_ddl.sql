CREATE TABLE IF NOT EXISTS app_sku_label_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd',
	`sku_id` STRING COMMENT 'SKU编号，商品唯一标识',
	`sku_label` STRING COMMENT 'ABC标签，取值范围：A/B/C，表示商品ABC分类',
	`dlv_30_origin_amt` DECIMAL(38,18) COMMENT '近30天履约应付GMV，单位：元',
	`dlv_30_cust_cnt` BIGINT COMMENT '近30天履约客户数',
	`dlv_30_sku_cnt` BIGINT COMMENT '近30天履约数量',
	`dlv_30_gross_profit_amt` DECIMAL(38,18) COMMENT '近30天履约实付毛利润，单位：元',
	`dlv_30_gross_rate` DECIMAL(38,18) COMMENT '近30天履约实付毛利率',
	`dlv_31_60_origin_amt` DECIMAL(38,18) COMMENT '近31-60天履约应付GMV，单位：元',
	`dlv_31_60_cust_cnt` BIGINT COMMENT '近31-60天履约客户数',
	`dlv_31_60_sku_cnt` BIGINT COMMENT '近31-60天履约数量',
	`dlv_31_60_gross_profit_amt` DECIMAL(38,18) COMMENT '近31-60天履约实付毛利润，单位：元',
	`dlv_31_60_gross_rate` DECIMAL(38,18) COMMENT '近31-60天履约实付毛利率',
	`dlv_origin_growth_amount` DECIMAL(38,18) COMMENT '履约应付增长额，单位：元',
	`dlv_origin_growth_rate` DECIMAL(38,18) COMMENT '履约应付增长率',
	`dlv_cust_growth_cnt` BIGINT COMMENT '履约客户增长数',
	`dlv_cust_growth_rate` DECIMAL(38,18) COMMENT '履约客户增长率',
	`dlv_origin_profit_growth_amount` DECIMAL(38,18) COMMENT '履约毛利润增长额，单位：元',
	`dlv_real_profit_growth_rate` DECIMAL(38,18) COMMENT '履约毛利润增长率',
	`dlv_origin_growth_label` STRING COMMENT 'GMC增速标签，取值范围：高增长/其他',
	`dlv_origin_rate_label` STRING COMMENT 'GMC增率标签，取值范围：高增长/其他',
	`dlv_cust_growth_label` STRING COMMENT '客户增速标签，取值范围：高增长/其他',
	`dlv_cust_rate_label` STRING COMMENT '客户增率标签，取值范围：高增长/其他',
	`dlv_profit_growth_label` STRING COMMENT '毛利润增速标签，取值范围：高增长/锐减/其他',
	`dlv_profit_rate_label` STRING COMMENT '毛利润增率标签，取值范围：高增长/其他',
	`spu_name_list` STRING COMMENT '购物车关联TOP3商品名称列表，用下划线分隔'
) 
COMMENT '商品标签表，包含商品的ABC分类标签、履约相关指标和增长标签'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='商品标签分析表，用于商品分类和履约表现分析') 
LIFECYCLE 30;