CREATE TABLE IF NOT EXISTS app_crm_chatbi_cust_info_df(
	`cust_id` BIGINT COMMENT '客户ID，唯一标识客户',
	`sales_team_region` STRING COMMENT '客户所属的销售区域，如：浙江大区',
	`cust_m3` STRING COMMENT '客户所属的销售总监M3名字',
	`cust_m2` STRING COMMENT '客户所属的销售区域经理M2名字',
	`cust_m1` STRING COMMENT '客户所属的销售经理M1名字',
	`bd_name` STRING COMMENT '客户所属的销售BD名字，也可能是公海客户',
	`register_date` STRING COMMENT '客户注册日期，格式：yyyy-MM-dd HH:mm:ss，如：2023-04-10 13:21:29',
	`member_grade` INT COMMENT '客户的会员等级，取值范围[0,3]：0-V0，1-V1，2-V2，3-V3',
	`register_ctiy` STRING COMMENT '客户注册城市',
	`register_province` STRING COMMENT '客户注册省份',
	`large_area_name` STRING COMMENT '运营大区，如：上海大区、杭州大区',
	`cust_phone` STRING COMMENT '客户手机号码',
	`cust_name` STRING COMMENT '客户名字，如：茶百道凤中路店',
	`cust_size` STRING COMMENT '客户的规模，如：单店、大客户',
	`last_order_date` DATE COMMENT '客户最后一次下单日期，格式：yyyy-MM-dd，如：2024-01-10。如果从未下单，则为空',
	`cust_brand_name` STRING COMMENT '连锁客户的所属品牌，如：上海星巴克。如果是单店，则为空',
	`cust_brand_company_name` STRING COMMENT '连锁客户的所属品牌的公司工商注册名字，如：四川蜀味茶韵供应链有限公司。如果是单店，则为空',
	`cust_address` STRING COMMENT '客户的注册地址',
	`cust_operate_status` STRING COMMENT '客户运营状态，取值范围：0-正常，1-倒闭'
) 
COMMENT '客户信息表，包含客户的销售团队信息、会员信息、注册信息等'
PARTITIONED BY (
	`ds` STRING COMMENT '分区字段，格式：yyyyMMdd，如：20240101'
)
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='客户信息表，包含客户的销售团队信息、会员信息、注册信息等') 
LIFECYCLE 7;