```sql
CREATE TABLE IF NOT EXISTS app_saas_store_inventory_audit_di(
    report_day DATETIME COMMENT '稽核自然日，格式：年月日时分秒',
    channel_type BIGINT COMMENT '渠道类型：1=美团，2=饿了么，3=抖音，4=其他',
    tenant_id BIGINT COMMENT '租户ID',
    out_store_code STRING COMMENT '外部系统门店编码',
    out_store_name STRING COMMENT '外部系统门店名称',
    merchant_store_id BIGINT COMMENT '帆台门店ID',
    merchant_store_code STRING COMMENT '帆台门店编码',
    out_item_code STRING COMMENT '外部系统物料编码',
    out_item_name STRING COMMENT '外部系统物料名称',
    market_item_id BIGINT COMMENT '帆台商品ID',
    specification STRING COMMENT '商品规格',
    store_ordering_inventory_unit STRING COMMENT '门店订货单位',
    store_inventory_unit STRING COMMENT '门店库存单位',
    use_count DECIMAL(38,18) COMMENT '销用总量',
    need_buy_count DECIMAL(38,18) COMMENT '应进货总量',
    real_buy_count DECIMAL(38,18) COMMENT '实际帆台进货总量',
    inventory_check_before_count DECIMAL(38,18) COMMENT '盘前数量',
    inventory_check_after_count DECIMAL(38,18) COMMENT '盘后数量'
)
COMMENT '门店库存进销稽核日表：用于记录门店每日库存进销存数据的稽核结果'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式：yyyyMMdd')
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = '门店库存进销稽核日表，包含门店商品库存的进销存稽核数据',
    'lifecycle' = '30'
);
```