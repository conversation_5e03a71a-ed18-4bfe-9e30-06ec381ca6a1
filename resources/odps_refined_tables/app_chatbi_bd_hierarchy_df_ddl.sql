CREATE TABLE IF NOT EXISTS app_chatbi_bd_hierarchy_df(
	bd_id STRING COMMENT 'BD ID，销售人员的唯一标识',
	bd_name STRING COMMENT 'BD姓名，销售人员的真实姓名',
	m1_name STRING COMMENT 'M1管理者（销售主管）姓名，即BD的直接上级',
	m2_name STRING COMMENT 'M2管理者（销售经理）姓名，即M1的直接上级',
	m3_name STRING COMMENT 'M3管理者（销售总监）姓名，即M2的直接上级',
	is_valid_bd BIGINT COMMENT 'BD有效性标识：1-有效BD（近30天跟进记录>5次且是普通BD），0-无效BD（可能是离职了，也可能是测试BD，也可能是管理者）。取值范围：0-无效，1-有效',
	is_m1_manager BIGINT COMMENT '该BD是否是一个合法的M1管理者（有别于普通BD）。取值范围：0-否，1-是',
	is_m2_manager BIGINT COMMENT '该BD是否是一个合法的M2管理者（有别于普通BD）。取值范围：0-否，1-是',
	is_m3_manager BIGINT COMMENT '该BD是否是一个合法的M3管理者（有别于普通BD）。取值范围：0-否，1-是'
) 
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='BD层级结果表，包含BD的完整层级关系和基于跟进记录的有效性判断。统计信息：总记录数244条，有效BD占比56.15%，M1管理者占比9.43%，M2管理者占比3.28%，M3管理者占比0.82%') 
LIFECYCLE 30;