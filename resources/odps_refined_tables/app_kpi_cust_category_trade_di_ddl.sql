CREATE TABLE IF NOT EXISTS app_kpi_cust_category_trade_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示年月日',
	`cust_team` STRING COMMENT '客户团队类型，取值范围：集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户',
	`category` STRING COMMENT '品类，取值范围：鲜果、乳制品、其他',
	`origin_total_amt` DECIMAL(38,18) COMMENT '应付总金额',
	`real_total_amt` DECIMAL(38,18) COMMENT '实付总金额',
	`cust_cnt` BIGINT COMMENT '客户数',
	`cust_arpu` DECIMAL(38,18) COMMENT 'ARPU(应付总金额/客户数)',
	`order_cnt` BIGINT COMMENT '订单数',
	`order_avg` DECIMAL(38,18) COMMENT '订单均价(应付总金额/订单数)',
	`after_sale_noreceived_amt` DECIMAL(38,18) COMMENT '未到货售后总金额',
	`after_sale_rate` DECIMAL(38,18) COMMENT '退货率(未到货售后总金额/应付总金额)',
	`dire_origin_total_amt` DECIMAL(38,18) COMMENT '直发采购应付总金额',
	`delivery_amt` DECIMAL(38,18) COMMENT '运费（运费+超时加单费）',
	`timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送应付总金额'
) 
COMMENT '交易口径KPI指标日汇总表，按客户团队类型和品类维度统计的交易相关指标'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示年月日') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='交易口径KPI指标日汇总表，包含客户团队类型和品类维度的交易指标统计') 
LIFECYCLE 30;