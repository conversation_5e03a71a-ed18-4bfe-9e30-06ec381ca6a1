```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_log_purchase_order_time_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
  `is_purchase` STRING COMMENT '根据下单前行为划分：a未进入-未进入采购助手，b进入-下单前15分钟点击采购助手的商品（含常购商品和榜单推荐商品）',
  `type` STRING COMMENT '实验分组类型：V3-V3版本实验组，V4-V4版本实验组，对照组-对照组',
  `order_cnt` BIGINT COMMENT '下单数量，统计周期内的订单总数',
  `order_interval_minute` DECIMAL(38,18) COMMENT '下单间隔时长均值_分钟，用户从进入页面到下单的平均时间间隔',
  `order_interval_minute_75` DECIMAL(38,18) COMMENT '下单间隔时长75分位_分钟，75%用户的下单时间间隔小于等于此值',
  `order_interval_minute_mid` DECIMAL(38,18) COMMENT '下单间隔时长中位值_分钟，50%用户的下单时间间隔小于等于此值',
  `order_interval_minute_25` DECIMAL(38,18) COMMENT '下单间隔时长25分位_分钟，25%用户的下单时间间隔小于等于此值'
) 
COMMENT '下单时长效率分析表，用于分析不同实验分组下用户的购买行为时长特征，评估采购助手功能对下单效率的影响'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式为yyyyMMdd，表示数据所属的业务日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '下单时长效率分析表，统计不同实验分组的下单时长分布特征',
  'lifecycle' = '30'
);
```