CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_dlv_delivery_plan_di` (
  `date` STRING COMMENT '日期，格式为yyyyMMdd，表示业务日期',
  `cust_type` STRING COMMENT '客户类型，取值范围：ALL-全部客户类型，其他具体客户类型',
  `sku_type` STRING COMMENT '商品类型，取值范围：ALL-全部商品类型，代仓-代仓商品，其他具体商品类型',
  `category1` STRING COMMENT '一级分类，取值范围：ALL-全部分类，乳制品-乳制品分类，鲜果-鲜果分类，其他-其他分类',
  `origin_total_amt` DECIMAL(38,18) COMMENT '交易应付GMV，原始订单总金额',
  `after_sale_noreceived_amt` DECIMAL(38,18) COMMENT '未到货售后GMV，售后未收货金额',
  `pre_origin_total_amt` DECIMAL(38,18) COMMENT '计划履约次日订单应付GMV，次日订单原始金额',
  `pre_real_total_amt` DECIMAL(38,18) COMMENT '计划履约次日订单实付GMV，次日订单实际支付金额',
  `today_origin_total_amt` DECIMAL(38,18) COMMENT '计划履约当日订单应付GMV，当日订单原始金额',
  `history_origin_total_amt` DECIMAL(38,18) COMMENT '计划履约历史订单实付GMV，历史订单实际支付金额',
  `cust_cnt` BIGINT COMMENT '计划履约客户数，客户数量统计',
  `cost_amt` DECIMAL(38,18) COMMENT '计划履约成本，履约成本金额',
  `dlv_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付GMV，履约原始订单金额',
  `dlv_real_total_amt` DECIMAL(38,18) COMMENT '履约实付GMV，履约实际支付金额',
  `dlv_cost_amt` DECIMAL(38,18) COMMENT '计划履约成本，履约成本金额',
  `dlv_cust_cnt` BIGINT COMMENT '计划履约客户数，履约客户数量统计'
)
COMMENT '计划履约数据表，记录每日计划履约相关的GMV、成本、客户数等核心业务指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据分区日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '计划履约数据表，包含交易应付GMV、履约实付GMV、客户数、成本等核心业务指标',
  'lifecycle' = '30'
);