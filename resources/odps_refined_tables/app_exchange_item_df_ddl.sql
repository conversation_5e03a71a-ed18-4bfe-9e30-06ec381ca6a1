```sql
CREATE TABLE IF NOT EXISTS app_exchange_item_df(
    cust_id BIGINT COMMENT '客户ID，取值范围：3-26780',
    sku_id STRING COMMENT '商品SKU编号',
    type BIGINT COMMENT '行为类型：0-客户浏览未购买，1-同行购买客户未购买',
    order BIGINT COMMENT '排序序号，取值范围：1-30'
)
COMMENT '换购活动推荐商品表，用于存储客户在换购活动中被推荐的商品信息'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期')
STORED AS ALIORC
TBLPROPERTIES ('comment'='换购活动推荐商品表，包含客户ID、商品SKU、行为类型和排序信息')
LIFECYCLE 100;
```