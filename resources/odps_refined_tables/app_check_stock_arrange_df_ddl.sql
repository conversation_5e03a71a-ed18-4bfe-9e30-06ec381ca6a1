CREATE TABLE IF NOT EXISTS app_check_stock_arrange_df(
	purchase_no STRING COMMENT '采购编号，唯一标识一次采购业务',
	sku STRING COMMENT '商品SKU编码，唯一标识一个商品',
	arrange_count BIGINT COMMENT '已安排入库数量，整型数值',
	task_count BIGINT COMMENT '任务总数量，整型数值'
)
COMMENT '业务数据一致性校验-采购入库对账表，用于校验采购入库数据的完整性和一致性'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='采购入库对账校验表，通过对比安排数量和任务数量来确保数据一致性')
LIFECYCLE 30;