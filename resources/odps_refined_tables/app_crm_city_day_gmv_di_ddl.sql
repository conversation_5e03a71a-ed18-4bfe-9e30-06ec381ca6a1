CREATE TABLE IF NOT EXISTS app_crm_city_day_gmv_di(
	administrative_city STRING COMMENT '行政城市名称',
	team_tag BIGINT COMMENT '团队类型：0-全部团队，1-平台销售，2-大客户团队',
	total_gmv DECIMAL(38,18) COMMENT '总GMV（商品交易总额）',
	brand_gmv DECIMAL(38,18) COMMENT '自有品牌GMV',
	pull_new_amount BIGINT COMMENT '拉新客户数量',
	core_merchant_amount BIGINT COMMENT '核心客户数量',
	month_live_amount BIGINT COMMENT '月活跃客户总数',
	open_merchant_month_live BIGINT COMMENT '公海客户月活跃数',
	private_merchant_month_live BIGINT COMMENT '私海客户月活跃数',
	open_merchant_amount BIGINT COMMENT '公海客户总数',
	private_merchant_amount BIGINT COMMENT '私海客户总数',
	operate_merchant_num BIGINT COMMENT '倒闭客户数量',
	visit_num BIGINT COMMENT '总拜访次数',
	drop_in_visit_num BIGINT COMMENT '普通上门拜访次数',
	efficient_num BIGINT COMMENT '有效拜访次数',
	escort_num BIGINT COMMENT '陪访次数',
	performance DECIMAL(38,18) COMMENT '城市BD总绩效（已作废）',
	saas_total_gmv_amt DECIMAL(38,18) COMMENT 'SaaS商家GMV',
	saas_total_cust_cnt BIGINT COMMENT 'SaaS月活跃客户数',
	spu_average DECIMAL(38,18) COMMENT 'SPU均值',
	brand_cust_cnt BIGINT COMMENT '自营品牌下单客户数',
	fruit_gmv DECIMAL(38,18) COMMENT '鲜果品类GMV',
	fruit_cust_cnt BIGINT COMMENT '鲜果下单客户数',
	dairy_gmv DECIMAL(38,18) COMMENT '乳制品品类GMV',
	dairy_cust_cnt BIGINT COMMENT '乳制品下单客户数',
	non_dairy_gmv DECIMAL(38,18) COMMENT '非乳制品品类GMV',
	non_dairy_cust_cnt BIGINT COMMENT '非乳制品下单客户数',
	reward_gmv DECIMAL(38,18) COMMENT '安佳铁塔GMV',
	reward_cust_cnt BIGINT COMMENT '安佳铁塔下单客户数',
	worth_num BIGINT COMMENT '价值拜访次数',
	ordinary_pull_new_amount BIGINT COMMENT '普通拉新客户数量',
	private_merchant_effective_month_live BIGINT COMMENT '私海有效月活跃客户数',
	open_merchant_effective_month_live BIGINT COMMENT '公海有效月活跃客户数',
	agent_goods_gmv DECIMAL(38,18) COMMENT '代售品GMV',
	area STRING COMMENT '所属区域',
	delivery_gmv DECIMAL(38,18) COMMENT '配送服务GMV',
	agent_merchant_count BIGINT COMMENT '代售下单客户数'
) 
COMMENT '行政城市每日GMV统计表，按城市和团队类型统计各类业务指标数据'
PARTITIONED BY (ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期')
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='行政城市本月GMV表，包含各城市团队的业务指标统计') 
LIFECYCLE 30;