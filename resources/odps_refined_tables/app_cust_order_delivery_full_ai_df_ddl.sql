```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_cust_order_delivery_full_ai_df` (
  `cust_id` BIGINT COMMENT '客户ID，唯一标识客户',
  `cust_name` STRING COMMENT '客户名称',
  `cust_source` STRING COMMENT '客户来源，枚举：XM-SaaS客户,XM-小满客户',
  `cust_phone` STRING COMMENT '客户手机号码',
  `register_time` STRING COMMENT '客户注册时间，格式：yyyy-MM-dd HH:mm:ss',
  `register_province` STRING COMMENT '注册时所在省份',
  `register_city` STRING COMMENT '注册时所在城市',
  `register_area` STRING COMMENT '注册时所在区县',
  `register_address` STRING COMMENT '注册时详细地址',
  `cust_type` STRING COMMENT '客户类型，枚举：面包蛋糕,西餐,咖啡,甜品冰淇淋,茶饮,其他',
  `bd_id` BIGINT COMMENT 'BD ID，业务发展人员ID',
  `bd_name` STRING COMMENT 'BD姓名，业务发展人员姓名',
  `m1_name` STRING COMMENT 'M1管理者姓名，BD的直接上级',
  `m2_name` STRING COMMENT 'M2管理者姓名，M1的直接上级',
  `m3_name` STRING COMMENT 'M3管理者姓名，M2的直接上级',
  `zone_name` STRING COMMENT '销售区域名称',
  `cust_group` STRING COMMENT '客户分组类型，枚举：大客户,平台客户,批发客户',
  `brand_id` BIGINT COMMENT '品牌ID，原大客户ID，-1表示无品牌',
  `brand_name` STRING COMMENT '品牌名称',
  `order_no` STRING COMMENT '订单编号，唯一标识订单',
  `order_type` STRING COMMENT '订单类型，枚举：是-省心送,否-非省心送',
  `order_status` BIGINT COMMENT '订单状态，枚举：1-待支付,2-待配送,3-待收货,6-已收货,8-已退款,10-支付中断超时关闭,11-已撤销,12-待支付尾款,13-尾款支付超时,14-手动关闭,15-人工退款中',
  `order_item_status` BIGINT COMMENT '订单项状态，枚举：1-待支付,2-待配送,3-待收货,6-已收货,8-已退款,10-支付中断超时关闭,11-已撤销,12-待支付尾款,13-尾款支付超时,14-手动关闭,15-人工退款中',
  `order_time` DATETIME COMMENT '下单时间，格式：yyyy-MM-dd HH:mm:ss',
  `sku_id` STRING COMMENT '商品SKU ID，唯一标识商品',
  `sku_type` STRING COMMENT '商品类型，枚举：0-自营,1-全品类',
  `spu_id` BIGINT COMMENT '商品SPU ID，产品ID',
  `sku_no` STRING COMMENT '商品SKU编号',
  `spu_name` STRING COMMENT '商品名称',
  `category1` STRING COMMENT '商品一级类目',
  `category2` STRING COMMENT '商品二级类目',
  `category3` STRING COMMENT '商品三级类目',
  `category4` STRING COMMENT '商品四级类目',
  `origin_total_amt` DECIMAL(38,18) COMMENT '订单应付总金额',
  `real_total_amt` DECIMAL(38,18) COMMENT '订单实付总金额',
  `sku_cnt` BIGINT COMMENT '商品购买数量，不含赠品',
  `gift_cnt` BIGINT COMMENT '赠品数量',
  `delivery_date` DATETIME COMMENT '履约日期，格式：yyyy-MM-dd HH:mm:ss',
  `finished_date` DATETIME COMMENT '履约完成日期，格式：yyyy-MM-dd HH:mm:ss',
  `delivery_status` BIGINT COMMENT '履约状态，枚举：6-已完成',
  `delivery_sku_cnt` BIGINT COMMENT '本次履约商品数量',
  `delivery_total_sku_cnt` BIGINT COMMENT '累计履约商品总数量',
  `no_delivery_sku_cnt` BIGINT COMMENT '剩余待履约商品数量',
  `no_delivery_day_cnt` BIGINT COMMENT '剩余履约天数',
  `dlv_origin_total_amt` DECIMAL(38,18) COMMENT '履约应付总金额',
  `dlv_real_total_amt` DECIMAL(38,18) COMMENT '履约实付总金额',
  `dlv_sku_weight` DECIMAL(38,18) COMMENT '履约商品重量（kg）',
  `warehouse_no` BIGINT COMMENT '库存仓库ID',
  `warehouse_name` STRING COMMENT '库存仓库名称',
  `store_no` BIGINT COMMENT '城配仓库ID',
  `area_name` STRING COMMENT '城配仓库区域名称',
  `point_id` BIGINT COMMENT '配送点位ID',
  `delivery_path_id` BIGINT COMMENT '配送线路ID'
) 
COMMENT 'AI小助手底层数据表，包含客户信息、订单信息、商品信息、履约配送信息等完整数据'
PARTITIONED BY (
  `ds` STRING NOT NULL COMMENT '分区字段，格式：yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = 'AI小助手底层数据表，用于支持AI小助手的各种数据分析和推荐功能',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```