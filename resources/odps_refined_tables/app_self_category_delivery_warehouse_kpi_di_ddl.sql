CREATE TABLE IF NOT EXISTS app_self_category_delivery_warehouse_kpi_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
	`category` STRING COMMENT '商品品类：鲜果-新鲜水果，乳制品-奶制品，其他-其他商品类型',
	`origin_total_amt` DECIMAL(38,18) COMMENT '原始总金额，订单原始金额总计',
	`real_total_amt` DECIMAL(38,18) COMMENT '实际总金额，订单实际成交金额总计',
	`cost_amt` DECIMAL(38,18) COMMENT '成本金额，商品成本总计',
	`timing_origin_total_amt` DECIMAL(38,18) COMMENT '省心送原始总金额，省心送服务的原始订单金额',
	`timing_real_total_amt` DECIMAL(38,18) COMMENT '省心送实际总金额，省心送服务的实际成交金额',
	`cust_cnt` BIGINT COMMENT '客户数量，下单客户总数',
	`order_cnt` BIGINT COMMENT '订单数量，总订单数',
	`point_cnt` BIGINT COMMENT '点位数，配送点位总数',
	`day_point_cnt` BIGINT COMMENT '日均点位数，每日平均配送点位数量',
	`sku_cnt` BIGINT COMMENT 'SKU数量，商品SKU总数',
	`delivery_amt` DECIMAL(38,18) COMMENT '运费金额，配送费用总计',
	`after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后总金额，售后已收货的金额总计',
	`inventory_loss_amt` DECIMAL(38,18) COMMENT '盘亏总金额，库存盘点亏损金额',
	`inventory_profit_amt` DECIMAL(38,18) COMMENT '盘盈总金额，库存盘点盈余金额',
	`damage_amt` DECIMAL(38,18) COMMENT '货损总金额，商品损坏损失金额',
	`damage_rate` DECIMAL(38,18) COMMENT '货损占比，货损金额占总金额的比例',
	`storage_amt` DECIMAL(38,18) COMMENT '仓储成本，仓储费用总计',
	`arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本，干线运输费用总计',
	`deliver_amt` DECIMAL(38,18) COMMENT '配送成本，配送环节费用总计',
	`self_picked_amt` DECIMAL(38,18) COMMENT '采购自提成本，自提采购费用总计',
	`other_amt` DECIMAL(38,18) COMMENT '其他成本，其他未分类费用总计',
	`allocation_amt` DECIMAL(38,18) COMMENT '调拨成本，商品调拨运输费用总计'
) 
COMMENT '履约口径KPI指标日汇总表(自营)，包含自营商品的履约相关关键绩效指标数据，按日期和品类维度汇总'
PARTITIONED BY (`ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据所属日期') 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='自营商品履约KPI指标日度汇总表，涵盖销售、成本、配送、库存等全方位业务指标') 
LIFECYCLE 30;