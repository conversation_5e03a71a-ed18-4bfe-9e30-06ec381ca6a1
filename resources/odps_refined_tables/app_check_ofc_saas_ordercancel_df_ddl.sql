CREATE TABLE IF NOT EXISTS app_check_ofc_saas_ordercancel_df(
    order_no STRING COMMENT '订单号，唯一标识一个订单，如：OR168630787123815'
)
COMMENT '业务数据校验——SaaS-OFC未到货售后同步校验表，用于校验SaaS和OFC系统间未到货售后订单的同步情况'
PARTITIONED BY (
    ds STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据所属日期，如：20230610表示2023年6月10日'
)
STORED AS ALIORC
TBLPROPERTIES (
    'comment' = '业务数据校验表，专门用于SaaS-OFC系统间未到货售后订单的同步数据校验',
    'columnar.nested.type' = 'true'
)
LIFECYCLE 30;