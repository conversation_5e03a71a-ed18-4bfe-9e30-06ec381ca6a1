CREATE TABLE IF NOT EXISTS app_kpi_sku_manage_honour_di(
	`date` STRING COMMENT '日期，格式为yyyyMMdd，表示数据统计的日期',
	`manage_type` STRING COMMENT '商品经营类型：自营-平台自营商品，代仓-第三方仓库代发商品，代售-第三方销售商品',
	`origin_total_amt` DECIMAL(38,18) COMMENT '原始总金额(GMV)，包含所有订单的原始金额',
	`real_total_amt` DECIMAL(38,18) COMMENT '实际总金额，扣除优惠和退款后的实际成交金额',
	`origin_pay_rate` DECIMAL(38,18) COMMENT '应付毛利率，原始金额对应的毛利率',
	`real_pay_rate` DECIMAL(38,18) COMMENT '实付毛利率，实际金额对应的毛利率',
	`preferential_amt` DECIMAL(38,18) COMMENT '营销金额，各种优惠券、促销活动的总金额',
	`refund_amt` DECIMAL(38,18) COMMENT '售后退款总金额，所有售后退款的总金额',
	`after_sale_received_amt` DECIMAL(38,18) COMMENT '已到货售后总金额，商品已送达的售后订单金额',
	`after_sale_received_order_cnt` BIGINT COMMENT '已到货售后订单数，商品已送达的售后订单数量',
	`cust_cnt` BIGINT COMMENT '客户数，当日下单的独立客户数量',
	`cust_unit_amt` DECIMAL(38,18) COMMENT '客户单价，平均每个客户的消费金额',
	`order_cnt` BIGINT COMMENT '订单数，当日产生的总订单数量',
	`point_cnt` BIGINT COMMENT '点位数，当日有订单的配送点位数量',
	`point_out_rate` DECIMAL(38,18) COMMENT '外区点位占比，非本区域点位的比例',
	`point_in_rate` DECIMAL(38,18) COMMENT '内区点位占比，本区域点位的比例',
	`inventory_loss_amt` DECIMAL(38,18) COMMENT '盘亏总金额，库存盘点亏损的总金额',
	`inventory_profit_amt` DECIMAL(38,18) COMMENT '盘盈总金额，库存盘点盈余的总金额',
	`damage_amt` DECIMAL(38,18) COMMENT '货损总金额，商品损坏造成的损失金额',
	`damage_rate` DECIMAL(38,18) COMMENT '货损率，货损金额占总金额的比例',
	`replenish_out_amt` DECIMAL(38,18) COMMENT '补发出库总金额，补发商品的出库金额',
	`return_in_amt` DECIMAL(38,18) COMMENT '退货入库总金额，退货商品的入库金额',
	`sku_cnt` BIGINT COMMENT 'SKU数量，当日有销售的商品SKU数量',
	`storage_amt` DECIMAL(38,18) COMMENT '仓储成本，商品存储相关的费用',
	`arterial_roads_amt` DECIMAL(38,18) COMMENT '干线成本，主要运输线路的运输费用',
	`deliver_amt` DECIMAL(38,18) COMMENT '配送成本，最后一公里配送的费用',
	`self_picked_amt` DECIMAL(38,18) COMMENT '采购自提成本，自提点相关的运营成本',
	`other_amt` DECIMAL(38,18) COMMENT '其他成本，无法归类的其他运营成本',
	`point_day_cnt` BIGINT COMMENT '均日点位数，平均每日活跃的点位数量',
	`allocation_amt` DECIMAL(38,18) COMMENT '调拨成本，商品调拨运输的费用',
	`delivery_amt` DECIMAL(38,18) COMMENT '运费，总的运输费用'
) 
COMMENT '履约口径商品经营维度KPI指标日汇总表，包含商品经营相关的各项关键绩效指标'
PARTITIONED BY (ds STRING COMMENT '分区字段，yyyyMMdd格式，表示数据所属日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='商品经营KPI日度汇总表，按经营类型统计各项财务和运营指标') 
LIFECYCLE 30;