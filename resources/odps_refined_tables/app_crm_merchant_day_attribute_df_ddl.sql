```sql
CREATE TABLE IF NOT EXISTS summerfarm_tech.`app_crm_merchant_day_attribute_df` (
  `cust_id` BIGINT COMMENT '客户ID，唯一标识商户',
  `not_visited` BIGINT COMMENT '未拜访天数，从最后一次拜访开始计算的天数',
  `days_without_order` BIGINT COMMENT '未下单天数，从最后一次下单开始计算的天数',
  `merchant_lifecycle` BIGINT COMMENT '商户生命周期阶段：0-新注册，1-首单，2-非稳定，3-稳定',
  `order_frequency` BIGINT COMMENT '下单频率，统计周期内的下单次数',
  `timing_follow_type` BIGINT COMMENT '未完结的省心送标识：0-无省心送，1-有省心送',
  `order_cycle` BIGINT COMMENT '近三个月内平均下单周期（天）',
  `days_without_order_follow` BIGINT COMMENT '未下单天数（与跟进相关的计算）',
  `life_cycle` STRING COMMENT '新生命周期标签：N0-新注册，L3-流失风险，S1-稳定客户，A2-活跃客户等',
  `r_value` STRING COMMENT 'R价值标签：L-低价值，R1-R2-中价值，R3+-高价值',
  `f_value` STRING COMMENT 'F价值标签：L-低频，F1-F4-中高频',
  `m_value` STRING COMMENT 'M价值标签：L-低消费，M1-M4-中高消费',
  `days_not_logged_in` BIGINT COMMENT '未登录天数，从最后一次登录开始计算',
  `frequency` BIGINT COMMENT '近1年平均下单周期（天），超过60天按60天计算',
  `visit_count` BIGINT COMMENT '本月被拜访次数，统计当月内的拜访总次数'
)
COMMENT '商户属性表，包含商户的基本属性、生命周期状态、价值标签和业务行为指标'
PARTITIONED BY (
  `ds` STRING COMMENT '分区字段，格式为yyyyMMdd，表示数据日期'
)
STORED AS ALIORC
TBLPROPERTIES (
  'comment' = '商户属性表，用于存储商户的日常属性数据和业务指标',
  'columnar.nested.type' = 'true'
)
LIFECYCLE 30;
```