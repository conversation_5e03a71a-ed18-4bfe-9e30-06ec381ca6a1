"""
Main query API endpoint for handling user queries.
"""

import uuid
from datetime import datetime

import os
from flask import request, jsonify, Blueprint, session
import queue
import time

from src.services.agent.api_query_processor import (
    api_processor,
)  # Direct use of API processor
from src.services.agent.base_query_processor import QueryRequest
from src.services.auth.user_login_with_feishu import login_required, decode_jwt_token
from src.services.auth.user_session_service import user_session_service
from src.services.chatbot.history_service import save_user_message, check_chat_history_ownership, get_current_streaming_message_id
from src.services.chatbot.message_cleanup_service import message_cleanup_service
from src.repositories.chatbi.history import get_chat_history_by_id
from src.utils.logger import logger
from src.utils.user_utils import get_valid_user_email
from src.utils.conversation_context import conversation_context

# Create a Blueprint for query endpoints
# Using url_prefix='' to maintain original URL paths
query_bp = Blueprint("query", __name__, url_prefix="")


@query_bp.route("/query", methods=["POST"])
@login_required
def query_endpoint():
    """
    POST /query

    说明（已改为纯轮询模式）：
    - 不再返回SSE流。
    - 后端在收到请求后，立即保存用户消息，创建并启动后台AI任务，
      返回JSON，包含会话ID与预创建的assistant消息ID（chat_history_id）。
    - 前端拿到chat_history_id后，统一通过 /query/poll/<id> 进行轮询。
    """
    user_info = session.get("user_info")
    username = user_info.get("name")
    email = get_valid_user_email(user_info)
    job_title = user_info.get("job_title")

    if not username or not email:
        return jsonify({"error": "User information not found in session"}), 401

    data = request.get_json()
    if not data or "query" not in data:
        return jsonify({"error": "参数 'query' 不能为空"}), 400

    user_query = data["query"]
    images = data.get("images", [])  # 获取图片URL列表
    conversation_id = data.get(
        "conversation_id"
    )  # Get existing ID or None for new chatbot
    user_timestamp = int(datetime.now().timestamp() * 1000)
    is_retry = data.get("is_retry", False)
    agent = data.get("agent")  # 获取指定的agent名称（可选）

    # 无需验证agent参数，因为base_query_processor 会验证且默认会使用coordinator_bot
    default_agent_model_overrides = None
    deep_thinking_mode = False
    if agent and agent in ["deep_research_agent", "odps_autopilot_agent"]:
        # 设置默认agent模型覆盖，深度分析和ODPS自动驾驶使用anthropic/claude-sonnet-4，
        # UI上通过“深度分析”按钮来触发。
        default_agent_model_overrides = {
            "deep_research_agent": {
                "provider": "openrouter",
                "model": os.getenv("PROVIDER_OPENROUTER_CLAUDE_MODEL", "anthropic/claude-sonnet-4"),
            },
            "odps_autopilot_agent": {
                "provider": "openrouter",
                "model": os.getenv("PROVIDER_OPENROUTER_CLAUDE_MODEL", "anthropic/claude-sonnet-4"),
            }
        }
        deep_thinking_mode = True
        logger.info(f"用户 {username} ({email}) 使用指定的agent: {agent}")

    # 获取前端发送的用户信息（包括位置信息）
    frontend_user_info = data.get("user_info", {})

    # 将前端发送的位置信息合并到session中的用户信息
    if frontend_user_info.get("location"):
        user_info["location"] = frontend_user_info["location"]
        logger.info(f"从前端接收到位置信息: {frontend_user_info['location']}")

    # Generate a new conversation ID if one isn't provided
    if not conversation_id:
        conversation_id = str(uuid.uuid4())
        logger.info(f"后端生成新的conversation_id: {conversation_id}")
    else:
        logger.info(f"使用前端提供的conversation_id: {conversation_id}")

    # 设置conversation_id上下文，确保整个API处理链路中的日志都包含conversation_id
    conversation_context.set_conversation_id(conversation_id)

    # Log received parameters (history is no longer passed)
    logger.info(
        f"User: {username} ({job_title}) | Convo: {conversation_id} | Query: {user_query} | Images: {len(images)} | is_retry: {is_retry} | agent: {agent}"
    )

    # --- Save User Message ---
    # Save user message only if it's not a retry (retries reuse the original user message)
    if not is_retry:
        try:
            save_user_message(
                username=username,
                email=email,
                conversation_id=conversation_id,
                content=user_query,
                timestamp=user_timestamp,
                images=images,
            )
            logger.debug(f"User message saved for convo {conversation_id}")
        except Exception as e:
            logger.exception(f"Error saving user message for convo {conversation_id}: {e}")
            # Decide if we should proceed or return an error
            # For now, let's proceed but log the error

    # 从用户session获取access_token
    access_token = None
    jwt_token = request.cookies.get("jwt_token")
    if jwt_token:
        payload = decode_jwt_token(jwt_token)
        if payload and payload.get("session_id"):
            session_id = payload.get("session_id")
            user_session = user_session_service.get_session_by_id(session_id)
            if user_session:
                access_token = user_session.access_token

    try:
        # if deep_thinking_mode:
        #     logger.info(f"用户 {username} ({email}) 进入深度思考模式，给他加上深度思考的系统提示词")
        #     user_query = f"请优先使用ODPS来深度分析我的问题：\n\n{user_query}"
        if agent and agent == "general_chat_bot":
            user_query = f"请一定要使用工具来查询最新的信息并回答我的问题：{user_query}"

        # --- 改为后台执行并返回JSON ---
        # 1) 构建请求模型
        request_obj = QueryRequest(
            user_query=user_query,
            user_info=user_info,
            access_token=access_token,
            conversation_id=conversation_id,
            images=images,
            agent_model_overrides=default_agent_model_overrides,
            agent=agent,
        )

        # 2) 启动后台任务
        msg_queue: queue.Queue = queue.Queue()
        async_worker = api_processor.create_async_worker(request_obj, msg_queue)
        api_processor.thread_pool.submit(async_worker)

        # 3) 等待预创建的 chat_history_id（尽量保证立即可轮询）
        chat_history_id = None
        deadline = time.time() + 3.0  # 最多等待3秒
        while time.time() < deadline:
            try:
                msg = msg_queue.get(timeout=0.5)
                if isinstance(msg, dict) and msg.get("type") == "chat_history_id":
                    chat_history_id = msg.get("content")
                    break
                # 其余消息忽略（日志/心跳等）
            except queue.Empty:
                # 尝试从数据库补救（某些情况下队列消息可能稍晚到达）
                if not chat_history_id:
                    chat_history_id = get_current_streaming_message_id(username, email, conversation_id)
                    if chat_history_id:
                        break
                continue

        if not chat_history_id:
            # 兜底：再查一次数据库
            chat_history_id = get_current_streaming_message_id(username, email, conversation_id)

        if not chat_history_id:
            logger.warning(f"未能在超时时间内获取chat_history_id，conversation_id={conversation_id}")

        # 4) 返回JSON，前端以轮询方式获取内容
        return jsonify({
            "conversation_id": conversation_id,
            "chat_history_id": chat_history_id,
            "message": "processing started"
        })
    finally:
        # 确保在API请求结束时清理conversation_id上下文
        conversation_context.clear_conversation_id()


@query_bp.route("/query/poll/<int:message_id>", methods=["GET"])
@login_required
def poll_message_content(message_id):
    """
    轮询获取消息的最新内容和状态

    用于页面刷新或网络中断后恢复正在进行中的AI响应

    Args:
        message_id (int): chat_history表中的消息ID

    Returns:
        JSON: {
            "content": str,           # 消息内容
            "is_completed": bool,     # 是否已完成
            "last_updated": str,      # 最后更新时间
            "logs": str,              # 执行日志（可选）
            "agent": str,             # Agent名称（可选）
            "time_spend": int         # 耗时（可选）
        }
    """
    user_info = session.get("user_info")
    username = user_info.get("name")
    email = get_valid_user_email(user_info)

    if not username or not email:
        return jsonify({"error": "User information not found in session"}), 401

    try:
        # 验证消息所有权
        is_owner, message_exists = check_chat_history_ownership(username, email, message_id)

        if not message_exists:
            logger.warning(f"Message {message_id} not found for user {username}")
            return jsonify({"error": "消息不存在"}), 404

        if not is_owner:
            logger.warning(f"User {username} does not own message {message_id}")
            return jsonify({"error": "无权访问此消息"}), 403

        # 获取消息详情
        message_data = get_chat_history_by_id(message_id)
        if not message_data:
            return jsonify({"error": "无法获取消息详情"}), 500

        # 检查消息角色
        if message_data.get('role') != 'assistant':
            return jsonify({"error": "只能轮询AI助手消息"}), 400

        # 构建响应数据
        response_data = {
            "content": message_data.get('content', ''),
            "is_completed": message_data.get('is_in_process', 0) == 0,  # is_in_process=0表示已完成
            "last_updated": message_data.get('updated_at').isoformat() if message_data.get('updated_at') else None,
            "logs": message_data.get('logs', ''),
            "agent": message_data.get('agent', ''),
            "time_spend": message_data.get('time_spend'),
            "conversation_id": message_data.get('conversation_id')
        }

        logger.debug(f"Polling message {message_id}: completed={response_data['is_completed']}, content_length={len(response_data['content'])}")

        return jsonify(response_data)

    except Exception as e:
        logger.exception(f"Error polling message {message_id}: {e}", exc_info=True)
        return jsonify({"error": "服务器内部错误"}), 500


@query_bp.route("/query/health", methods=["GET"])
@login_required
def get_system_health():
    """
    获取系统健康状态

    返回正在处理中的消息统计和系统状态
    """
    try:
        health_info = message_cleanup_service.health_check()
        return jsonify(health_info)

    except Exception as e:
        logger.exception(f"获取系统健康状态失败: {e}", exc_info=True)
        return jsonify({"error": "获取系统状态失败"}), 500


@query_bp.route("/query/cleanup", methods=["POST"])
@login_required
def cleanup_stale_messages():
    """
    清理超时消息

    管理员接口，用于手动清理超时的流式消息
    """
    user_info = session.get("user_info")
    username = user_info.get("name")

    # 简单的权限检查（可以根据需要增强）
    if not username:
        return jsonify({"error": "用户信息不完整"}), 401

    try:
        # 执行清理任务
        stats = message_cleanup_service.cleanup_all_stale_messages()

        logger.info(f"用户 {username} 执行了消息清理任务: {stats}")

        return jsonify({
            "message": "清理任务完成",
            "stats": stats
        })

    except Exception as e:
        logger.exception(f"清理超时消息失败: {e}", exc_info=True)
        return jsonify({"error": "清理任务执行失败"}), 500


@query_bp.route("/query/force-cleanup/<int:message_id>", methods=["POST"])
@login_required
def force_cleanup_message(message_id):
    """
    强制清理指定消息

    管理员接口，用于强制清理指定的流式消息
    """
    user_info = session.get("user_info")
    username = user_info.get("name")
    email = get_valid_user_email(user_info)

    if not username or not email:
        return jsonify({"error": "用户信息不完整"}), 401

    try:
        data = request.get_json() or {}
        reason = data.get('reason', f'用户{username}手动清理')

        # 验证消息所有权
        is_owner, message_exists = check_chat_history_ownership(username, email, message_id)

        if not message_exists:
            return jsonify({"error": "消息不存在"}), 404

        if not is_owner:
            return jsonify({"error": "无权操作此消息"}), 403

        # 执行强制清理
        success = message_cleanup_service.force_cleanup_message(message_id, reason)

        if success:
            logger.info(f"用户 {username} 强制清理了消息 {message_id}: {reason}")
            return jsonify({"message": "消息清理成功"})
        else:
            return jsonify({"error": "消息清理失败"}), 500

    except Exception as e:
        logger.exception(f"强制清理消息 {message_id} 失败: {e}", exc_info=True)
        return jsonify({"error": "清理操作失败"}), 500
