{"cells": [{"cell_type": "code", "execution_count": null, "id": "49900ce6", "metadata": {}, "outputs": [], "source": ["from src.services.odps.odps_client import odps,get_odps_sql_result_as_df\n", "from datetime import datetime,timedelta\n", "\n", "tables = odps.list_tables(project=\"summerfarm_tech\", prefix=\"app_\")\n", "\n", "today = datetime.now()\n", "\n", "def fetch_table_ddl(table):\n", "    if table.last_data_modified_time < today - timedelta(days=30):\n", "        print(table.name, \"is older than 30 days\", table.last_data_modified_time)\n", "        return\n", "    if \"app_anchor_\" in table.name or \"_extra_h\" in table.name:\n", "        print(table.name, \"is anchor table or extra table，跳过\")\n", "        return\n", "    if table.name < \"app_scp_lt_change_record_df\":\n", "        print(table.name, \"这个表已经生成过了\")\n", "        return\n", "    print(table.name, table.last_data_modified_time)\n", "    partition_to_fetch_data=table.partitions.get_max_partition()\n", "    sql_to_fetch_head=f\"select * from {table.name} where {partition_to_fetch_data} limit 10000\"\n", "    head_data = None\n", "    print(\"sql_to_fetch_head:\", sql_to_fetch_head)\n", "    head_data = get_odps_sql_result_as_df(sql_to_fetch_head)\n", "    if head_data is None:\n", "        print(table.name, \"is empty\")\n", "        return\n", "    head_data_desc=head_data.describe().to_markdown()\n", "    head_data_json=head_data.head(10).astype(str).to_json(force_ascii=False)\n", "    table_ddl_content = f\"\"\"\n", "# {table.name}\n", "* comment: {table.comment}\n", "* last_data_modified_time: {table.last_data_modified_time}\n", "\n", "# schema:\n", "{table.get_ddl()}\n", "\n", "# head data:\n", "{head_data_json}\n", "\n", "# head data desc(generated by pandas.dataframe.describe()):\n", "{head_data_desc}\n", "    \"\"\".strip()\n", "    # save the content into file resources/odps_raw_tables/{table.name}.md\n", "    with open(f\"resources/odps_raw_tables/{table.name}.md\", \"w\") as f:\n", "        f.write(table_ddl_content)\n", "\n", "for table in tables:\n", "    try:\n", "        fetch_table_ddl(table)\n", "    except Exception as e:\n", "        print(table.name, \"fetch data failed\", e)"]}, {"cell_type": "code", "execution_count": 34, "id": "65507a7f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["开始处理表：app_cust_brand_alias_performance_mi开始处理表：app_dynamic_price_statistics_df\n", "\n", "开始处理表：app_sale_category_kpi_trade_di\n", "开始处理表：app_crm_bd_month_gmv_mi\n", "开始处理表：app_crm_wecom_workers_bd_df\n", "开始处理表：app_crm_cust_lifecycle_detail_df\n", "开始处理表：app_finance_timing_unfinish_df\n", "开始处理表：app_finance_saas_revenue_mi\n", "开始处理表：app_crm_comfort_send_will_finished_di\n", "开始处理表：app_saas_store_record_day_summary_di_tmp\n", "开始处理表：app_cust_lifecycle_summary_df\n", "开始处理表：app_saas_trade_detail_di\n", "开始处理表：app_saas_merchant_store_purchase_activity_detail_di\n", "开始处理表：app_saas_merchant_store_order_proportion_analysis_df\n", "开始处理表：app_log_mall_meeting_di\n", "开始处理表：app_timing_sku_top_di\n", "开始处理表：app_cust_category_performance_comm_mi_extra_h\n", "开始处理表：app_saas_order_profit_sharing_difference_di\n", "开始处理表：app_sku_sales_summary_df\n", "开始处理表：app_cust_brand_performance_wi\n", "开始处理表：app_crm_wecom_bd_summary_df\n", "开始处理表：app_xianmu_card_statistics_df\n", "开始处理表：app_crm_team_brandalias_performance_mi\n", "开始处理表：app_log_mall_banner_di\n", "开始处理表：app_cust_lifecycle_order_mi\n", "开始处理表：app_dlv_xianmu_saas_business_mi\n", "开始处理表：app_dlv_area_kpi_wi\n", "开始处理表：app_crm_cust_performance_detail_mi\n", "开始处理表：app_stc_warehouse_category_kpi_wi\n", "开始处理表：app_saas_brand_sku_trade_wi\n", "开始处理表：app_bd_target_achieve_rate_mi\n", "开始处理表：app_kpi_category_wholesale_di\n", "开始处理表：app_xianmu_sale_purchase_back_df\n", "开始处理表：app_cust_lifecycle_change_mi\n", "开始处理表：app_temporary_insurance_risk_df\n", "开始处理表：app_cust_brand_category_performance_di\n", "开始处理表：app_crm_area_performance_mi\n", "开始处理表：app_area_delivery_cost_mi\n", "开始处理表：app_crm_consign_order_detail_di\n", "开始处理表：app_cust_values_df\n", "开始处理表：app_cust_lifecycle_progress_period_mi\n", "开始处理表：app_dlv_deliver_timing_rate_month_df\n", "开始处理表：app_area_path_cost_di\n", "开始处理表：app_dlv_area_city_cust_sku_timing_di\n", "开始处理表：app_crm_city_district_day_gmv_di\n", "开始处理表：app_saas_order_after_sale_detail_summary_2_di\n", "开始处理表：app_area_honour_kpi_wi\n", "开始处理表：app_finance_recharge_record_details_di\n", "开始处理表：app_saas_order_after_sale_detail_summary_di\n", "开始处理表：app_operate_kpi_trade_wi\n", "开始处理表：app_saas_trade_di\n", "开始处理表：app_finance_saas_cost_mi\n", "开始处理表：app_crm_bd_performance_di\n", "开始处理表：app_crm_sku_performance_wi\n", "开始处理表：app_cust_mtd_category_comm_mi_extra_h\n", "开始处理表：app_log_channel_sku_di\n", "开始处理表：app_after_sale_delivery_sku_di\n", "开始处理表：app_m2_sales_performance_mi\n", "开始处理表：app_srm_supplier_consignment_warehouse_stock_detail_df\n", "开始处理表：app_kpi_operate_large_area_delivery_wi\n", "开始处理表：app_stock_sku_statistics_di\n", "开始处理表：app_spu_trade_selfowend_wi\n", "开始处理表：app_crm_merchant_sku_label_df\n", "开始处理表：app_self_cust_delivery_kpi_wi\n", "开始处理表：app_log_purchase_order_time_di\n", "开始处理表：app_warehouse_estimated_consumption_df\n", "开始处理表：app_cust_pb_score_comm_mi_extra_h\n", "开始处理表：app_log_mall_module_di\n", "开始处理表：app_crm_performance_mi\n", "开始处理表：app_pop_purchaser_supplier_final_di\n", "开始处理表：app_log_largearea_home_page_di\n", "开始处理表：app_sku_preferential_mi\n", "开始处理表：app_crm_team_performance_mi\n", "开始处理表：app_kpi_operate_large_category_delivery_di\n", "开始处理表：app_kpi_operate_large_category_delivery_wi\n", "开始处理表：app_wareshoue_sku_store_df_17\n", "开始处理表：app_log_largearea_home_page_wi\n", "开始处理表：app_cust_effective_mi\n", "开始处理表：app_kpi_cust_trade_mi\n", "开始处理表：app_crm_wecom_bd_communication_di\n", "开始处理表：app_stc_warehouse_sku_board_position_cost_di\n", "开始处理表：app_self_cust_delivery_kpi_di\n", "开始处理表：app_spu_trade_selfowend_di\n", "开始处理表：app_stock_dashboard_history_df\n", "开始处理表：app_kpi_operate_large_area_delivery_di\n", "开始处理表：app_saas_merchant_store_order_analysis_df\n", "开始处理表：app_cust_order_sku_di\n", "开始处理表：app_log_channel_sku_wi\n", "开始处理表：app_crm_city_month_gmv_mi\n", "开始处理表：app_crm_merchant_increment_label_di\n", "开始处理表：app_crm_sku_performance_di\n", "开始处理表：app_saas_trade_wi\n", "开始处理表：app_mkt_activity_order_di\n", "开始处理表：app_operate_kpi_trade_di\n", "开始处理表：app_warehouse_path_time_df\n", "开始处理表：app_saas_market_item_no_sale_detail_di\n", "开始处理表：app_area_honour_kpi_di\n", "开始处理表：app_log_mall_sku_di\n", "开始处理表：app_crm_merchant_area_type_top_di\n", "开始处理表：app_crm_risk_merchant_di\n", "开始处理表：app_mkt_order_preferential_roi_di\n", "开始处理表：app_dlv_area_sku_ue_mi\n", "开始处理表：app_saas_order_item_detail_summary_di\n", "开始处理表：app_external_product_df\n", "开始处理表：app_kpi_cust_sku_trade_mi\n", "开始处理表：app_saas_merchant_store_order_analysis_quarter_df\n", "开始处理表：app_cust_brand_category_performance_wi\n", "开始处理表：app_crm_bd_big_cust_month_gmv_mi\n", "开始处理表：app_category_cust_monitor_mi\n", "开始处理表：app_service_area_warehouse_cost_wi\n", "开始处理表：app_category_coupon_quota_reward_di\n", "开始处理表：app_log_mall_search_skey_di\n", "开始处理表：app_check_ofc_saas_order_df\n", "开始处理表：app_area_deliver_1di\n", "开始处理表：app_city_trade_selfowened_mi\n", "开始处理表：app_crm_chatbi_cust_visit_record_df\n", "开始处理表：app_kpi_category_wholesale_wi\n", "开始处理表：app_self_delivery_warehouse_kpi_mi\n", "开始处理表：app_crm_sku_bd_month_merchant_di\n", "开始处理表：app_crm_city_today_hour_gmv_di_extra_h\n", "开始处理表：app_platform_kpi_mi\n", "开始处理表：app_crm_team_bd_mi\n", "开始处理表：app_warehouse_sku_temporary_di\n", "开始处理表：app_saas_market_item_on_sale_sold_out_detail_di\n", "开始处理表：app_stc_damage_detail_report_df\n", "开始处理表：app_sku_log_view_mi\n", "开始处理表：app_crm_city_performance_di\n", "开始处理表：app_stc_warehouse_category_kpi_di\n", "开始处理表：app_dlv_area_kpi_di\n", "开始处理表：app_saas_product_movement_month_di\n", "开始处理表：app_sku_sale_statistics_df\n", "开始处理表：app_check_ofc_saas_ordercancel_df\n", "开始处理表：app_chatbi_supplier_order_analysis_df\n", "开始处理表：app_cust_city_order_category_mi\n", "开始处理表：app_cust_brand_performance_di\n", "开始处理表：app_finance_cash_after_sale_details_di\n", "开始处理表：app_crm_team_brand_performance_mi\n", "开始处理表：app_saas_pos_order_audit_wi\n", "开始处理表：app_chatbi_bd_hierarchy_df\n", "开始处理表：app_saas_merchant_store_purchase_di\n", "开始处理表：app_exchange_item_df\n", "开始处理表：app_history_price_log_df\n", "开始处理表：app_saas_order_detail_month_di\n", "开始处理表：app_saas_merchant_store_order_hysteresis_analysis_df\n", "开始处理表：app_sale_category_kpi_trade_wi\n", "开始处理表：app_check_xianmu_ofc_tms_order_df\n", "开始处理表：app_cust_order_cohort_mi\n", "开始处理表：app_check_saas_tms_order_df\n", "开始处理表：app_mkt_order_sku_preferential_mi\n", "开始处理表：app_saas_store_inventory_audit_di\n", "开始处理表：app_spu_delivery_selfowend_mi\n", "开始处理表：app_cust_order_delivery_full_ai_df\n", "开始处理表：app_xianmu_sale_order_item_df\n", "开始处理表：app_brand_cust_trade_delivery_after_sale_di\n", "开始处理表：app_stock_dashboard_history_di\n", "开始处理表：app_sku_order_load_time_di\n", "开始处理表：app_sku_sales_volume_di\n", "开始处理表：app_kpi_trade_di\n", "开始处理表：app_kpi_operate_delivery_di\n", "开始处理表：app_self_category_delivery_warehouse_kpi_di\n", "开始处理表：app_wms_warehouse_storage_statistics_di\n", "开始处理表：app_trd_self_brand_di\n", "开始处理表：app_service_kpi_di\n", "开始处理表：app_brand_trade_delivery_after_sale_wi\n", "开始处理表：app_cust_register_order_mi\n", "开始处理表：app_search_frontcat_mini_rank_df\n", "开始处理表：app_bms_purchase_stock_detail_df\n", "开始处理表：app_pcs_direct_purchase_kpi_wi\n", "开始处理表：app_crm_city_day_gmv_di_extra_h\n", "开始处理表：app_finance_terminal_quantity_di\n", "开始处理表：app_pcs_saas_purchase_detail_report_df\n", "开始处理表：app_chatbi_mall_cust_analytics_di\n", "开始处理表：app_sku_log_search_mi\n", "开始处理表：app_saas_brand_city_delivery_wi\n", "开始处理表：app_after_sale_delivery_detail_di\n", "开始处理表：app_kpi_wholesale_mi\n", "开始处理表：app_crm_team_bd_spu_wi\n", "开始处理表：app_delivery_cust_grade_mi\n", "开始处理表：app_largeg_area_sku_order_mi\n", "开始处理表：app_stc_kpi_di\n", "开始处理表：app_kpi_all_trade_mi\n", "开始处理表：app_estimated_sku_warehouse_record_df\n", "开始处理表：app_kpi_area_honour_di\n", "开始处理表：app_warehouse_category_supply_kpi_di\n", "开始处理表：app_area_warehouse_cost_mi\n", "开始处理表：app_stc_warehouse_kpi_di\n", "开始处理表：app_sku_cust_monitor_wi\n", "开始处理表：app_dlv_delivery_replace_di\n", "开始处理表：app_saas_merchant_store_order_proportion_analysis_week_df\n", "开始处理表：app_chatbi_after_sale_summary_df\n", "开始处理表：app_crm_bd_cust_task_detail_df\n", "开始处理表：app_category_cust_self_delivery_kpi_wi\n", "开始处理表：app_cust_year_bill_di\n", "开始处理表：app_expand_activity_products_df\n", "开始处理表：app_saas_merchant_store_item_order_analysis_quarter_df\n", "开始处理表：app_finance_cost_mi\n", "开始处理表：app_kpi_operate_category_delivery_di\n", "开始处理表：app_sale_large_area_kpi_trade_mi\n", "开始处理表：app_saas_replenishment_suggestion_infomation_df\n", "开始处理表：app_crm_comfort_send_7day_no_send_di\n", "开始处理表：app_m1_target_achieve_rate_mi\n", "开始处理表：app_dlv_delivery_plan_di\n", "开始处理表：app_kpi_cust_category_trade_mi\n", "开始处理表：app_pcs_batch_sku_in_bound_di\n", "开始处理表：app_crm_area_consign_performance_mi\n", "开始处理表：app_sale_large_area_category_kpi_trade_wi\n", "开始处理表：app_kpi_sku_manage_honour_di\n", "开始处理表：app_sale_city_kpi_trade_mi\n", "开始处理表：app_stc_warehouse_sku_turnover_di\n", "开始处理表：app_warehouse_sku_unsalable_sale_storage_di\n", "开始处理表：app_saas_goods_near_deadline_summary_di\n", "开始处理表：app_kpi_whole_delivery_di\n", "开始处理表：app_gorse_offline_recommend_df\n", "开始处理表：app_log_purchase_function_di\n", "开始处理表：app_city_delivery_selfowend_di\n", "开始处理表：app_finance_bill_after_sale_details_di\n", "开始处理表：app_city_delivery_selfowend_wi\n", "开始处理表：app_merchant_sku_order_data_df\n", "开始处理表：app_finance_store_mi\n", "开始处理表：app_kpi_whole_delivery_wi\n", "开始处理表：app_sku_cust_order_mi\n", "开始处理表：app_cust_lifecycle_distribution_df\n", "开始处理表：app_cust_label_summary_detail_df\n", "开始处理表：app_stc_timing_replenishment_df\n", "开始处理表：app_stc_location_use_di\n", "开始处理表：app_kpi_sku_manage_honour_wi\n", "开始处理表：app_saas_store_inventory_summary_day_di\n", "开始处理表：app_sale_large_area_category_kpi_trade_di\n", "开始处理表：app_xianmu_sale_purchase_change_order_df\n", "开始处理表：app_saas_goods_no_sale_summary_di\n", "开始处理表：app_saas_brand_label_df\n", "开始处理表：app_check_product_sku_df\n", "开始处理表：app_purchase_category_kpi_mi\n", "开始处理表：app_kpi_operate_category_delivery_wi\n", "开始处理表：app_big_cust_delivery_after_sale_di\n", "开始处理表：app_m1_sales_performance_mi\n", "开始处理表：app_crm_crm_city_today_gmv_di\n", "开始处理表：app_crm_merchant_day_attribute_df\n", "开始处理表：app_log_purchase_main_di\n", "开始处理表：app_category_cust_self_delivery_kpi_di\n", "开始处理表：app_finance_revenue_di\n", "开始处理表：app_cust_brand_alias_category_performance_mi\n", "开始处理表：app_dlv_delivery_replace_wi\n", "开始处理表：app_cust_label_summary_df\n", "开始处理表：app_srm_supplier_consignment_warehouse_stock_df\n", "开始处理表：app_bd_dlv_sku_comm_monthly_mi\n", "开始处理表：app_stc_warehouse_kpi_wi\n", "开始处理表：app_saas_merchant_store_order_analysis_week_df\n", "开始处理表：app_pcs_direct_category_warehouse_purchase_kpi_mi\n", "开始处理表：app_warehouse_category_supply_kpi_wi\n", "开始处理表：app_kpi_area_honour_wi\n", "开始处理表：app_stc_kpi_wi\n", "开始处理表：app_saas_store_record_day_summary_di\n", "开始处理表：app_stc_warehouse_sku_demage_di\n", "开始处理表：app_pcs_supplier_rebate_sku_cumulative_df\n", "开始处理表：app_crm_team_bd_spu_di\n", "开始处理表：app_bd_kpi_dlv_performance_di\n", "开始处理表：app_crm_bd_today_gmv_di\n", "开始处理表：app_mkt_activity_cust_delivery_df\n", "开始处理表：app_bd_mtd_comm_mi\n", "开始处理表：app_sale_kpi_trade_mi\n", "开始处理表：app_crm_bd_consign_performance_mi\n", "开始处理表：app_kpi_category_trade_mi\n", "开始处理表：app_crm_wecom_workers_m1_df\n", "开始处理表：app_finance_cash_revenue_details_di\n", "开始处理表：app_search_related_queries_df\n", "开始处理表：app_saas_brand_sku_delivery_wi\n", "开始处理表：app_pcs_direct_purchase_kpi_di\n", "开始处理表：app_scp_lt_change_record_df\n", "开始处理表：app_brand_trade_delivery_after_sale_di\n", "开始处理表：app_kpi_whole_trade_mi\n", "开始处理表：app_crm_wecom_conversation_visit_detail_di\n", "开始处理表：app_service_kpi_wi\n", "开始处理表：app_crm_comfort_send_finished_di\n", "开始处理表：app_area_nohonour_cost_mi\n", "开始处理表：app_saas_brand_log_di\n", "开始处理表：app_warehouse_cust_after_sale_di\n", "开始处理表：app_dlv_pop_buyer_supplier_final_di\n", "开始处理表：app_self_category_delivery_warehouse_kpi_wi\n", "开始处理表：app_kpi_operate_delivery_wi\n", "开始处理表：app_pcs_supplier_rebate_cumulative_df\n", "开始处理表：app_kpi_trade_wi\n", "开始处理表：app_cust_lifecycle_cohort_di\n", "开始处理表：app_pcs_saas_purchase_back_detail_report_df\n", "开始处理表：app_crm_city_consign_performance_di\n", "开始处理表：app_pcs_supplier_rebate_target_cumulative_df\n", "开始处理表：app_crm_merchant_day_label_df\n", "开始处理表：app_sku_area_cust_category_gross_margin_wi\n", "开始处理表：app_kpi_trade_mi\n", "开始处理表：app_self_category_delivery_warehouse_kpi_mi\n", "开始处理表：app_kpi_operate_delivery_mi\n", "开始处理表：app_merchant_pool_data_df\n", "开始处理表：app_pcs_supplier_in_bound_df\n", "开始处理表：app_crm_wechat_tag_group_di\n", "开始处理表：app_area_nohonour_cost_wi\n", "开始处理表：app_saas_product_sales_overview_day_di\n", "开始处理表：app_kpi_whole_trade_wi\n", "开始处理表：app_service_kpi_mi\n", "开始处理表：app_crm_bd_today_hour_gmv_di_extra_h\n", "开始处理表：app_cust_register_order_di\n", "开始处理表：app_log_area_cust_di\n", "开始处理表：app_stc_timing_order_sku_bd_df\n", "开始处理表：app_stock_dashboard_future_df\n", "开始处理表：app_saas_brand_sku_delivery_mi\n", "开始处理表：app_finance_store_record_details_di\n", "开始处理表：app_saas_brand_health_mi\n", "开始处理表：app_cust_city_order_nextmonth_cohort_mi\n", "开始处理表：app_saas_self_goods_cost_price_df\n", "开始处理表：app_largearea_sku_adjust_price_df\n", "开始处理表：app_purchase_list_recommend_df\n", "开始处理表：app_kpi_category_trade_wi\n", "开始处理表：app_finance_other_cost_details_di\n", "开始处理表：app_dlv_delivery_brand_replace_di\n", "开始处理表：app_dlv_area_city_cust_sku_deliver_trade_di\n", "开始处理表：app_sale_kpi_trade_wi\n", "开始处理表：app_sku_sales_ranking_list_df\n", "开始处理表：app_dlv_deliver_timing_rate_df\n", "开始处理表：app_kpi_wholesale_di\n", "开始处理表：app_log_module_di\n", "开始处理表：app_cust_performance_comm_mi_extra_h\n", "开始处理表：app_saas_merchant_store_item_order_analysis_month_df\n", "开始处理表：app_crm_merchant_today_gmv_di\n", "开始处理表：app_warehouse_sku_health_df\n", "开始处理表：app_warehouse_category_supply_kpi_mi\n", "开始处理表：app_kpi_area_honour_mi\n", "开始处理表：app_kpi_all_trade_di\n", "开始处理表：app_xianmu_sale_purchase_item_change_order_df\n", "开始处理表：app_stc_kpi_mi\n", "开始处理表：app_stc_sku_temporary_damage_delivery_di\n", "开始处理表：app_stc_warehouse_kpi_mi\n", "开始处理表：app_area_warehouse_cost_di\n", "开始处理表：app_pcs_direct_category_warehouse_purchase_kpi_wi\n", "开始处理表：app_saas_purchases_outbound_detail_aggregate_offline_df\n", "开始处理表：app_dlv_delivery_replace_mi\n", "开始处理表：app_cust_brand_alias_category_performance_wi\n", "开始处理表：app_saas_bill_supplier_direct_assign_summary_di\n", "开始处理表：app_saas_order_statement_summary_di\n", "开始处理表：app_sale_large_area_kpi_trade_di\n", "开始处理表：app_kpi_operate_category_delivery_mi\n", "开始处理表：app_finance_cost_di\n", "开始处理表：app_merchant_tags_pool_df\n", "开始处理表：app_merchant_sku_tags_pool_df\n", "开始处理表：app_cust_grade_df\n", "开始处理表：app_consignment_not_warehouse_goods_grade_day_di\n", "开始处理表：app_cust_city_order_cohort_mi\n", "开始处理表：app_saas_product_detail_sales_di\n", "开始处理表：app_saas_merchant_store_purchase_day_di\n", "开始处理表：app_purchase_category_kpi_wi\n", "开始处理表：app_kpi_cust_category_trade_di\n", "开始处理表：app_xianmu_sale_purchase_back_item_df\n", "开始处理表：app_crm_area_consign_performance_di\n", "开始处理表：app_crm_city_day_gmv_di\n", "开始处理表：app_kpi_sku_manage_honour_mi\n", "开始处理表：app_crm_sku_area_month_merchant_di\n", "开始处理表：app_crm_merchant_future_day_gmv_di\n", "开始处理表：app_sale_city_kpi_trade_di\n", "开始处理表：app_crm_wecom_conversation_detail_di\n", "开始处理表：app_cust_values_analysis_df\n", "开始处理表：app_kpi_whole_delivery_mi\n", "开始处理表：app_sku_cust_order_wi\n", "开始处理表：app_city_delivery_selfowend_mi\n", "开始处理表：app_warehouse_sku_unsalable_di\n", "开始处理表：app_history_cust_category_performance_mi\n", "开始处理表：app_saas_market_item_sales_summary_di\n", "开始处理表：app_mkt_deliver_sku_preferential_mi\n", "开始处理表：app_log_search_transform_di\n", "开始处理表：app_bd_cust_effective_mi\n", "开始处理表：app_finance_store_di\n", "开始处理表：app_xianmu_search_front_category_prediction_df\n", "开始处理表：app_crm_sku_month_gmv_di\n", "开始处理表：app_sale_city_kpi_trade_wi\n", "开始处理表：app_saas_stock_turnover_summary_di\n", "开始处理表：app_sale_large_area_category_kpi_trade_mi\n", "开始处理表：app_purchase_category_kpi_di\n", "开始处理表：app_kpi_cust_category_trade_wi\n", "开始处理表：app_saas_bill_agent_warehouse_summary_di\n", "开始处理表：app_bms_proxy_stock_change_statistics_di\n", "开始处理表：app_log_mall_cms_di\n", "开始处理表：app_sale_large_area_kpi_trade_wi\n", "开始处理表：app_crm_chatbi_cust_orders_2yrs_df\n", "开始处理表：app_saas_merchant_store_order_analysis_month_df\n", "开始处理表：app_cust_brand_alias_category_performance_di\n", "开始处理表：app_finance_revenue_mi\n", "开始处理表：app_category_cust_self_delivery_kpi_mi\n", "开始处理表：app_area_warehouse_cost_wi\n", "开始处理表：app_pcs_direct_category_warehouse_purchase_kpi_di\n", "开始处理表：app_sku_cust_monitor_mi\n", "开始处理表：app_sku_ue_mi\n", "开始处理表：app_crm_merchant_today_gmv_di_extra_h\n", "开始处理表：app_crm_wecom_state_df\n", "开始处理表：app_kpi_all_trade_wi\n", "开始处理表：app_largeg_area_sku_order_wi\n", "开始处理表：app_crm_merchant_mall_search_top_di\n", "开始处理表：app_crm_team_bd_spu_mi\n", "开始处理表：app_saas_merchant_store_item_order_analysis_df\n", "开始处理表：app_sku_cust_delivery_mi\n", "开始处理表：app_inventory_consignment_in_warehouse_di\n", "开始处理表：app_kpi_wholesale_wi\n", "开始处理表：app_saas_purchases_inbound_detail_aggregate_offline_df\n", "开始处理表：app_saas_brand_city_delivery_mi\n", "开始处理表：app_sale_kpi_trade_di\n", "开始处理表：app_mkt_deliver_preferential_roi_di\n", "开始处理表：app_log_search_di\n", "开始处理表：app_stc_warehouse_category_allocate_mi\n", "开始处理表：app_crm_bd_consign_performance_di\n", "开始处理表：app_finance_saas_revenue_details_di\n", "开始处理表：app_cust_mtd_performance_mi\n", "开始处理表：app_deliver_gross_margin_di\n", "开始处理表：app_kpi_category_trade_di\n", "开始处理表：app_service_area_deliver_cost_wi\n", "开始处理表：app_saas_product_sales_overview_week_di\n", "开始处理表：app_pcs_direct_purchase_kpi_mi\n", "开始处理表：app_after_sale_rate_di\n", "开始处理表：app_sku_delivery_mi\n", "开始处理表：app_crm_cust_label_df\n", "开始处理表：app_log_recommend_transform_di\n", "开始处理表：app_pop_biaoguo_products_df\n", "开始处理表：app_crm_wecom_user_summary_df\n", "开始处理表：app_crm_bd_day_gmv_di\n", "开始处理表：app_sku_marketing_mi\n", "开始处理表：app_brand_trade_delivery_after_sale_mi\n", "开始处理表：app_kpi_whole_trade_di\n", "开始处理表：app_area_nohonour_cost_di\n", "开始处理表：app_log_puchase_transfer_di\n", "开始处理表：app_sku_order_load_time_hour_di\n", "开始处理表：app_cust_label_df\n", "开始处理表：app_m1_performance_comm_mi_extra_h\n", "开始处理表：app_saas_supplier_metrics_summary_di\n", "开始处理表：app_follow_up_relation_release_detail_df\n", "开始处理表：app_sku_area_cust_category_gross_margin_di\n", "开始处理表：app_crm_deliver_day_di_extra_h\n", "开始处理表：app_sku_label_di\n", "开始处理表：app_crm_city_consign_performance_mi\n", "开始处理表：app_check_xianmu_ofc_tms_ordercancel_df\n", "开始处理表：app_service_warehouse_cost_wi\n", "开始处理表：app_spu_delivery_selfowend_wi\n", "开始处理表：app_cust_brand_alias_performance_di\n", "开始处理表：app_mkt_order_coupon_preferential_mi\n", "开始处理表：app_dlv_wholesale_delivery_df\n", "开始处理表：app_log_cust_route_di\n", "开始处理表：app_sale_category_kpi_trade_mi\n", "开始处理表：app_cust_order_cohort_wi\n", "开始处理表：app_saas_tenant_metrics_summary_di\n", "开始处理表：app_delivery_after_sale_cust_gmv_di\n", "开始处理表：app_crm_bd_month_gmv_di\n", "开始处理表：app_saas_merchant_store_order_proportion_analysis_quarter_df\n", "开始处理表：app_xianmu_sale_order_df\n", "开始处理表：app_finance_saas_revenue_di\n", "开始处理表：app_crm_team_brand_performance_wi\n", "开始处理表：app_warehouse_sku_monitor_df\n", "开始处理表：app_pcs_direct_warehouse_category_price_di\n", "开始处理表：app_cust_sku_area_value_mi\n", "开始处理表：app_saas_pos_order_item_detail_di\n", "开始处理表：app_log_mall_whole_di\n", "开始处理表：app_cust_order_nextmonth_cohort_mi\n", "开始处理表：app_crm_team_brandalias_performance_di\n", "开始处理表：app_after_sale_supplier_detail_di\n", "开始处理表：app_log_mall_cust_category_di\n", "开始处理表：app_dlv_xianmu_saas_business_di\n", "开始处理表：app_commonly_recommended_df\n", "开始处理表：app_saas_goods_expiration_summary_di\n", "开始处理表：app_platform_kpi_wi\n", "开始处理表：app_prd_sku_label_df\n", "开始处理表：app_city_trade_selfowened_wi\n", "开始处理表：app_crm_crm_city_today_gmv_di_extra_h\n", "开始处理表：app_kpi_category_wholesale_mi\n", "开始处理表：app_self_delivery_warehouse_kpi_wi\n", "开始处理表：app_category_cust_monitor_wi\n", "开始处理表：app_wms_warehouse_sku_after_sale_rate_statistics_df\n", "开始处理表：app_saas_product_sales_overview_month_di\n", "开始处理表：app_stc_warehouse_sku_temporary_di\n", "开始处理表：app_area_delivery_cost_di\n", "开始处理表：app_crm_merchant_future_day_gmv_di_extra_h\n", "开始处理表：app_crm_area_performance_di\n", "开始处理表：app_cust_brand_category_performance_mi\n", "开始处理表：app_saas_product_movement_day_di\n", "开始处理表：app_saas_merchant_store_purchase_month_di\n", "开始处理表：app_warehouse_in_out_di\n", "开始处理表：app_finance_saas_cost_di\n", "开始处理表：app_saas_trade_mi\n", "开始处理表：app_pcs_successful_supplier_in_bound_df\n", "开始处理表：app_log_channel_sku_mi\n", "开始处理表：app_recommendation_final\n", "开始处理表：app_crm_bd_performance_mi\n", "开始处理表：app_chatbi_search_analysis_df\n", "开始处理表：app_saas_merchant_store_purchase_week_di\n", "开始处理表：app_replenishment_sku_cycle_statistics_df\n", "开始处理表：app_crm_cust_performance_mi\n", "开始处理表：app_crm_label_pool_df\n", "开始处理表：app_cust_lifecycle_progress_mi\n", "开始处理表：app_crm_merchant_day_gmv_di\n", "开始处理表：app_finance_bill_revenue_details_di\n", "开始处理表：app_log_largearea_home_page_mi\n", "开始处理表：app_bd_target_review_mi\n", "开始处理表：app_saas_safe_stock_warning_day_df\n", "开始处理表：app_kpi_cust_trade_wi\n", "开始处理表：app_crm_team_performance_di\n", "开始处理表：app_kpi_operate_large_category_delivery_mi\n", "开始处理表：app_crm_team_performance_wi\n", "开始处理表：app_crm_saas_order_cust_df_extra_h\n", "开始处理表：app_kpi_cust_trade_di\n", "开始处理表：app_dlv_pop_supplier_final_di\n", "开始处理表：app_crm_merchant_day_gmv_di_extra_h\n", "开始处理表：app_check_stock_arrange_df\n", "开始处理表：app_stock_daily_change_vs_biz_di\n", "开始处理表：app_chatbi_cust_orders_df\n", "开始处理表：app_stc_warehouse_sku_initquantity_22_di\n", "开始处理表：app_saas_merchant_store_item_order_analysis_week_df\n", "开始处理表：app_crm_wecom_bd_task_df\n", "开始处理表：app_spu_trade_selfowend_mi\n", "开始处理表：app_saas_store_maturity_warning_day_di\n", "开始处理表：app_self_cust_delivery_kpi_mi\n", "开始处理表：app_kpi_operate_large_area_delivery_mi\n", "开始处理表：app_trd_city_sku_price_df\n", "开始处理表：app_saas_order_after_sale_inverted_summary_di\n", "开始处理表：app_crm_sku_performance_mi\n", "开始处理表：app_saas_merchant_store_detail_purchase_di\n", "开始处理表：app_sku_order_coupon_mi\n", "开始处理表：app_delivery_supplier_detail_di\n", "开始处理表：app_area_honour_kpi_mi\n", "开始处理表：app_operate_kpi_trade_mi\n", "开始处理表：app_saas_proxy_warehousing_order_batch_cost_data_df\n", "开始处理表：app_saas_order_item_statement_analysis_df\n", "开始处理表：app_crm_saas_order_item_df_extra_h\n", "开始处理表：app_log_recommend_transform_cust_di\n", "开始处理表：app_saas_shelf_life_warning_day_di\n", "开始处理表：app_area_delivery_cost_wi\n", "开始处理表：app_dlv_entrust_sku_amount_di\n", "开始处理表：app_financial_accounts_order_item_di\n", "开始处理表：app_crm_chatbi_cust_info_df\n", "开始处理表：app_self_delivery_warehouse_kpi_di\n", "开始处理表：app_saas_merchant_store_order_proportion_analysis_month_df\n", "开始处理表：app_city_trade_selfowened_di\n", "开始处理表：app_platform_kpi_di\n", "开始处理表：app_saas_brand_sku_trade_mi\n", "开始处理表：app_saas_product_movement_di\n", "开始处理表：app_stc_warehouse_category_kpi_mi\n", "开始处理表：app_crm_city_performance_mi\n", "开始处理表：app_damage_sale_ratio_report_df\n", "开始处理表：app_crm_city_district_day_gmv_di_extra_h\n", "开始处理表：app_saas_merchant_store_purchase_report_di\n", "开始处理表：app_dlv_xianmu_saas_business_wi\n", "开始处理表：app_dlv_area_kpi_mi\n", "开始处理表：app_crm_team_brandalias_performance_wi\n", "开始处理表：app_crm_task_cust_status_df\n", "开始处理表：app_crm_crm_new_customers_month_di\n", "开始处理表：app_service_area_trunk_cost_wi\n", "开始处理表：app_finance_saas_after_sale_details_di\n", "开始处理表：app_stc_warehouse_sku_fruit_quantity_df\n", "开始处理表：app_cust_brand_performance_mi\n", "开始处理表：app_saas_product_sales_overview_di\n", "开始处理表：app_saas_product_movement_week_di\n", "开始处理表：app_crm_team_brand_performance_di\n", "开始处理表：app_pms_sku_purchse_price_df\n", "开始处理表：app_xianmu_search_category_prediction_df\n", "开始处理表：app_stc_warehouse_batch_sku_cost_df\n", "开始处理表：app_crm_bd_day_gmv_di_extra_h\n", "开始处理表：app_crm_follow_record_detail_df\n", "开始处理表：app_log_search_detail_di\n", "开始处理表：app_crm_merchant_month_gmv_mi\n", "开始处理表：app_spu_delivery_selfowend_di\n", "开始处理表：app_cust_brand_alias_performance_wi\n", "开始处理表：app_log_recommend_detail_di\n"]}], "source": ["app_chatbi_bd_hierarchy_df_ddl = \"\"\n", "with open(\"resources/tables_ddl/app_chatbi_bd_hierarchy_df_ddl.sql\", \"r\") as f:\n", "    app_chatbi_bd_hierarchy_df_ddl = f.read()\n", "\n", "system_prompt = f\"\"\"你是一位专业的数据分析师，拥有超过10年的数据建模经验。\n", "你的任务是根据用户输入的ODPS表名，以及表的DDL、表的最大分区的数据之Top5行记录、表的最大分区的数据之describe()信息，优化表的DDL语句。\n", "请确保优化后的DDL语句能够满足以下要求：\n", "1. **保持表的名称不变**\n", "2. **保持表的Schema不变**\n", "3. **保持表的分区字段不变**\n", "4. 使用中文补充表的注释、字段的注释、字段的类型说明特别是对于日期类型，要说明是年月日还是年月日时分秒\n", "5. 补充枚举类型字段的取值范围\n", "\n", "产出的结果可高度参考以下示例：\n", "{app_chatbi_bd_hierarchy_df_ddl}\n", "\n", "请注意，不需要说明你是如何改写的，只需要把修改后的DDL语句输出即可。\n", "\"\"\"\n", "\n", "\n", "def call_openai_api_to_refine_ddl(\n", "    table_name: str, system_prompt: str = system_prompt\n", ") -> str:\n", "    print(f\"开始处理表：{table_name}\")\n", "    try:\n", "        table_ddl = \"\"\n", "        with open(f\"resources/odps_raw_tables/{table_name}.md\", \"r\") as f:\n", "            table_ddl = f.read()\n", "        import openai\n", "        import os\n", "        api_key=os.getenv(\"OPENAI_API_KEY\")\n", "        api_base=os.getenv(\"OPENAI_API_BASE\")\n", "        # 创建 OpenAI 客户端\n", "        client = openai.OpenAI(api_key=api_key, base_url=api_base)\n", "\n", "        # 构建消息\n", "        messages = [\n", "            {\"role\": \"system\", \"content\": system_prompt},\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": f\"请优化以下表的DDL：\\n\\n```\\n{table_ddl}\\n```\",\n", "            },\n", "        ]\n", "\n", "        # 调用 API\n", "        response = client.chat.completions.create(\n", "            model=\"deepseek-v3-1\",\n", "            messages=messages,\n", "            temperature=0,\n", "            max_tokens=8000,\n", "            timeout=600,\n", "        )\n", "\n", "        # 提取结果\n", "        enhanced_prompt = response.choices[0].message.content.strip()\n", "\n", "        if not enhanced_prompt:\n", "            raise Exception(\"API 返回空结果\")\n", "\n", "        # write result into file:\n", "        with open(f\"resources/odps_refined_tables/{table_name}_ddl.sql\", \"w\") as f:\n", "            f.write(enhanced_prompt)\n", "        \n", "        return enhanced_prompt\n", "\n", "    except Exception as e:\n", "        print(f\"OpenAI API 调用失败: {e}\")\n", "        raise Exception(f\"AI 服务调用失败: {str(e)}\")\n", "\n", "# 遍历所有表，并发的调用，5个并发：\n", "from concurrent.futures import ThreadPoolExecutor\n", "import os\n", "with ThreadPoolExecutor(max_workers=5) as executor:\n", "    for table_name in os.listdir(\"resources/odps_raw_tables\"):\n", "        if table_name.endswith(\".md\"):\n", "            executor.submit(call_openai_api_to_refine_ddl, table_name.replace(\".md\", \"\"))"]}, {"cell_type": "code", "execution_count": null, "id": "340f251c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.6"}}, "nbformat": 4, "nbformat_minor": 5}